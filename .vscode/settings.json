{"editor.formatOnSave": true, "editor.defaultFormatter": "dprint.dprint", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "typescript"], "[typescript]": {"editor.defaultFormatter": "dprint.dprint"}, "[javascript]": {"editor.defaultFormatter": "dprint.dprint"}, "[json]": {"editor.defaultFormatter": "dprint.dprint"}, "[svelte]": {"editor.defaultFormatter": "dprint.dprint"}, "[markdown]": {"editor.defaultFormatter": "dprint.dprint"}, "[css]": {"editor.defaultFormatter": "dprint.dprint"}}
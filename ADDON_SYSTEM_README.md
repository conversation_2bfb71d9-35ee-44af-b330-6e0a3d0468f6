# ระบบ Addons สำหรับการเช่าซื้อระบบเสริม

ระบบนี้ช่วยให้ผู้ใช้สามารถเช่าและใช้งานระบบเสริมต่างๆ เช่น ระบบข่าวสาร, บล็อก, และนิยาย

## 🚀 ฟีเจอร์หลัก

### ระบบ Addons

- **ระบบข่าวสาร** (299 บาท/เดือน)
  - สร้างและจัดการข่าวสาร
  - จัดหมวดหมู่ข่าว
  - SEO สำหรับข่าวสาร
  - แสดงผลหน้าเว็บ

- **ระบบบล็อก** (399 บาท/เดือน)
  - เขียนบทความ
  - ระบบแท็ก
  - ความคิดเห็น
  - แชร์โซเชียล

- **ระบบนิยาย** (599 บาท/เดือน)
  - เขียนนิยาย
  - แบ่งตอน
  - ระบบจองอ่าน
  - ระบบเรตติ้ง

### การจัดการ Addons

- เช่าระบบเสริม
- เปิด/ปิดใช้งาน
- ยกเลิกการเช่า
- ติดตามการใช้งาน

## 📁 โครงสร้างไฟล์

### Backend (Elysia)

```
src/modules/addon/
├── index.ts              # Routes สำหรับ addon
├── addon.service.ts      # Business logic
└── addon.model.ts        # Mongoose models

src/modules/content/
├── index.ts              # Routes สำหรับ content
├── content.model.ts      # Mongoose models
├── news.service.ts       # News service
├── blog.service.ts       # Blog service
└── novel.service.ts      # Novel service

src/core/guards/
└── addon.guard.ts        # Guard สำหรับตรวจสอบ addon
```

### Frontend (SvelteKit)

```
dashboard-sveltekit/src/routes/(protected)/(site)/dashboard/[siteId]/
├── addons/
│   ├── +page.server.ts   # Server load สำหรับ addons
│   └── +page.svelte      # หน้าจัดการ addons
└── contents/
    ├── news/             # ระบบข่าวสาร
    ├── blogs/            # ระบบบล็อก
    └── novels/           # ระบบนิยาย

dashboard-sveltekit/src/routes/api/sites/[siteId]/
├── addons/               # API สำหรับ addons
└── contents/             # API สำหรับ contents
```

## 🔧 การติดตั้งและใช้งาน

### 1. Backend Setup

#### ติดตั้ง Dependencies

```bash
cd backend-elysia
bun install
```

#### เพิ่ม Environment Variables

```env
BACKEND_URL=http://localhost:3000
MONGODB_URI=mongodb://localhost:27017/your-database
```

#### รัน Backend

```bash
bun run dev
```

### 2. Frontend Setup

#### ติดตั้ง Dependencies

```bash
cd dashboard-sveltekit
bun install
```

#### เพิ่ม Environment Variables

```env
BACKEND_URL=http://localhost:3000
```

#### รัน Frontend

```bash
bun run dev
```

## 📊 Database Schema

### Addon Rentals

```javascript
{
  siteId: String,           // ID ของเว็บไซต์
  addonId: String,          // ID ของ addon (news, blog, novel)
  addonName: String,        // ชื่อ addon
  price: Number,            // ราคาเช่า
  status: String,           // สถานะ (active, cancelled, expired)
  isActive: Boolean,        // เปิดใช้งานหรือไม่
  rentedBy: String,         // ID ผู้เช่า
  createdAt: Date,          // วันที่เช่า
  expiresAt: Date           // วันหมดอายุ
}
```

### Content Models

- **News**: ข่าวสาร
- **NewsCategory**: หมวดหมู่ข่าว
- **Blog**: บล็อก
- **BlogComment**: ความคิดเห็นบล็อก
- **Novel**: นิยาย
- **NovelChapter**: ตอนนิยาย
- **NovelRating**: การให้คะแนนนิยาย

## 🔐 การรักษาความปลอดภัย

### Addon Guard

ระบบตรวจสอบว่าผู้ใช้ได้เช่าและเปิดใช้งาน addon หรือไม่ก่อนเข้าถึงฟีเจอร์

```typescript
// ตัวอย่างการใช้งาน
.use(addonGuard('news'))  // ตรวจสอบระบบข่าวสาร
```

### Site Access Control

ตรวจสอบสิทธิ์เข้าถึงเว็บไซต์ของผู้ใช้

## 🎯 API Endpoints

### Addon Management

```
GET    /v1/addons/sites/:siteId                    # ดึงรายการ addons
POST   /v1/addons/sites/:siteId/:addonId/rent      # เช่า addon
POST   /v1/addons/sites/:siteId/:addonId/activate  # เปิดใช้งาน
POST   /v1/addons/sites/:siteId/:addonId/deactivate # ปิดใช้งาน
DELETE /v1/addons/sites/:siteId/:addonId           # ยกเลิกการเช่า
```

### Content Management

```
# News
GET    /v1/content/news/sites/:siteId              # ดึงรายการข่าว
POST   /v1/content/news/sites/:siteId              # สร้างข่าวใหม่
PUT    /v1/content/news/sites/:siteId/:newsId      # แก้ไขข่าว
DELETE /v1/content/news/sites/:siteId/:newsId      # ลบข่าว

# Blogs
GET    /v1/content/blogs/sites/:siteId             # ดึงรายการบล็อก
POST   /v1/content/blogs/sites/:siteId             # สร้างบล็อกใหม่

# Novels
GET    /v1/content/novels/sites/:siteId            # ดึงรายการนิยาย
POST   /v1/content/novels/sites/:siteId            # สร้างนิยายใหม่
```

## 🎨 UI Components

### หน้าจัดการ Addons

- แสดงรายการ addons ที่มีให้เลือก
- สถานะการเช่าและการใช้งาน
- ปุ่มเช่า/เปิดใช้งาน/ปิดใช้งาน
- ข้อมูลราคาและฟีเจอร์

### หน้าจัดการ Contents

- รายการเนื้อหาแต่ละประเภท
- ฟอร์มสร้าง/แก้ไขเนื้อหา
- การจัดการสถานะการเผยแพร่
- สถิติการใช้งาน

## 🔄 Workflow การใช้งาน

1. **เช่า Addon**: ผู้ใช้เลือกและเช่า addon ที่ต้องการ
2. **เปิดใช้งาน**: เปิดใช้งาน addon ที่เช่าแล้ว
3. **สร้างเนื้อหา**: เข้าไปสร้างและจัดการเนื้อหา
4. **เผยแพร่**: เผยแพร่เนื้อหาให้แสดงในเว็บไซต์
5. **จัดการ**: ติดตามและจัดการเนื้อหาต่อไป

## 🚨 ข้อควรระวัง

- ระบบที่ยังไม่ได้เช่าจะไม่สามารถเข้าถึงได้
- ระบบที่เช่าแล้วแต่ยังไม่เปิดใช้งานจะไม่แสดงในเว็บไซต์
- การยกเลิกการเช่าจะทำให้ข้อมูลถูกเก็บไว้ 30 วัน
- ต้องมี token authentication ในการเรียก API

## 📝 การพัฒนาต่อ

### ฟีเจอร์ที่สามารถเพิ่มได้

- ระบบ addon เพิ่มเติม (อีคอมเมิร์ซ, ฟอรั่ม, แกลเลอรี่)
- ระบบแจ้งเตือนเมื่อ addon ใกล้หมดอายุ
- ระบบสถิติการใช้งานแบบละเอียด
- ระบบส่วนลดและโปรโมชั่น
- ระบบ backup และ restore ข้อมูล

### การปรับปรุง

- เพิ่ม unit tests
- เพิ่ม integration tests
- ปรับปรุง error handling
- เพิ่ม logging และ monitoring
- ปรับปรุง performance

## 🤝 การสนับสนุน

หากมีปัญหาหรือข้อสงสัย สามารถติดต่อทีมพัฒนาได้ที่:

- Email: <EMAIL>
- Discord: #addon-support
- GitHub Issues: [repository-url]

---

**หมายเหตุ**: ระบบนี้ใช้ Mongoose สำหรับ database และ SvelteKit สำหรับ frontend ตามที่ระบุในความต้องการ

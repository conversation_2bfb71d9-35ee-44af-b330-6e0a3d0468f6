// ตัวอย่างการใช้งาน Addon System API

const BACKEND_URL = 'http://localhost:3000';
const USER_TOKEN = 'your-jwt-token-here';
const SITE_ID = 'your-site-id-here';

// Headers สำหรับ API calls
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${USER_TOKEN}`,
};

// 1. ดึงรายการ Addons ที่มีให้เลือก
async function getAvailableAddons() {
  try {
    const response = await fetch(`${BACKEND_URL}/v1/addons/sites/${SITE_ID}`, {
      method: 'GET',
      headers,
    });

    const result = await response.json();
    console.log('Available Addons:', result.data);
    return result.data;
  }
  catch (error) {
    console.error('Error fetching addons:', error);
  }
}

// 2. เช่า Addon
async function rentAddon(addonId) {
  try {
    const response = await fetch(`${BACKEND_URL}/v1/addons/sites/${SITE_ID}/${addonId}/rent`, {
      method: 'POST',
      headers,
    });

    const result = await response.json();
    console.log('Rental Result:', result);
    return result;
  }
  catch (error) {
    console.error('Error renting addon:', error);
  }
}

// 3. เปิดใช้งาน Addon
async function activateAddon(addonId) {
  try {
    const response = await fetch(`${BACKEND_URL}/v1/addons/sites/${SITE_ID}/${addonId}/activate`, {
      method: 'POST',
      headers,
    });

    const result = await response.json();
    console.log('Activation Result:', result);
    return result;
  }
  catch (error) {
    console.error('Error activating addon:', error);
  }
}

// 4. สร้างข่าวใหม่ (ต้องเช่าและเปิดใช้งาน addon news ก่อน)
async function createNews(newsData) {
  try {
    const response = await fetch(`${BACKEND_URL}/v1/content/news/sites/${SITE_ID}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(newsData),
    });

    const result = await response.json();
    console.log('News Created:', result);
    return result;
  }
  catch (error) {
    console.error('Error creating news:', error);
  }
}

// 5. สร้างบล็อกใหม่ (ต้องเช่าและเปิดใช้งาน addon blog ก่อน)
async function createBlog(blogData) {
  try {
    const response = await fetch(`${BACKEND_URL}/v1/content/blogs/sites/${SITE_ID}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(blogData),
    });

    const result = await response.json();
    console.log('Blog Created:', result);
    return result;
  }
  catch (error) {
    console.error('Error creating blog:', error);
  }
}

// 6. สร้างนิยายใหม่ (ต้องเช่าและเปิดใช้งาน addon novel ก่อน)
async function createNovel(novelData) {
  try {
    const response = await fetch(`${BACKEND_URL}/v1/content/novels/sites/${SITE_ID}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(novelData),
    });

    const result = await response.json();
    console.log('Novel Created:', result);
    return result;
  }
  catch (error) {
    console.error('Error creating novel:', error);
  }
}

// ตัวอย่างการใช้งานแบบ Workflow
async function exampleWorkflow() {
  console.log('=== Addon System Workflow Example ===');

  // 1. ดูรายการ addons ที่มี
  console.log('\n1. Fetching available addons...');
  const addons = await getAvailableAddons();

  // 2. เช่า addon news
  console.log('\n2. Renting news addon...');
  await rentAddon('news');

  // 3. เปิดใช้งาน addon news
  console.log('\n3. Activating news addon...');
  await activateAddon('news');

  // 4. สร้างข่าวใหม่
  console.log('\n4. Creating news...');
  const newsData = {
    title: 'ข่าวสารตัวอย่าง',
    content: '<p>นี่คือเนื้อหาข่าวสารตัวอย่าง</p>',
    excerpt: 'สรุปข่าวสาร',
    published: true,
    featuredImage: 'https://example.com/image.jpg',
  };
  await createNews(newsData);

  // 5. เช่าและเปิดใช้งาน addon blog
  console.log('\n5. Renting and activating blog addon...');
  await rentAddon('blog');
  await activateAddon('blog');

  // 6. สร้างบล็อกใหม่
  console.log('\n6. Creating blog...');
  const blogData = {
    title: 'บล็อกตัวอย่าง',
    content: '<p>นี่คือเนื้อหาบล็อกตัวอย่าง</p>',
    excerpt: 'สรุปบล็อก',
    tags: ['ตัวอย่าง', 'บล็อก'],
    published: true,
  };
  await createBlog(blogData);

  // 7. เช่าและเปิดใช้งาน addon novel
  console.log('\n7. Renting and activating novel addon...');
  await rentAddon('novel');
  await activateAddon('novel');

  // 8. สร้างนิยายใหม่
  console.log('\n8. Creating novel...');
  const novelData = {
    title: 'นิยายตัวอย่าง',
    description: 'คำอธิบายนิยายตัวอย่าง',
    genre: 'แฟนตาซี',
    tags: ['แฟนตาซี', 'ผจญภัย'],
    published: true,
  };
  await createNovel(novelData);

  console.log('\n=== Workflow Complete ===');
}

// Error Handling Example
async function handleAddonErrors() {
  try {
    // พยายามสร้างข่าวโดยไม่ได้เช่า addon
    await createNews({
      title: 'ข่าวทดสอบ',
      content: 'เนื้อหาทดสอบ',
    });
  }
  catch (error) {
    // จะได้ error: "ระบบ news ยังไม่ได้เปิดใช้งาน กรุณาเช่าและเปิดใช้งานก่อน"
    console.error('Expected error:', error.message);
  }
}

// ตัวอย่างการตรวจสอบสถานะ addon
async function checkAddonStatus() {
  const addons = await getAvailableAddons();

  addons.forEach(addon => {
    console.log(`Addon: ${addon.name}`);
    console.log(`- Rented: ${addon.isRented ? 'Yes' : 'No'}`);
    console.log(`- Active: ${addon.isActive ? 'Yes' : 'No'}`);
    console.log(`- Status: ${addon.status}`);
    console.log(`- Price: ฿${addon.price}/month`);
    console.log('---');
  });
}

// Export functions สำหรับใช้งานใน module อื่น
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getAvailableAddons,
    rentAddon,
    activateAddon,
    createNews,
    createBlog,
    createNovel,
    exampleWorkflow,
    handleAddonErrors,
    checkAddonStatus,
  };
}

// รันตัวอย่างถ้าไฟล์นี้ถูกเรียกโดยตรง
if (typeof require !== 'undefined' && require.main === module) {
  exampleWorkflow().catch(console.error);
}

# 🔄 Hybrid Approach Guide - SvelteKit Route API + Service Pattern

## 📋 สรุปการปรับปรุง

เราได้ปรับปรุงโปรเจ็คจาก **Service Pattern** เป็น **Hybrid Approach** ที่รวม:

- ✅ **SvelteKit Route API** - สำหรับ form handling และ validation
- ✅ **Service Pattern** - สำหรับ business logic และ API calls
- ✅ **Native Fetch** - แทน ofetch เพื่อลด dependency

## 🏗️ Architecture Overview

```
Client Form → use:enhance → Route API → Service Class → Backend API
```

### ข้อดีของ Hybrid Approach:

1. **SvelteKit Native** - ใช้ `use:enhance`, `fail()`, progressive enhancement
2. **Reusable Logic** - Service classes สามารถใช้ซ้ำได้
3. **Type Safety** - TypeScript support เต็มรูปแบบ
4. **Error Handling** - Consistent error handling pattern
5. **Testing** - ง่ายต่อการ test แยกส่วน

## 📁 File Structure

```
src/
├── routes/
│   ├── (public)/
│   │   └── signin/
│   │       ├── +page.server.ts    # Route API + Service
│   │       └── +page.svelte       # Client + use:enhance
│   └── (protected)/
│       └── dashboard/
│           └── profile/
│               ├── +page.server.ts # Route API + Service
│               └── +page.svelte    # Client + use:enhance
├── lib/
│   ├── services/
│   │   ├── auth.ts                # Auth Service
│   │   ├── user.ts                # User Service
│   │   └── base.ts                # Base Service
│   └── api/
│       └── client.ts              # API Client (Native Fetch)
```

## 🔧 Implementation Examples

### 1. Route API (Server-side)

```typescript
// +page.server.ts
import {
  acceptInvitationSchema,
  joinWithCodeSchema,
} from '$lib/schemas/invitation.schema';
import { invitationService } from '$lib/services/invitation';
import { fail } from '@sveltejs/kit';
import type { Actions } from './$types';

export const actions: Actions = {
  joinWithCode: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // ✅ Route-level validation
      const validationResult = joinWithCodeSchema.safeParse({
        inviteCode: data.get('inviteCode')?.toString()?.trim(),
      });

      if (!validationResult.success) {
        return fail(400, {
          error: validationResult.error.issues[0]?.message || 'กรุณากรอกโค้ดเชิญ',
          type: 'joinCode',
        });
      }

      const { inviteCode } = validationResult.data;

      // ✅ Call service for business logic
      const result = await invitationService.joinWithCode(
        inviteCode,
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          error: result.error || 'โค้ดเชิญไม่ถูกต้องหรือหมดอายุแล้ว',
          type: 'joinCode',
        });
      }

      return {
        success: true,
        message: result.data?.message || 'เข้าร่วมทีมงานด้วยโค้ดเชิญสำเร็จ',
        type: 'joinCode',
      };
    }
    catch (error) {
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน',
        type: 'joinCode',
      });
    }
  },
};
```

### 2. Service Class (Business Logic)

```typescript
// services/invitation.ts
import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

export interface Invitation {
  _id: string;
  siteId: string;
  siteName: string;
  fromUserId: string;
  fromUserName: string;
  fromUserEmail: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  message?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: string;
  expiresAt: string;
}

export class InvitationService extends BaseService {
  async getReceivedInvitations(
    token: string,
  ): Promise<ApiResponse<{ invitations: Invitation[]; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<
        { data: { invitations: Invitation[]; }; }
      >(
        '/invitations/received',
        token,
      );
      return result.data;
    });
  }

  async joinWithCode(
    inviteCode: string,
    token: string,
  ): Promise<ApiResponse<{ message: string; }>> {
    if (!inviteCode?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'โค้ดเชิญไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<
        { data: { message: string; }; }
      >(
        `/invitations/${inviteCode}/accept`,
        token,
        { method: 'POST' },
      );
      return result.data;
    });
  }
}
```

### 3. Schema Validation

```typescript
// schemas/invitation.schema.ts
import { z } from 'zod';

export const joinWithCodeSchema = z.object({
  inviteCode: z.string().min(1, 'กรุณากรอกโค้ดเชิญ').trim(),
});

export const acceptInvitationSchema = z.object({
  invitationId: z.string().min(1, 'ไม่พบรหัสคำเชิญ'),
});

export type JoinWithCodeData = z.infer<typeof joinWithCodeSchema>;
export type AcceptInvitationData = z.infer<typeof acceptInvitationSchema>;
```

### 4. Client Component (use:enhance)

```svelte
<!-- +page.svelte -->
<script lang="ts">
	import { enhance } from '$app/forms';

	const { data, form } = $props();
	const invitations = $derived(data.invitations || []);
	let inviteCodeInput = $state('');
	let isJoiningWithCode = $state(false);

	// Handle form result
	$effect(() => {
		if (form?.success) {
			showSuccess(form.message || 'ดำเนินการสำเร็จ');
			setTimeout(() => {
				window.location.reload();
			}, 1500);
		} else if (form?.error) {
			showError(form.error);
		}
	});
</script>

<form
	method="POST"
	action="?/joinWithCode"
	use:enhance={() => {
		isJoiningWithCode = true;
		return async ({ result }) => {
			isJoiningWithCode = false;
			if (result.type === 'success') {
				showSuccess('เข้าร่วมทีมงานสำเร็จ');
				window.location.reload();
			} else {
				showError(result.data?.error || 'เกิดข้อผิดพลาด');
			}
		};
	}}
>
	<input
		type="text"
		name="inviteCode"
		placeholder="ใส่โค้ดเชิญ"
		bind:value={inviteCodeInput}
		required
	/>
	<button type="submit" disabled={isJoiningWithCode}>
		{isJoiningWithCode ? 'กำลังเข้าร่วม...' : 'เข้าร่วม'}
	</button>
</form>
```

## 🎯 Best Practices

### 1. **Validation Strategy**

- **Route Level**: Basic validation (required fields, format)
- **Service Level**: Business logic validation
- **Client Level**: UX validation (real-time feedback)

### 2. **Error Handling**

```typescript
// Route API - ใช้ fail() สำหรับ SvelteKit
return fail(400, { message: 'Error message', type: 'field_name' });

// Service - ใช้ ApiResponse pattern
return { success: false, error: 'Error message' };

// Client - ใช้ use:enhance result
if (result.type === 'failure') {
  showError(result.data?.message);
}
```

### 3. **Type Safety**

```typescript
// Define clear interfaces
interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
}

// Use typed responses
Promise<ApiResponse<User>>;
```

### 4. **Progressive Enhancement**

- Form ทำงานได้แม้ไม่มี JavaScript
- `use:enhance` เพิ่ม UX เมื่อมี JavaScript
- Loading states และ error handling

## 🧪 Testing Strategy

### 1. **Service Testing**

```typescript
// Easy to test business logic
const result = await userService.updateProfile(mockData, mockToken);
expect(result.success).toBe(true);
```

### 2. **Route API Testing**

```typescript
// Test SvelteKit actions
const result = await actions.updateProfile({
  request: mockRequest,
  locals: mockLocals,
});
```

### 3. **Integration Testing**

- Test complete flow: Client → Route → Service → API

## 📊 Performance Benefits

1. **Reduced Bundle Size** - ไม่ต้อง ship service classes ไป client
2. **Better Caching** - SvelteKit handle caching automatically
3. **SSR Support** - Form ทำงานได้ทั้ง server และ client
4. **Progressive Enhancement** - ทำงานได้แม้ไม่มี JavaScript

## 🔄 Migration Checklist

- [x] ✅ เปลี่ยนจาก ofetch เป็น native fetch
- [x] ✅ ปรับปรุง API client ให้ใช้ class-based pattern
- [x] ✅ อัปเดต BaseService ให้รองรับ API client ใหม่
- [x] ✅ ปรับปรุง signin route ให้ใช้ fail() แทน ResponseHelper
- [x] ✅ ปรับปรุง profile route ให้ใช้ Hybrid Approach
- [x] ✅ เพิ่ม validation ใน route level
- [x] ✅ ปรับปรุง error handling ให้สอดคล้องกัน
- [x] ✅ ทดสอบ use:enhance ใน client components
- [x] ✅ ปรับปรุง orders route ให้ใช้ Hybrid Approach
- [x] ✅ ปรับปรุง shipping route ให้ใช้ Hybrid Approach
- [x] ✅ ปรับปรุง products/reviews route ให้ใช้ Hybrid Approach
- [x] ✅ ปรับปรุง join route ให้ใช้ Hybrid Approach

## 🚀 Next Steps

1. **ขยายไปยัง routes อื่นๆ** - categories, products, orders
2. **เพิ่ม middleware** - rate limiting, validation
3. **ปรับปรุง error handling** - centralized error management
4. **เพิ่ม testing** - unit tests และ integration tests
5. **Performance optimization** - caching, lazy loading

## 📚 Resources

- [SvelteKit Form Actions](https://kit.svelte.dev/docs/form-actions)
- [Progressive Enhancement](https://kit.svelte.dev/docs/form-actions#progressive-enhancement)
- [TypeScript Support](https://kit.svelte.dev/docs/types)
- [Error Handling](https://kit.svelte.dev/docs/errors)

---

**สรุป**: Hybrid Approach ให้เราได้ประโยชน์จากทั้ง SvelteKit native features และ Service pattern ที่มีอยู่แล้ว ทำให้โค้ดมี maintainability ดี type safety สูง และ user experience ที่ดีขึ้น

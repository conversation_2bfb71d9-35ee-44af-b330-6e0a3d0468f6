# การปรับปรุง Stores ใน SvelteKit Dashboard

## ปัญหาที่พบและแก้ไขแล้ว

### 1. **API Client Issues** ✅ แก้ไขแล้ว

**ปัญหา:**

- `apiClient` ไม่สามารถเรียกใช้ได้เพราะ type ไม่ถูกต้อง
- ใช้ `apiClient(url, options)` แทน `apiClient.request(url, options)`

**การแก้ไข:**

```typescript
// ❌ เดิม
const data = await apiClient('/api/users', { headers: this.getAuthHeaders() });

// ✅ แก้ไขแล้ว
const data = await apiClient.request('/api/users', {
  headers: this.getAuthHeaders(),
});
```

**ไฟล์ที่แก้ไข:**

- `src/lib/stores/users.svelte.ts`
- `src/lib/stores/websites.svelte.ts`

### 2. **User Interface Mismatch** ✅ แก้ไขแล้ว

**ปัญหา:**

- ใช้ `User.id` แทน `User._id` ซึ่งไม่ตรงกับ interface ที่กำหนด

**การแก้ไข:**

```typescript
// ❌ เดิม
const index = this._users.findIndex(u => u.id === id);

// ✅ แก้ไขแล้ว
const index = this._users.findIndex(u => u._id === id);
```

**ไฟล์ที่แก้ไข:**

- `src/lib/stores/users.svelte.ts`

### 3. **Auth Store Complexity** ✅ ปรับปรุงแล้ว

**ปัญหา:**

- Code ซับซ้อนเกินไปสำหรับ Token Rotation System
- มี dependencies ที่ไม่จำเป็น (logger, tokenCache, securityMonitor, sweetalert)
- มี console.log มากเกินไป

**การแก้ไข:**

- ลบ dependencies ที่ไม่จำเป็นออก
- ลดความซับซ้อนของ cross-tab sync
- ปรับปรุง token management ให้เหมาะกับ Token Rotation System
- ลด console.log ที่ไม่จำเป็น

**ไฟล์ที่แก้ไข:**

- `src/lib/stores/auth.svelte.ts`

### 4. **Loading Store Dependencies** ✅ ตรวจสอบแล้ว

**สถานะ:**

- Loading store ใช้ config ที่มีอยู่แล้วใน `src/lib/config/loading.ts`
- ไม่มีปัญหา dependencies

## สรุปการปรับปรุง

### ✅ ปรับปรุงแล้ว:

1. **API Client Usage** - แก้ไขการเรียกใช้ `apiClient.request()` ให้ถูกต้อง
2. **User Interface** - แก้ไข `User.id` เป็น `User._id`
3. **Auth Store Simplification** - ลดความซับซ้อนและลบ dependencies ที่ไม่จำเป็น
4. **Error Handling** - ปรับปรุงการจัดการ error ให้สอดคล้องกัน

### 🔍 ตรวจสอบแล้ว:

1. **Loading Store** - ใช้ config ที่ถูกต้องแล้ว
2. **Other Stores** - ไม่มีปัญหาที่ต้องแก้ไข

## คุณสมบัติที่ปรับปรุงแล้ว

### Auth Store:

- ✅ Simplified Token Rotation System support
- ✅ Cross-tab synchronization
- ✅ Proper error handling
- ✅ Clean state management
- ✅ Reduced dependencies

### Users Store:

- ✅ Correct API client usage
- ✅ Proper User interface usage
- ✅ CRUD operations working

### Websites Store:

- ✅ Correct API client usage
- ✅ CRUD operations working
- ✅ Backup functionality

### Other Stores:

- ✅ Language Store - Working correctly
- ✅ Theme Store - Working correctly
- ✅ Site Store - Working correctly
- ✅ Toast Store - Working correctly
- ✅ Notification Store - Working correctly
- ✅ Loading Store - Working correctly

## การใช้งาน

### Auth Store:

```typescript
import { authStore } from '$lib/stores/auth.svelte';

// Sign in
await authStore.signin({ email, password, rememberMe });

// Sign out
await authStore.signout();

// Update user
authStore.updateUser(updatedUser);

// Check authentication
if (authStore.isAuthenticated) {
  // User is logged in
}
```

### Users Store:

```typescript
import { usersStore } from '$lib/stores/users.svelte';

// Load users
await usersStore.loadUsers({ search: 'john', role: 'admin' });

// Create user
await usersStore.createUser(userData);

// Update user
await usersStore.updateUser(userId, updates);

// Delete user
await usersStore.deleteUser(userId);
```

### Websites Store:

```typescript
import { websitesStore } from '$lib/stores/websites.svelte';

// Load websites
await websitesStore.loadWebsites({ status: 'active' });

// Create website
await websitesStore.createWebsite(websiteData);

// Backup website
await websitesStore.backupWebsite(websiteId);
```

## หมายเหตุ

- ทุก stores ใช้ Svelte 5 runes (`$state`) แล้ว
- Token Rotation System ทำงานผ่าน server-side hooks
- Cross-tab synchronization ทำงานผ่าน BroadcastChannel และ localStorage fallback
- Error handling สอดคล้องกันทุก stores

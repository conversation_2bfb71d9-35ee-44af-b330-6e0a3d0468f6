<script lang="ts">
	import Icon from '@iconify/svelte';
	import { fade } from 'svelte/transition';

	export interface Activity {
		_id: string;
		type: string;
		title: string;
		description: string;
		createdAt: string;
		icon: string;
		color: string;
		priority: 'low' | 'medium' | 'high' | 'critical';
		status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
		entityType: string;
		entityId: string;
		entityName?: string;
		metadata?: Record<string, any>;
	}

	const { 
		activities = [], 
		showPriority = false, 
		showStatus = false, 
		showEntity = false, 
		maxItems = 10, 
		emptyMessage = 'ไม่มีข้อมูลกิจกรรม' 
	} = $props<{
		activities?: Activity[];
		showPriority?: boolean;
		showStatus?: boolean;
		showEntity?: boolean;
		maxItems?: number;
		emptyMessage?: string;
	}>();

	// Filter activities based on maxItems
	const displayedActivities = $derived(activities.slice(0, maxItems));

	// Get priority color
	function getPriorityColor(priority: string): string {
		switch (priority) {
			case 'critical':
				return 'text-red-500';
			case 'high':
				return 'text-orange-500';
			case 'medium':
				return 'text-yellow-500';
			case 'low':
				return 'text-green-500';
			default:
				return 'text-gray-500';
		}
	}

	// Get status color
	function getStatusColor(status: string): string {
		switch (status) {
			case 'completed':
				return 'text-green-500';
			case 'processing':
				return 'text-blue-500';
			case 'pending':
				return 'text-yellow-500';
			case 'failed':
				return 'text-red-500';
			case 'cancelled':
				return 'text-gray-500';
			default:
				return 'text-gray-500';
		}
	}

	// Get status text
	function getStatusText(status: string): string {
		switch (status) {
			case 'completed':
				return 'เสร็จสิ้น';
			case 'processing':
				return 'กำลังดำเนินการ';
			case 'pending':
				return 'รอดำเนินการ';
			case 'failed':
				return 'ล้มเหลว';
			case 'cancelled':
				return 'ยกเลิก';
			default:
				return status;
		}
	}

	// Get priority text
	function getPriorityText(priority: string): string {
		switch (priority) {
			case 'critical':
				return 'สำคัญมาก';
			case 'high':
				return 'สูง';
			case 'medium':
				return 'ปานกลาง';
			case 'low':
				return 'ต่ำ';
			default:
				return priority;
		}
	}
</script>

<div class="space-y-3">
	{#if displayedActivities.length === 0}
		<div class="text-center py-8">
			<Icon icon="mdi:information" class="w-12 h-12 mx-auto text-base-content/30 mb-4" />
			<p class="text-base-content/60">{emptyMessage}</p>
		</div>
	{:else}
		{#each displayedActivities as activity, index}
			<div
				class="flex items-start gap-4 p-4 bg-base-200/50 rounded-lg hover:bg-base-200 transition-colors"
				transition:fade={{ delay: index * 50 }}
			>
				<!-- Activity Icon -->
				<div
					class="w-10 h-10 rounded-full bg-base-100 flex items-center justify-center flex-shrink-0"
				>
					<Icon icon={activity.icon} class="w-5 h-5 {activity.color}" />
				</div>

				<!-- Activity Content -->
				<div class="flex-1 min-w-0">
					<div class="flex items-center justify-between mb-1">
						<h4 class="font-medium text-base-content">
							{activity.title}
						</h4>
						<span class="text-xs text-base-content/60">
							{new Date(activity.createdAt).toLocaleString('th-TH')}
						</span>
					</div>
					<p class="text-sm text-base-content/70 mb-2">
						{activity.description}
					</p>

					<!-- Activity Metadata -->
					<div class="flex items-center gap-2 flex-wrap">
						{#if showPriority}
							<span class="text-xs px-2 py-1 rounded-full bg-base-100 {getPriorityColor(activity.priority)}">
								{getPriorityText(activity.priority)}
							</span>
						{/if}

						{#if showStatus}
							<span class="text-xs px-2 py-1 rounded-full bg-base-100 {getStatusColor(activity.status)}">
								{getStatusText(activity.status)}
							</span>
						{/if}

						{#if showEntity && activity.entityName}
							<span class="text-xs px-2 py-1 rounded-full bg-base-100 text-base-content/70">
								{activity.entityName}
							</span>
						{/if}

						<!-- Activity Type Badge -->
						<span class="text-xs px-2 py-1 rounded-full bg-primary/10 text-primary">
							{activity.type.replace('_', ' ')}
						</span>
					</div>

					<!-- Additional Metadata -->
					{#if activity.metadata && Object.keys(activity.metadata).length > 0}
						<div class="mt-2 text-xs text-base-content/50">
							{#each Object.entries(activity.metadata) as [key, value]}
								<span class="inline-block mr-2">
									{key}: {typeof value === 'object' ? JSON.stringify(value) : value}
								</span>
							{/each}
						</div>
					{/if}
				</div>
			</div>
		{/each}

		<!-- Show more indicator -->
		{#if activities.length > maxItems}
			<div class="text-center py-4">
				<p class="text-sm text-base-content/60">
					แสดง {maxItems} จาก {activities.length} กิจกรรม
				</p>
			</div>
		{/if}
	{/if}
</div> 
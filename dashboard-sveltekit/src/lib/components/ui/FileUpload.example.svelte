<script lang="ts">
	import Icon from '@iconify/svelte';
	import FileUpload from './FileUpload.svelte';
	import Card from './Card.svelte';
	import Button from './Button.svelte';

	// ✅ State สำหรับตัวอย่างต่างๆ
	let singleImageFiles = $state<File[]>([]);
	let multipleImageFiles = $state<File[]>([]);
	let documentFiles = $state<File[]>([]);
	let videoFiles = $state<File[]>([]);
	let allFiles = $state<File[]>([]);

	// ✅ Event handlers
	function handleSingleImageSelected(files: File[]) {
		singleImageFiles = files;
		console.log('Single image files:', files);
	}

	function handleMultipleImageSelected(files: File[]) {
		multipleImageFiles = files;
		console.log('Multiple image files:', files);
	}

	function handleDocumentSelected(files: File[]) {
		documentFiles = files;
		console.log('Document files:', files);
	}

	function handleVideoSelected(files: File[]) {
		videoFiles = files;
		console.log('Video files:', files);
	}

	function handleAllFilesSelected(files: File[]) {
		allFiles = files;
		console.log('All files:', files);
	}

	function handleFileRemoved(file: File, index: number) {
		console.log('File removed:', file.name, 'at index:', index);
	}

	function handleError(error: string) {
		console.error('File upload error:', error);
		alert(`ข้อผิดพลาด: ${error}`);
	}
</script>

<div class="space-y-8 p-6">
	<h1 class="text-2xl font-bold mb-6">FileUpload Component Examples</h1>

	<!-- Single Image Upload -->
	<Card title="Single Image Upload" size="full">
		<div class="space-y-4">
			<p class="text-sm text-base-content/70">
				อัปโหลดรูปภาพเดียว ขนาดไม่เกิน 5MB
			</p>
			<FileUpload
				multiple={false}
				accept="image/*"
				maxSize={5 * 1024 * 1024}
				preview={true}
				previewSize="w-20 h-20"
				placeholder="เลือกรูปภาพโปรไฟล์"
				onFilesSelected={handleSingleImageSelected}
				onFileRemoved={handleFileRemoved}
				onError={handleError}
			/>
			{#if singleImageFiles.length > 0}
				<div class="text-sm text-success">
					✓ เลือกไฟล์แล้ว: {singleImageFiles.length} ไฟล์
				</div>
			{/if}
		</div>
	</Card>

	<!-- Multiple Image Upload -->
	<Card title="Multiple Image Upload (สูงสุด 3 ไฟล์)" size="full">
		<div class="space-y-4">
			<p class="text-sm text-base-content/70">
				อัปโหลดรูปภาพหลายไฟล์ สูงสุด 3 ไฟล์
			</p>
            <!-- // 2MB -->
			<FileUpload
				multiple={true}
				maxFiles={3}
				accept="image/*"
				maxSize={2 * 1024 * 1024}
				preview={true}
				previewSize="w-16 h-16"
				placeholder="เลือกรูปภาพหลายไฟล์"
				onFilesSelected={handleMultipleImageSelected}
				onFileRemoved={handleFileRemoved}
				onError={handleError}
			/>
			{#if multipleImageFiles.length > 0}
				<div class="text-sm text-success">
					✓ เลือกไฟล์แล้ว: {multipleImageFiles.length}/3 ไฟล์
				</div>
			{/if}
		</div>
	</Card>

	<!-- Document Upload -->
	<Card title="Document Upload" size="full">
		<div class="space-y-4">
			<p class="text-sm text-base-content/70">
				อัปโหลดเอกสาร PDF, Word, Excel ขนาดไม่เกิน 10MB
			</p>
            <!-- // 10MB -->
			<FileUpload
				multiple={true}
				maxFiles={5}
				accept=".pdf,.doc,.docx,.xls,.xlsx"
				maxSize={10 * 1024 * 1024}
				preview={false}
				placeholder="เลือกเอกสาร"
				onFilesSelected={handleDocumentSelected}
				onFileRemoved={handleFileRemoved}
				onError={handleError}
			/>
			{#if documentFiles.length > 0}
				<div class="text-sm text-success">
					✓ เลือกเอกสารแล้ว: {documentFiles.length} ไฟล์
				</div>
			{/if}
		</div>
	</Card>

	<!-- Video Upload -->
	<Card title="Video Upload" size="full">
		<div class="space-y-4">
			<p class="text-sm text-base-content/70">
				อัปโหลดวิดีโอ ขนาดไม่เกิน 50MB
			</p>
            <!-- // 50MB -->
			<FileUpload
				multiple={false}
				accept="video/*"
				maxSize={50 * 1024 * 1024}
				preview={true}
				previewSize="w-24 h-24"
				placeholder="เลือกไฟล์วิดีโอ"
				onFilesSelected={handleVideoSelected}
				onFileRemoved={handleFileRemoved}
				onError={handleError}
			/>
			{#if videoFiles.length > 0}
				<div class="text-sm text-success">
					✓ เลือกวิดีโอแล้ว: {videoFiles.length} ไฟล์
				</div>
			{/if}
		</div>
	</Card>

	<!-- All File Types -->
	<Card title="All File Types" size="full">
		<div class="space-y-4">
			<p class="text-sm text-base-content/70">
				อัปโหลดไฟล์ทุกประเภท ขนาดไม่เกิน 20MB
			</p>
            <!-- // 20MB -->
			<FileUpload
				multiple={true}
				maxFiles={10}
				accept="*/*"
				maxSize={20 * 1024 * 1024}
				preview={true}
				previewSize="w-12 h-12"
				placeholder="เลือกไฟล์ใดก็ได้"
				onFilesSelected={handleAllFilesSelected}
				onFileRemoved={handleFileRemoved}
				onError={handleError}
			/>
			{#if allFiles.length > 0}
				<div class="text-sm text-success">
					✓ เลือกไฟล์แล้ว: {allFiles.length}/10 ไฟล์
				</div>
			{/if}
		</div>
	</Card>

	<!-- Custom Styling -->
	<Card title="Custom Styling" size="full">
		<div class="space-y-4">
			<p class="text-sm text-base-content/70">
				ตัวอย่างการปรับแต่ง style
			</p>
			<FileUpload
				multiple={false}
				accept="image/*"
				maxSize={5 * 1024 * 1024}
				preview={true}
				previewSize="w-32 h-32"
				placeholder="อัปโหลดรูปภาพสวยๆ"
				dropZoneClass="bg-gradient-to-r from-primary to-secondary border-primary"
				onFilesSelected={handleSingleImageSelected}
				onFileRemoved={handleFileRemoved}
				onError={handleError}
			/>
		</div>
	</Card>

	<!-- Disabled State -->
	<Card title="Disabled State" size="full">
		<div class="space-y-4">
			<p class="text-sm text-base-content/70">
				ตัวอย่างเมื่อ component ถูก disable
			</p>
			<FileUpload
				multiple={false}
				accept="image/*"
				maxSize={5 * 1024 * 1024}
				preview={true}
				disabled={true}
				placeholder="ไม่สามารถอัปโหลดได้"
				onFilesSelected={handleSingleImageSelected}
				onFileRemoved={handleFileRemoved}
				onError={handleError}
			/>
		</div>
	</Card>

	<!-- Usage Instructions -->
	<Card title="วิธีการใช้งาน" size="full">
		<div class="space-y-4">
			<h3 class="font-semibold">Props ที่สำคัญ:</h3>
			<div class="text-sm space-y-2">
				<div><strong>multiple:</strong> true/false - รองรับหลายไฟล์</div>
				<div><strong>maxFiles:</strong> จำนวนไฟล์สูงสุด</div>
				<div><strong>accept:</strong> ประเภทไฟล์ที่รองรับ (เช่น "image/*", ".pdf,.doc")</div>
				<div><strong>maxSize:</strong> ขนาดไฟล์สูงสุด (bytes)</div>
				<div><strong>preview:</strong> true/false - แสดงตัวอย่าง</div>
				<div><strong>previewSize:</strong> ขนาดตัวอย่าง (เช่น "w-16 h-16")</div>
				<div><strong>disabled:</strong> true/false - ปิดการใช้งาน</div>
			</div>

			<h3 class="font-semibold mt-4">Event Handlers:</h3>
			<div class="text-sm space-y-2">
				<div><strong>onFilesSelected:</strong> เรียกเมื่อเลือกไฟล์</div>
				<div><strong>onFileRemoved:</strong> เรียกเมื่อลบไฟล์</div>
				<div><strong>onError:</strong> เรียกเมื่อเกิดข้อผิดพลาด</div>
			</div>

			<h3 class="font-semibold mt-4">Methods ที่ expose:</h3>
			<div class="text-sm space-y-2">
				<div><strong>clearFiles():</strong> ลบไฟล์ทั้งหมด</div>
				<div><strong>getFiles():</strong> ดึงไฟล์ที่เลือก</div>
				<div><strong>setFiles(files):</strong> กำหนดไฟล์</div>
			</div>
		</div>
	</Card>
</div> 
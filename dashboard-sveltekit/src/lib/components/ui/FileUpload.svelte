<script lang="ts">
	import Icon from '@iconify/svelte';
	import { fade } from 'svelte/transition';

	// ✅ Props
	const {
		multiple = false,
		maxFiles = 1,
		accept = 'image/*',
		maxSize = 5 * 1024 * 1024, // 5MB default
		preview = true,
		disabled = false,
		placeholder = 'ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์',
		dropZoneClass = '',
		showFileInfo = true,
		showRemoveButton = true,
		previewSize = 'w-16 h-16',
		onFilesSelected = () => {},
		onFileRemoved = () => {},
		onError = () => {},
	} = $props<{
		multiple?: boolean;
		maxFiles?: number;
		accept?: string;
		maxSize?: number;
		preview?: boolean;
		disabled?: boolean;
		placeholder?: string;
		dropZoneClass?: string;
		showFileInfo?: boolean;
		showRemoveButton?: boolean;
		previewSize?: string;
		onFilesSelected?: (files: File[]) => void;
		onFileRemoved?: (file: File, index: number) => void;
		onError?: (error: string) => void;
	}>();

	// ✅ State
	let selectedFiles = $state<File[]>([]);
	let isDragOver = $state(false);
	let dragCounter = $state(0);

	// ✅ Computed values
	const isMultiple = $derived(multiple && maxFiles > 1);
	const canAddMore = $derived(selectedFiles.length < maxFiles);
	const isDisabled = $derived(disabled || (!isMultiple && selectedFiles.length >= maxFiles));

	// ✅ File validation
	function validateFile(file: File): { valid: boolean; error?: string } {
		// Check file type
		if (accept !== '*/*' && !file.type.match(accept.replace('*', '.*'))) {
			return { valid: false, error: `ไฟล์ประเภท ${file.type} ไม่รองรับ` };
		}

		// Check file size
		if (file.size > maxSize) {
			const maxSizeMB = (maxSize / 1024 / 1024).toFixed(1);
			return { valid: false, error: `ไฟล์ต้องมีขนาดไม่เกิน ${maxSizeMB}MB` };
		}

		return { valid: true };
	}

	// ✅ File handling
	function handleFiles(files: FileList | null) {
		if (!files || files.length === 0) return;

		const newFiles: File[] = [];
		const errors: string[] = [];

		for (let i = 0; i < files.length; i++) {
			const file = files[i];
			const validation = validateFile(file);

			if (validation.valid) {
				if (isMultiple) {
					if (selectedFiles.length + newFiles.length < maxFiles) {
						newFiles.push(file);
					} else {
						errors.push(`สามารถเลือกไฟล์ได้สูงสุด ${maxFiles} ไฟล์`);
						break;
					}
				} else {
					newFiles.push(file);
					break; // Only take first file for single mode
				}
			} else {
				errors.push(validation.error!);
			}
		}

		// Add valid files
		if (newFiles.length > 0) {
			selectedFiles = [...selectedFiles, ...newFiles];
			onFilesSelected(selectedFiles);
		}

		// Show errors
		if (errors.length > 0) {
			onError(errors.join(', '));
		}
	}

	// ✅ Event handlers
	function handleDropZoneClick() {
		if (isDisabled) return;
		const fileInput = document.querySelector(`input[name="${accept.replace('*', 'file')}"]`) as HTMLInputElement;
		if (fileInput) {
			fileInput.click();
		}
	}

	function handleDropZoneKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleDropZoneClick();
		}
	}

	function handleFileChange(event: Event) {
		const target = event.target as HTMLInputElement;
		handleFiles(target.files);
	}

	function handleDropZoneDrop(event: DragEvent) {
		event.preventDefault();
		dragCounter = 0;
		isDragOver = false;
		handleFiles(event.dataTransfer?.files || null);
	}

	function handleDropZoneDragOver(event: DragEvent) {
		event.preventDefault();
		if (!isDisabled) {
			isDragOver = true;
		}
	}

	function handleDropZoneDragEnter(event: DragEvent) {
		event.preventDefault();
		dragCounter++;
		if (!isDisabled) {
			isDragOver = true;
		}
	}

	function handleDropZoneDragLeave(event: DragEvent) {
		event.preventDefault();
		dragCounter--;
		if (dragCounter === 0) {
			isDragOver = false;
		}
	}

	function removeFile(index: number) {
		const removedFile = selectedFiles[index];
		selectedFiles = selectedFiles.filter((_, i) => i !== index);
		onFileRemoved(removedFile, index);
	}

	function clearAllFiles() {
		selectedFiles = [];
		onFilesSelected([]);
	}

	// ✅ Format file size
	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	// ✅ Get file type display name
	function getFileTypeDisplay(file: File): string {
		if (file.type.startsWith('image/')) {
			return 'รูปภาพ';
		} else if (file.type.startsWith('video/')) {
			return 'วิดีโอ';
		} else if (file.type.startsWith('audio/')) {
			return 'เสียง';
		} else if (file.type.includes('pdf')) {
			return 'PDF';
		} else if (file.type.includes('document') || file.type.includes('word')) {
			return 'เอกสาร';
		} else if (file.type.includes('spreadsheet') || file.type.includes('excel')) {
			return 'ตาราง';
		} else {
			return 'ไฟล์';
		}
	}

	// ✅ Get file icon
	function getFileIcon(file: File): string {
		if (file.type.startsWith('image/')) {
			return 'mdi:file-image';
		} else if (file.type.startsWith('video/')) {
			return 'mdi:file-video';
		} else if (file.type.startsWith('audio/')) {
			return 'mdi:file-music';
		} else if (file.type.includes('pdf')) {
			return 'mdi:file-pdf-box';
		} else if (file.type.includes('document') || file.type.includes('word')) {
			return 'mdi:file-document';
		} else if (file.type.includes('spreadsheet') || file.type.includes('excel')) {
			return 'mdi:file-excel';
		} else {
			return 'mdi:file';
		}
	}

	// ✅ Expose methods for parent component (using bind:this instead of $host)
	let componentElement: HTMLElement | undefined;
	$effect(() => {
		if (componentElement) {
			(componentElement as any).clearFiles = clearAllFiles;
			(componentElement as any).getFiles = () => selectedFiles;
			(componentElement as any).setFiles = (files: File[]) => {
				selectedFiles = files;
			};
		}
	});
</script>

<div class="file-upload-component" bind:this={componentElement}>
	<!-- Drop Zone -->
	<div
		class="border-2 border-dashed rounded-lg p-8 transition-all duration-200 cursor-pointer {isDragOver ? 'border-primary bg-primary/10' : 'border-base-300 hover:border-primary'} {isDisabled ? 'opacity-50 cursor-not-allowed' : ''} {dropZoneClass}"
		onclick={handleDropZoneClick}
		onkeydown={handleDropZoneKeydown}
		tabindex={isDisabled ? -1 : 0}
		role="button"
		aria-label={placeholder}
		aria-disabled={isDisabled}
		ondrop={handleDropZoneDrop}
		ondragover={handleDropZoneDragOver}
		ondragenter={handleDropZoneDragEnter}
		ondragleave={handleDropZoneDragLeave}
	>
		<Icon 
			icon="mdi:cloud-upload" 
			class="w-12 h-12 mx-auto {isDragOver ? 'text-primary' : 'text-base-content/50'} mb-2 transition-colors" 
		/>
		<p class="text-sm {isDragOver ? 'text-primary font-medium' : 'text-base-content/70'} transition-colors">
			{isDragOver ? 'วางไฟล์ที่นี่' : placeholder}
		</p>
		<p class="text-xs text-base-content/50 mt-1">
			{isMultiple ? `รองรับไฟล์ ${accept} สูงสุด ${maxFiles} ไฟล์ ขนาดไม่เกิน ${formatFileSize(maxSize)}` : `รองรับไฟล์ ${accept} ขนาดไม่เกิน ${formatFileSize(maxSize)}`}
		</p>
		
		<!-- Hidden file input -->
		<input 
			type="file" 
			name="{accept.replace('*', 'file')}"
			accept={accept}
			multiple={isMultiple}
			class="hidden" 
			onchange={handleFileChange}
			disabled={isDisabled}
		/>
	</div>

	<!-- Selected Files -->
	{#if selectedFiles.length > 0}
		<div class="mt-4 space-y-3" transition:fade>
			{#each selectedFiles as file, index}
				<div class="bg-base-200 p-3 rounded-lg">
					<div class="flex items-center gap-3">
						<!-- Preview -->
						{#if preview}
							<div class="{previewSize} rounded-lg overflow-hidden bg-base-300 flex items-center justify-center">
								{#if file.type.startsWith('image/')}
									<img 
										src={URL.createObjectURL(file)} 
										alt="Preview" 
										class="w-full h-full object-cover"
									/>
								{:else}
									<Icon icon={getFileIcon(file)} class="w-8 h-8 text-base-content/50" />
								{/if}
							</div>
						{/if}
						
						<!-- File Info -->
						{#if showFileInfo}
							<div class="flex-1">
								<div class="flex items-center gap-2">
									<Icon icon={getFileIcon(file)} class="w-4 h-4 text-primary" />
									<span class="text-sm font-medium">{file.name}</span>
								</div>
								<div class="text-xs text-base-content/60">
									{formatFileSize(file.size)} • {getFileTypeDisplay(file)}
								</div>
							</div>
						{/if}

						<!-- Remove Button -->
						{#if showRemoveButton}
							<button
								type="button"
								class="btn btn-xs btn-circle btn-ghost text-error hover:bg-error hover:text-error-content"
								onclick={() => removeFile(index)}
								aria-label="ลบไฟล์ {file.name}"
							>
								<Icon icon="mdi:close" class="w-3 h-3" />
							</button>
						{/if}
					</div>
				</div>
			{/each}

			<!-- Clear All Button (for multiple files) -->
			{#if isMultiple && selectedFiles.length > 1 && showRemoveButton}
				<div class="flex justify-end">
					<button
						type="button"
						class="btn btn-xs btn-ghost text-error"
						onclick={clearAllFiles}
					>
						<Icon icon="mdi:delete-sweep" class="w-3 h-3 mr-1" />
						ลบทั้งหมด
					</button>
				</div>
			{/if}
		</div>
	{/if}
</div> 
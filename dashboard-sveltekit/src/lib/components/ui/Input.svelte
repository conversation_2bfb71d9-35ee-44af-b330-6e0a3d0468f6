<script lang="ts">
	import Icon from '@iconify/svelte'; 
	import Label from './Label.svelte';


	interface Props {
		type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
		value?: string | number;
		placeholder?: string;
		label?: string;
		error?: string;
		info?: string;
		disabled?: boolean;
		required?: boolean;
		readonly?: boolean;
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		color?: 'ghost' | 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
		variant?: 'bordered' | 'soft' | 'ghost' | 'outlined';
		rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
		icon?: string;
		iconRight?: string;
		class?: string;
		id?: string;
		name?: string;
		autocomplete?:
			| 'off'
			| 'on'
			| 'name'
			| 'email'
			| 'username'
			| 'new-password'
			| 'current-password'
			| 'given-name'
			| 'family-name'
			| 'tel'
			| 'url'
			| 'street-address'
			| 'postal-code'
			| 'country'
			| string;
		// Number input specific props
		min?: number | string;
		max?: number | string;
		step?: number | string;
		// Text input specific props
		maxlength?: number;
		pattern?: string;
		// Event handlers
		oninput?: (event: Event) => void;
		onchange?: (event: Event) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
		onkeydown?: (event: KeyboardEvent) => void;
		// Password specific
		showPasswordToggle?: boolean;
		// Validation
		validate?: string;
		showRequired?: boolean;
		// Number formatting
		formatNumber?: boolean;
		numberFormat?: {
			locale?: string;
			currency?: string;
			minimumFractionDigits?: number;
			maximumFractionDigits?: number;
		};
	}

	let {
		type = 'text',
		value = $bindable(''),
		placeholder,
		label,
		error,
		info,
		disabled = false,
		required = false,
		readonly = false,
		size = 'md',
		color,
		variant = 'bordered',
		rounded = 'sm',
		icon,
		iconRight,
		class: className = '',
		id,
		name,
		autocomplete,
		min,
		max,
		step,
		maxlength,
		pattern,
		oninput,
		onchange,
		onfocus,
		onblur,
		onkeydown,
		showPasswordToggle = false,
		validate,
		showRequired = false,
		formatNumber = false,
		numberFormat = {},
		...restProps
	}: Props = $props();

	let showPassword = $state(false);
	let displayValue = $state('');

	// จัดการ display value สำหรับ number formatting
	$effect(() => {
		if (type === 'number' && formatNumber && typeof value === 'number') {
			displayValue = formatNumberValue(value);
		} else {
			displayValue = value as string;
		}
	});

	const inputType = $derived(() => {
		if (type === 'password' && showPasswordToggle) {
			return showPassword ? 'text' : 'password';
		}
		return type;
	});

	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}

	// ฟังก์ชันสำหรับจัดการ number formatting
	function formatNumberValue(val: number | string): string {
		if (typeof val !== 'number') return val;
		
		const { locale = 'th-TH', currency = 'THB', minimumFractionDigits = 0, maximumFractionDigits = 2 } = numberFormat;
		
		return new Intl.NumberFormat(locale, {
			style: currency ? 'currency' : 'decimal',
			currency,
			minimumFractionDigits,
			maximumFractionDigits,
		}).format(val);
	}

	// ฟังก์ชันสำหรับแปลง formatted number กลับเป็น number
	function parseNumberValue(val: string): number {
		if (!val) return 0;
		
		// ลบสัญลักษณ์สกุลเงินและ comma ออก
		const cleanValue = val.replace(/[^\d.-]/g, '');
		return parseFloat(cleanValue) || 0;
	}

	// ฟังก์ชันสำหรับจัดการ input change สำหรับ number
	function handleNumberChange(event: Event) {
		if (type === 'number' && formatNumber) {
			const target = event.target as HTMLInputElement;
			const numericValue = parseNumberValue(target.value);
			value = numericValue;
		}
		onchange?.(event);
	}

	// Get icon size based on input size
	function getIconSize() {
		switch (size) {
			case 'xs':
				return 'size-4';
			case 'sm':
				return 'size-5';
			case 'lg':
				return 'size-7';
			case 'xl':
				return 'size-8';
			default: // md
				return 'size-6';
		}
	}

	// Combine error and validate
	const displayError = $derived(error || validate);

	const baseClasses = 'input w-full';

	const classes = $derived(
		[
			baseClasses,
			// Style classes
			variant === 'bordered' ? '!input-bordered' : '',
			variant === 'soft' ? '!input-soft' : '',
			variant === 'ghost' ? '!input-ghost' : '',
			variant === 'outlined' ? '!input-outlined' : '',
			// Rounded classes
			rounded === 'none' ? '!rounded-none' : '',
			rounded === 'sm' ? '!rounded-sm' : '',
			rounded === 'md' ? '!rounded-md' : '',
			rounded === 'lg' ? '!rounded-lg' : '',
			rounded === 'full' ? '!rounded-full' : '',
			// Variant classes (only if variant is provided)
			color ? `input-${color}` : '',
			size !== 'md' ? `input-${size}` : '',
			displayError ? 'input-error border-error focus:border-error' : '',
			className,
		]
			.filter(Boolean)
			.join(' ')
	);
	const wrapperClasses = $derived(
		['w-full', icon || iconRight ? 'relative' : ''].filter(Boolean).join(' ')
	);
</script>

<div class={wrapperClasses}>
	{#if label}
		<!-- <label class="label" for={id}>
			<span class="label-text {displayError ? 'text-error' : ''}">{label}</span>
			{#if showRequired}
				<span class="text-error">*</span>
			{/if}
		</label> -->
		<Label validate={displayError} for={id} text={label} required={showRequired} /> 
	{/if}

	<div class="relative">
		{#if icon}
			<div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none z-10">
				<Icon
					{icon}
					class={getIconSize() + ' ' + (displayError ? 'text-error' : 'text-primary')}
				/>
			</div>
		{/if}

		<input
			type={inputType()}
		   	bind:value={value}	
			{placeholder}
			{disabled}
			{readonly}
			{id}
			{name}
			autocomplete={autocomplete as any}
			{min}
			{max}
			{step}
			{maxlength}
			{pattern}
			class={`${classes} ${icon ? 'pl-10' : ''} ${iconRight || (type === 'password' && showPasswordToggle) ? 'pr-10' : ''}`}
			{oninput}
			onchange={type === 'number' && formatNumber ? handleNumberChange : onchange}
			{onfocus}
			{onblur}
			{onkeydown}
			{...restProps}
		/>

		{#if type === 'password' && showPasswordToggle}
			<button
				type="button"
				class="absolute inset-y-0 right-0 pr-3 z-10 flex items-center text-base-content/50 hover:text-base-content transition-colors"
				onclick={togglePasswordVisibility}
				tabindex="-1"
			>
				<Icon icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'} class={getIconSize()} />
			</button>
		{:else if iconRight}
			<div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
				<Icon icon={iconRight} class={getIconSize() + ' text-base-content/50'} />
			</div>
		{/if}
	</div>

	{#if displayError}
		<label for="error" class="label">
			<span class="label-text-alt text-error">{displayError}</span>
		</label>
	{/if}

	{#if info && !displayError}
		<label for="info" class="label">
			<span class="label-text-alt text-info">{info}</span>
		</label>
	{/if}
</div>

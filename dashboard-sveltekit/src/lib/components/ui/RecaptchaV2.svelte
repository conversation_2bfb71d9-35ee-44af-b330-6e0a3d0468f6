<!-- reCAPTCHA v2 Component - Simplified and focused -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { getSiteKey } from '$lib/config/recaptcha';

	interface Props {
		siteKey?: string;
		theme?: 'light' | 'dark';
		size?: 'normal' | 'compact';
		language?: string;
		tabindex?: number;
		callback?: (token: string) => void;
		expiredCallback?: () => void;
		errorCallback?: () => void;
		disabled?: boolean;
	}

	const {
		siteKey = getSiteKey(),
		theme = 'light',
		size = 'normal',
		language = 'th',
		tabindex = 0,
		callback,
		expiredCallback,
		errorCallback,
		disabled = false,
	}: Props = $props();

	let container: HTMLDivElement;
	let widgetId: number | null = null;
	let isLoaded = false;

	// ฟังก์ชันสำหรับแปลงภาษาให้ตรงกับ reCAPTCHA
	function getRecaptchaLanguage(lang: string): string {
		const languageMap: Record<string, string> = {
			th: 'th',
			en: 'en',
			lo: 'lo',
		};
		return languageMap[lang] || 'en';
	}

	// โหลด reCAPTCHA script
	function loadRecaptchaScript(): Promise<void> {
		return new Promise((resolve, reject) => {
			// ตรวจสอบว่ามี script อยู่แล้วหรือไม่
			if ((window as any).grecaptcha && (window as any).grecaptcha.render) {
				console.log('[reCAPTCHA v2] grecaptcha already available');
				resolve();
				return;
			}

			const existingScript = document.querySelector('script[src*="recaptcha/api.js"]');
			if (existingScript) {
				console.log('[reCAPTCHA v2] Script already exists, waiting for grecaptcha...');
				const checkGrecaptcha = () => {
					if ((window as any).grecaptcha && (window as any).grecaptcha.render) {
						console.log('[reCAPTCHA v2] grecaptcha ready (existing script)');
						resolve();
					} else {
						setTimeout(checkGrecaptcha, 100);
					}
				};
				checkGrecaptcha();
				return;
			}

			console.log('[reCAPTCHA v2] Loading script...');
			const script = document.createElement('script');
			script.src = `https://www.google.com/recaptcha/api.js?hl=${getRecaptchaLanguage(language)}`;
			script.async = true;
			script.defer = true;
			script.onload = () => {
				console.log('[reCAPTCHA v2] Script loaded, waiting for grecaptcha...');
				const checkGrecaptcha = () => {
					if ((window as any).grecaptcha && (window as any).grecaptcha.render) {
						console.log('[reCAPTCHA v2] grecaptcha ready');
						resolve();
					} else {
						setTimeout(checkGrecaptcha, 100);
					}
				};
				checkGrecaptcha();
			};
			script.onerror = (error) => {
				console.error('[reCAPTCHA v2] Failed to load script:', error);
				reject(error);
			};
			document.head.appendChild(script);
		});
	}

	// สร้าง reCAPTCHA v2 widget
	function createRecaptcha() {
		if (!(window as any).grecaptcha || !(window as any).grecaptcha.render || !container) {
			console.error('[reCAPTCHA v2] grecaptcha or container not available');
			return;
		}

		// ตรวจสอบ siteKey
		if (!siteKey || siteKey.trim() === '') {
			console.error('[reCAPTCHA v2] siteKey is missing or empty:', siteKey);
			if (errorCallback) errorCallback();
			return;
		}

		console.log('[reCAPTCHA v2] Creating widget with siteKey:', siteKey.substring(0, 15) + '...');

		try {
			widgetId = (window as any).grecaptcha.render(container, {
				sitekey: siteKey,
				theme: theme,
				size: size,
				tabindex: tabindex,
				callback: (token: string) => {
					console.log('[reCAPTCHA v2] Token received:', token.substring(0, 50) + '...');
					if (callback) callback(token);
				},
				'expired-callback': () => {
					console.log('[reCAPTCHA v2] Token expired');
					if (expiredCallback) expiredCallback();
				},
				'error-callback': () => {
					console.error('[reCAPTCHA v2] Error occurred');
					if (errorCallback) errorCallback();
				},
			});
			console.log('[reCAPTCHA v2] Widget created successfully with ID:', widgetId);
		} catch (error) {
			console.error('[reCAPTCHA v2] Error creating widget:', error);
			if (errorCallback) errorCallback();
		}
	}

	// รีเซ็ต reCAPTCHA
	export function reset(): void {
		if (widgetId !== null && (window as any).grecaptcha && (window as any).grecaptcha.reset) {
			console.log('[reCAPTCHA v2] Resetting widget');
			(window as any).grecaptcha.reset(widgetId);
		}
	}

	// รับ response token
	export function getResponse(): string {
		if (widgetId !== null && (window as any).grecaptcha && (window as any).grecaptcha.getResponse) {
			return (window as any).grecaptcha.getResponse(widgetId);
		}
		return '';
	}

	onMount(async () => {
		if (disabled) return;

		// ตรวจสอบ siteKey ก่อน
		if (!siteKey || siteKey.trim() === '') {
			console.error('[reCAPTCHA v2] siteKey is missing. Please set VITE_RECAPTCHA_SITE_KEY in your .env file');
			if (errorCallback) errorCallback();
			return;
		}

		try {
			await loadRecaptchaScript();
			isLoaded = true;
			
			// รอสักครู่แล้วสร้าง widget
			setTimeout(() => {
				createRecaptcha();
			}, 100);
		} catch (error) {
			console.error('[reCAPTCHA v2] Error loading:', error);
			if (errorCallback) errorCallback();
		}
	});
</script>

<div
	bind:this={container}
	class="g-recaptcha-v2"
	class:disabled
>
	{#if !siteKey || siteKey.trim() === ''}
		<!-- แสดงข้อความแจ้งเตือนเมื่อไม่มี siteKey -->
		<div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
			<div class="flex">
				<div class="flex-shrink-0">
					<svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-yellow-800">
						reCAPTCHA v2 ไม่พร้อมใช้งาน
					</h3>
					<div class="mt-2 text-sm text-yellow-700">
						<p>กรุณาตั้งค่า VITE_RECAPTCHA_SITE_KEY ในไฟล์ .env</p>
						<p class="mt-1">สำหรับ development สามารถใช้ test key: 6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI</p>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.disabled {
		opacity: 0.5;
		pointer-events: none;
	}

	/* ซ่อน reCAPTCHA badge */
	:global(.grecaptcha-badge) {
		visibility: hidden !important;
		opacity: 0 !important;
		pointer-events: none !important;
	}

	/* Responsive design */
	@media only screen and (max-width: 500px) {
		.g-recaptcha-v2 {
			transform: scale(0.77);
			transform-origin: 0 0;
		}
	}
</style>
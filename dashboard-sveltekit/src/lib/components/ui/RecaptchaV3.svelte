<!-- reCAPTCHA v3 Component - Simplified and focused -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { getSiteKey } from '$lib/config/recaptcha';

	interface Props {
		siteKey?: string;
		action?: string;
		language?: string;
		callback?: (token: string) => void;
		errorCallback?: () => void;
		disabled?: boolean;
		autoExecute?: boolean; // เรียก execute อัตโนมัติเมื่อ mount
	}

	const {
		siteKey = getSiteKey(),
		action = 'submit',
		language = 'th',
		callback,
		errorCallback,
		disabled = false,
		autoExecute = false,
	}: Props = $props();

	let isLoaded = false;
	let isExecuting = false;

	// ฟังก์ชันสำหรับแปลงภาษาให้ตรงกับ reCAPTCHA
	function getRecaptchaLanguage(lang: string): string {
		const languageMap: Record<string, string> = {
			th: 'th',
			en: 'en',
			lo: 'lo',
		};
		return languageMap[lang] || 'en';
	}

	// โหลด reCAPTCHA v3 script
	function loadRecaptchaScript(): Promise<void> {
		return new Promise((resolve, reject) => {
			// ตรวจสอบว่ามี grecaptcha อยู่แล้วหรือไม่
			if ((window as any).grecaptcha && (window as any).grecaptcha.execute) {
				console.log('[reCAPTCHA v3] grecaptcha already available');
				resolve();
				return;
			}

			const existingScript = document.querySelector(`script[src*="render=${siteKey}"]`);
			if (existingScript) {
				console.log('[reCAPTCHA v3] Script already exists, waiting for grecaptcha...');
				const checkGrecaptcha = () => {
					if ((window as any).grecaptcha && (window as any).grecaptcha.execute) {
						console.log('[reCAPTCHA v3] grecaptcha ready (existing script)');
						resolve();
					} else {
						setTimeout(checkGrecaptcha, 100);
					}
				};
				checkGrecaptcha();
				return;
			}

			console.log('[reCAPTCHA v3] Loading script...');
			const script = document.createElement('script');
			script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}&hl=${getRecaptchaLanguage(language)}`;
			script.async = true;
			script.defer = true;
			script.onload = () => {
				console.log('[reCAPTCHA v3] Script loaded, waiting for grecaptcha...');
				const checkGrecaptcha = () => {
					if ((window as any).grecaptcha && (window as any).grecaptcha.execute) {
						console.log('[reCAPTCHA v3] grecaptcha ready');
						resolve();
					} else {
						setTimeout(checkGrecaptcha, 100);
					}
				};
				checkGrecaptcha();
			};
			script.onerror = (error) => {
				console.error('[reCAPTCHA v3] Failed to load script:', error);
				reject(error);
			};
			document.head.appendChild(script);
		});
	}

	// Execute reCAPTCHA v3
	export async function execute(): Promise<string> {
		if (disabled || isExecuting) {
			throw new Error('reCAPTCHA v3 is disabled or already executing');
		}

		if (!isLoaded) {
			await loadRecaptchaScript();
			isLoaded = true;
		}

		// ตรวจสอบ siteKey
		if (!siteKey || siteKey.trim() === '') {
			console.error('[reCAPTCHA v3] siteKey is missing or empty');
			if (errorCallback) errorCallback();
			throw new Error('reCAPTCHA v3 siteKey is missing or empty');
		}

		isExecuting = true;

		try {
			console.log('[reCAPTCHA v3] Executing with action:', action);
			const token = await (window as any).grecaptcha.execute(siteKey, {
				action: action,
			});
			
			console.log('[reCAPTCHA v3] Token received:', token.substring(0, 50) + '...');
			
			if (callback) callback(token);
			return token;
		} catch (error) {
			console.error('[reCAPTCHA v3] Error executing:', error);
			if (errorCallback) errorCallback();
			throw error;
		} finally {
			isExecuting = false;
		}
	}

	// รีเซ็ต (สำหรับ v3 จะเป็นการ execute ใหม่)
	export async function reset(): Promise<void> {
		console.log('[reCAPTCHA v3] Resetting (executing new token)');
		await execute();
	}

	onMount(async () => {
		if (disabled) return;

		// ตรวจสอบ siteKey ก่อน
		if (!siteKey || siteKey.trim() === '') {
			console.error('[reCAPTCHA v3] siteKey is missing. Please set VITE_RECAPTCHA_SITE_KEY in your .env file');
			if (errorCallback) errorCallback();
			return;
		}

		try {
			await loadRecaptchaScript();
			isLoaded = true;
			
			// Execute อัตโนมัติถ้าต้องการ
			if (autoExecute) {
				setTimeout(() => {
					execute().catch(console.error);
				}, 100);
			}
		} catch (error) {
			console.error('[reCAPTCHA v3] Error loading:', error);
			if (errorCallback) errorCallback();
		}
	});
</script>

<!-- reCAPTCHA v3 ไม่มี UI แต่จะแสดง badge -->
<div class="g-recaptcha-v3" class:disabled>
	{#if !siteKey || siteKey.trim() === ''}
		<!-- แสดงข้อความแจ้งเตือนเมื่อไม่มี siteKey -->
		<div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
			<div class="flex">
				<div class="flex-shrink-0">
					<svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-yellow-800">
						reCAPTCHA v3 ไม่พร้อมใช้งาน
					</h3>
					<div class="mt-2 text-sm text-yellow-700">
						<p>กรุณาตั้งค่า VITE_RECAPTCHA_SITE_KEY ในไฟล์ .env</p>
						<p class="mt-1">สำหรับ development สามารถใช้ test key: 6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI</p>
					</div>
				</div>
			</div>
		</div>
	{:else if isExecuting}
		<!-- แสดงสถานะ loading เมื่อกำลัง execute -->
		<div class="text-sm text-gray-500">
			กำลังตรวจสอบ reCAPTCHA...
		</div>
	{/if}
</div>

<style>
	.disabled {
		opacity: 0.5;
		pointer-events: none;
	}

	.g-recaptcha-v3 {
		/* v3 ไม่มี UI แต่อาจจะมี badge */
		min-height: 0;
	}
</style>
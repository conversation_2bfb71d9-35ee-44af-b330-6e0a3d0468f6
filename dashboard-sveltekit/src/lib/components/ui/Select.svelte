<script lang="ts">
	import Icon from '@iconify/svelte';
	import Label from './Label.svelte';

	interface Option {
		value: string | number;
		label: string;
		disabled?: boolean;
		icon?: string;
		description?: string;
	}

	interface Props {
		options?: Option[];
		value?: string | number | (string | number)[];
		placeholder?: string;
		multiple?: boolean;
		disabled?: boolean;
		required?: boolean;
		readonly?: boolean;
		error?: string;
		info?: string;
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		color?: 'ghost' | 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
		variant?: 'bordered' | 'soft' | 'ghost' | 'outlined';
		rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
		icon?: string;
		iconRight?: string;
		class?: string;
		id?: string;
		name?: string;
		label?: string;
		helpText?: string;
		loading?: boolean;
		autofocus?: boolean;
		form?: string;
		tabindex?: number;
		// Event handlers
		onchange?: (value: string | number | (string | number)[] | undefined) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
		onkeydown?: (event: KeyboardEvent) => void;
		// Validation
		validate?: string;
		showRequired?: boolean;
		children?: any;
	}

	let {
		options = [],
		value = $bindable(''),
		placeholder = 'เลือกตัวเลือก...',
		multiple = false,
		disabled = false,
		required = false,
		readonly = false,
		error,
		info,
		size = 'md',
		color,
		variant = 'bordered',
		rounded = 'sm',
		icon,
		iconRight = 'mdi:chevron-down',
		class: className = '',
		id,
		name,
		label,
		helpText,
		loading = false,
		autofocus = false,
		form,
		tabindex,
		onchange,
		onfocus,
		onblur,
		onkeydown,
		validate,
		showRequired = false,
		children,
		...restProps
	}: Props = $props();

	// State
	let selectedValue = $state(value);

	// Get icon size based on select size
	function getIconSize() {
		switch (size) {
			case 'xs':
				return 'size-4';
			case 'sm':
				return 'size-5';
			case 'lg':
				return 'size-7';
			case 'xl':
				return 'size-8';
			default: // md
				return 'size-6';
		}
	}

	// Combine error and validate
	const displayError = $derived(error || validate);

	// Effects
	$effect(() => {
		selectedValue = value;
	});

	$effect(() => {
		if (selectedValue !== value && selectedValue !== undefined) {
			onchange?.(selectedValue);
		}
	});

	// Computed
	const baseClasses = 'select w-full';

	const classes = $derived(
		[
			baseClasses,
			// Style classes
			variant === 'bordered' ? '!select-bordered' : '',
			variant === 'soft' ? '!select-soft' : '',
			variant === 'ghost' ? '!select-ghost' : '',
			variant === 'outlined' ? '!select-outlined' : '',
			// Rounded classes
			rounded === 'none' ? '!rounded-none' : '',
			rounded === 'sm' ? '!rounded-sm' : '',
			rounded === 'md' ? '!rounded-md' : '',
			rounded === 'lg' ? '!rounded-lg' : '',
			rounded === 'full' ? '!rounded-full' : '',
			// Variant classes (only if variant is provided)
			color ? `select-${color}` : '',
			size !== 'md' ? `select-${size}` : '',
			displayError ? 'select-error border-error focus:border-error' : '',
			loading ? 'select-disabled' : '',
			className,
		]
			.filter(Boolean)
			.join(' ')
	);

	const wrapperClasses = $derived(
		['w-full', icon || iconRight ? 'relative' : ''].filter(Boolean).join(' ')
	);

	// Functions
	function handleChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		if (multiple) {
			const selectedOptions = Array.from(target.selectedOptions).map(option => option.value);
			selectedValue = selectedOptions;
		} else {
			selectedValue = target.value;
		}
		onchange?.(selectedValue);
	}

	function handleFocus(event: FocusEvent) {
		onfocus?.(event);
	}

	function handleBlur(event: FocusEvent) {
		onblur?.(event);
	}

	function handleKeydown(event: KeyboardEvent) {
		onkeydown?.(event);
	}

	function getDisplayValue() {
		if (!selectedValue) return placeholder;

		if (multiple && Array.isArray(selectedValue)) {
			const selectedLabels = selectedValue.map(
				val => options.find(opt => opt.value === val)?.label || val
			);
			return selectedLabels.join(', ');
		} else {
			const option = options.find(opt => opt.value === selectedValue);
			return option?.label || selectedValue;
		}
	}
</script>

<div class={wrapperClasses}>
	{#if label}
		<Label validate={displayError} for={id} text={label} required={showRequired} />
	{/if}

	<div class="relative">
		{#if icon}
			<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
				<Icon
					{icon}
					class={getIconSize() + ' ' + (displayError ? 'text-error' : 'text-primary')}
				/>
			</div>
		{/if}

		<select
			{id}
			{name}
			class={`${classes} ${icon ? 'pl-10' : ''} ${iconRight ? 'pr-10' : ''}`}
			{disabled}
			{required}
			{readonly}
			{form}
			{tabindex}
			{autofocus}
			bind:value={selectedValue}
			onchange={handleChange}
			onfocus={handleFocus}
			onblur={handleBlur}
			onkeydown={handleKeydown}
			{...restProps}
		>
			{#if children}
				{@render children()}
			{:else}
				{#if placeholder && !required}
					<option value="" disabled selected={!selectedValue}>
						{placeholder}
					</option>
				{/if}

				{#each options as option}
					<option
						value={option.value}
						disabled={option.disabled}
						selected={multiple
							? Array.isArray(selectedValue) && selectedValue.includes(option.value)
							: selectedValue === option.value}
					>
						{option.label}
						{#if option.description}
							- {option.description}
						{/if}
					</option>
				{/each}
			{/if}
		</select>

		{#if iconRight}
			<div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
				<Icon icon={iconRight} class={getIconSize() + ' text-base-content/50'} />
			</div>
		{/if}

		{#if loading}
			<div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
				<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
			</div>
		{/if}
	</div>

	{#if displayError}
		<label for="error" class="label">
			<span class="label-text-alt text-error">{displayError}</span>
		</label>
	{:else if info && !displayError}
		<label for="info" class="label">
			<span class="label-text-alt text-info">{info}</span>
		</label>
	{:else if helpText}
		<label for={id} class="label">
			<span class="label-text-alt text-base-content/70">{helpText}</span>
		</label>
	{/if}
</div>

<style>
	select {
		appearance: none;
		background-image: none;
	}

	select:focus {
		outline: none;
		box-shadow: 0 0 0 2px hsl(var(--p) / 0.2);
	}

	select option {
		padding: 0.5rem;
	}

	select option:checked {
		background-color: hsl(var(--p));
		color: hsl(var(--pc));
	}

	select option:hover {
		background-color: hsl(var(--p) / 0.1);
	}

	.select-xs {
		height: 1.5rem;
		min-height: 1.5rem;
		padding-left: 0.5rem;
		padding-right: 0.5rem;
		font-size: 0.75rem;
	}

	.select-sm {
		height: 2rem;
		min-height: 2rem;
		padding-left: 0.75rem;
		padding-right: 0.75rem;
		font-size: 0.875rem;
	}

	.select-md {
		height: 2.5rem;
		min-height: 2.5rem;
		padding-left: 1rem;
		padding-right: 1rem;
		font-size: 1rem;
	}

	.select-lg {
		height: 3rem;
		min-height: 3rem;
		padding-left: 1.25rem;
		padding-right: 1.25rem;
		font-size: 1.125rem;
	}

	.select-xl {
		height: 3.5rem;
		min-height: 3.5rem;
		padding-left: 1.5rem;
		padding-right: 1.5rem;
		font-size: 1.25rem;
	}
</style>

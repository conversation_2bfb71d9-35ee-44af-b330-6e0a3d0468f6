<script lang="ts">
	import Icon from '@iconify/svelte';
	import Label from './Label.svelte';

	interface Props {
		value?: string;
		placeholder?: string;
		label?: string;
		error?: string;
		info?: string;
		disabled?: boolean;
		required?: boolean;
		readonly?: boolean;
		rows?: number;
		maxLength?: number;
		minLength?: number;
		autoResize?: boolean;
		showCharacterCount?: boolean;
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		color?: 'ghost' | 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
		variant?: 'bordered' | 'soft' | 'ghost' | 'outlined';
		rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
		icon?: string;
		iconRight?: string;
		class?: string;
		id?: string;
		name?: string;
		// Event handlers
		oninput?: (event: Event) => void;
		onchange?: (event: Event) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
		onkeydown?: (event: KeyboardEvent) => void;
		// Validation
		validate?: string;
		showRequired?: boolean;
	}

	let {
		value = $bindable(''),
		placeholder,
		label,
		error,
		info,
		disabled = false,
		required = false,
		readonly = false,
		rows = 3,
		maxLength,
		minLength,
		autoResize = false,
		showCharacterCount = false,
		size = 'md',
		color,
		variant = 'bordered',
		rounded = 'sm',
		icon,
		iconRight,
		class: className = '',
		id,
		name,
		oninput,
		onchange,
		onfocus,
		onblur,
		onkeydown,
		validate,
		showRequired = false,
		...restProps
	}: Props = $props();

	// Internal state
	let textareaElement: HTMLTextAreaElement;
	let characterCount = $state(0);

	// Get icon size based on textarea size
	function getIconSize() {
		switch (size) {
			case 'xs':
				return 'size-4';
			case 'sm':
				return 'size-5';
			case 'lg':
				return 'size-7';
			case 'xl':
				return 'size-8';
			default: // md
				return 'size-6';
		}
	}

	// Combine error and validate
	const displayError = $derived(error || validate);

	// Computed values
	const baseClasses = 'textarea w-full resize-none';

	const classes = $derived(
		[
			baseClasses,
			// Style classes
			variant === 'bordered' ? '!textarea-bordered' : '',
			variant === 'soft' ? '!textarea-soft' : '',
			variant === 'ghost' ? '!textarea-ghost' : '',
			variant === 'outlined' ? '!textarea-outlined' : '',
			// Rounded classes
			rounded === 'none' ? '!rounded-none' : '',
			rounded === 'sm' ? '!rounded-sm' : '',
			rounded === 'md' ? '!rounded-md' : '',
			rounded === 'lg' ? '!rounded-lg' : '',
			rounded === 'full' ? '!rounded-full' : '',
			// Variant classes (only if variant is provided)
			color ? `textarea-${color}` : '',
			size !== 'md' ? `textarea-${size}` : '',
			displayError ? 'textarea-error border-error focus:border-error' : '',
			className,
		]
			.filter(Boolean)
			.join(' ')
	);

	const wrapperClasses = $derived(
		['w-full', icon || iconRight ? 'relative' : ''].filter(Boolean).join(' ')
	);

	const isOverLimit = $derived(maxLength ? characterCount > maxLength : false);
	const remainingChars = $derived(maxLength ? maxLength - characterCount : 0);

	// Functions
	function handleInput(event: Event) {
		const target = event.target as HTMLTextAreaElement;
		characterCount = target.value.length;

		if (autoResize) {
			resizeTextarea();
		}

		oninput?.(event);
	}

	function handleChange(event: Event) {
		onchange?.(event);
	}

	function handleFocus(event: FocusEvent) {
		onfocus?.(event);
	}

	function handleBlur(event: FocusEvent) {
		onblur?.(event);
	}

	function handleKeydown(event: KeyboardEvent) {
		onkeydown?.(event);
	}

	function resizeTextarea() {
		if (textareaElement) {
			textareaElement.style.height = 'auto';
			textareaElement.style.height = textareaElement.scrollHeight + 'px';
		}
	}

	// Effects
	$effect(() => {
		if (autoResize && textareaElement) {
			resizeTextarea();
		}
	});

	$effect(() => {
		characterCount = value.length;
	});
</script>

<div class={wrapperClasses}>
	{#if label}
		<Label validate={displayError} for={id} text={label} required={showRequired} />
	{/if}

	<div class="relative">
		{#if icon}
			<div class="absolute top-3 left-3 flex items-start pointer-events-none z-10">
				<Icon
					{icon}
					class={getIconSize() + ' ' + (displayError ? 'text-error' : 'text-primary')}
				/>
			</div>
		{/if}

		<textarea
			bind:this={textareaElement}
			bind:value={value}
			{name}
			{placeholder}
			{disabled}
			{required}
			{readonly}
			{rows}
			maxlength={maxLength}
			minlength={minLength}
			{id}
			class={`${classes} ${icon ? 'pl-10' : ''} ${iconRight ? 'pr-10' : ''}`}
			oninput={handleInput}
			onchange={handleChange}
			onfocus={handleFocus}
			onblur={handleBlur}
			onkeydown={handleKeydown}
			{...restProps}
		></textarea>

		{#if iconRight}
			<div class="absolute top-3 right-3 flex items-start pointer-events-none">
				<Icon icon={iconRight} class={getIconSize() + ' text-base-content/50'} />
			</div>
		{/if}

		{#if showCharacterCount && maxLength}
			<div class="absolute bottom-2 right-2 text-xs text-base-content/50">
				<span class={isOverLimit ? 'text-error' : ''}>
					{characterCount}/{maxLength}
				</span>
			</div>
		{/if}
	</div>

	{#if displayError}
		<label for="error" class="label">
			<span class="label-text-alt text-error">{displayError}</span>
		</label>
	{:else if info && !displayError}
		<label for="info" class="label">
			<span class="label-text-alt text-info">{info}</span>
		</label>
	{:else if showCharacterCount && maxLength && remainingChars <= 10}
		<div class="label">
			<span class="label-text-alt {isOverLimit ? 'text-error' : 'text-warning'}">
				เหลือ {remainingChars} ตัวอักษร
			</span>
		</div>
	{/if}
</div>

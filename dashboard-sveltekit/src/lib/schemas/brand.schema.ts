import { z } from 'zod';

// Base brand schema
export const brandSchema = z.object({
  _id: z.string().optional(),
  siteId: z.string().min(1, 'Site ID จำเป็น'),
  name: z.string().min(1, 'กรุณากรอกชื่อแบรนด์'),
  description: z.string().optional(),
  logoUrl: z.string().url('URL โลโก้ไม่ถูกต้อง').optional().or(z.literal('')),
  websiteUrl: z.string().url('URL เว็บไซต์ไม่ถูกต้อง').optional().or(z.literal('')),
  isActive: z.boolean().default(true),
  productCount: z.number().min(0).default(0),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Create brand schema
export const createBrandSchema = brandSchema.omit({
  _id: true,
  productCount: true,
  createdAt: true,
  updatedAt: true,
});

// Update brand schema
export const updateBrandSchema = brandSchema.omit({
  siteId: true,
  productCount: true,
  createdAt: true,
  updatedAt: true,
}).partial();

// Delete brand schema
export const deleteBrandSchema = z.object({
  brandId: z.string().min(1, 'กรุณาระบุ Brand ID'),
});

// Filter brands schema
export const brandFilterSchema = z.object({
  search: z.string().optional(),
  isActive: z.boolean().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

// Brand stats schema
export const brandStatsSchema = z.object({
  total: z.number(),
  active: z.number(),
  inactive: z.number(),
  totalProducts: z.number(),
  topBrands: z.array(z.object({
    _id: z.string(),
    name: z.string(),
    productCount: z.number(),
  })),
});

// Type inference
export type Brand = z.infer<typeof brandSchema>;
export type CreateBrandData = z.infer<typeof createBrandSchema>;
export type UpdateBrandData = z.infer<typeof updateBrandSchema>;
export type DeleteBrandData = z.infer<typeof deleteBrandSchema>;
export type BrandFilterData = z.infer<typeof brandFilterSchema>;
export type BrandStatsData = z.infer<typeof brandStatsSchema>;

// Validation functions
export function validateCreateBrandData(data: unknown) {
  try {
    const validatedData = createBrandSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateUpdateBrandData(data: unknown) {
  try {
    const validatedData = updateBrandSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateDeleteBrandData(data: unknown) {
  try {
    const validatedData = deleteBrandSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateBrandFilterData(data: unknown) {
  try {
    const validatedData = brandFilterSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

// Sanitization functions
export function sanitizeBrandData(data: any): CreateBrandData | UpdateBrandData {
  return {
    siteId: data.siteId?.toString().trim() || '',
    name: data.name?.toString().trim() || '',
    description: data.description?.toString().trim() || undefined,
    logoUrl: data.logoUrl?.toString().trim() || undefined,
    websiteUrl: data.websiteUrl?.toString().trim() || undefined,
    isActive: Boolean(data.isActive),
    ...(data._id && { _id: data._id.toString() }),
  };
}

// Helper functions
export function getBrandStatusColor(isActive: boolean): string {
  return isActive ? 'badge-success' : 'badge-neutral';
}

export function getBrandStatusText(isActive: boolean): string {
  return isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
}

export function formatBrandDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

export function canDeleteBrand(brand: Brand): boolean {
  return brand.productCount === 0;
}

export function getDeleteBrandMessage(brand: Brand): string {
  if (brand.productCount > 0) {
    return `ไม่สามารถลบแบรนด์ "${brand.name}" ได้ เนื่องจากมีสินค้า ${brand.productCount} รายการ`;
  }
  return `คุณแน่ใจหรือไม่ที่จะลบแบรนด์ "${brand.name}"?`;
}

export function validateBrandUrl(url: string): boolean {
  if (!url) return true; // Optional field
  try {
    new URL(url);
    return true;
  }
  catch {
    return false;
  }
}

export function getBrandInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

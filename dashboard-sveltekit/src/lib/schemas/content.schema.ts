// ✅ Content Schema - Zod Validation for Blogs, News, Novels
import { z } from 'zod';

// ✅ Common Content Schemas
const baseContentSchema = z.object({
  title: z.string()
    .min(1, 'กรุณากรอกหัวข้อ')
    .max(200, 'หัวข้อต้องไม่เกิน 200 ตัวอักษร'),
  content: z.string()
    .min(1, 'กรุณากรอกเนื้อหา')
    .max(50000, 'เนื้อหาต้องไม่เกิน 50,000 ตัวอักษร'),
  excerpt: z.string()
    .max(500, 'สรุปเนื้อหาต้องไม่เกิน 500 ตัวอักษร')
    .optional(),
  published: z.boolean().default(false),
  featuredImage: z.string()
    .url('รูปแบบ URL ไม่ถูกต้อง')
    .optional()
    .or(z.literal('')),
});

// ✅ Blog Schemas
export const createBlogSchema = baseContentSchema.extend({
  tags: z.array(z.string()).max(10, 'แท็กต้องไม่เกิน 10 รายการ').optional(),
});

export const updateBlogSchema = createBlogSchema.partial().extend({
  id: z.string().min(1, 'กรุณาระบุ ID'),
});

// ✅ News Schemas
export const createNewsSchema = baseContentSchema.extend({
  categoryId: z.string().optional(),
});

export const updateNewsSchema = createNewsSchema.partial().extend({
  id: z.string().min(1, 'กรุณาระบุ ID'),
});

// ✅ Novel Schemas
export const createNovelSchema = z.object({
  title: z.string()
    .min(1, 'กรุณากรอกชื่อเรื่อง')
    .max(200, 'ชื่อเรื่องต้องไม่เกิน 200 ตัวอักษร'),
  description: z.string()
    .min(1, 'กรุณากรอกคำอธิบายเรื่อง')
    .max(1000, 'คำอธิบายต้องไม่เกิน 1,000 ตัวอักษร'),
  genre: z.string().max(50, 'ประเภทต้องไม่เกิน 50 ตัวอักษร').optional(),
  tags: z.array(z.string()).max(10, 'แท็กต้องไม่เกิน 10 รายการ').optional(),
  published: z.boolean().default(false),
  coverImage: z.string()
    .url('รูปแบบ URL ไม่ถูกต้อง')
    .optional()
    .or(z.literal('')),
});

export const updateNovelSchema = createNovelSchema.partial().extend({
  id: z.string().min(1, 'กรุณาระบุ ID'),
});

// ✅ Type inference from schemas
export type CreateBlogData = z.infer<typeof createBlogSchema>;
export type UpdateBlogData = z.infer<typeof updateBlogSchema>;
export type CreateNewsData = z.infer<typeof createNewsSchema>;
export type UpdateNewsData = z.infer<typeof updateNewsSchema>;
export type CreateNovelData = z.infer<typeof createNovelSchema>;
export type UpdateNovelData = z.infer<typeof updateNovelSchema>;

// ✅ Validation Functions using Zod
export function validateCreateBlogData(data: unknown): {
  success: boolean;
  data?: CreateBlogData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createBlogSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateCreateNewsData(data: unknown): {
  success: boolean;
  data?: CreateNewsData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createNewsSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateCreateNovelData(data: unknown): {
  success: boolean;
  data?: CreateNovelData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createNovelSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

// ✅ Type Guards using Zod
export function isValidCreateBlogData(data: unknown): data is CreateBlogData {
  return createBlogSchema.safeParse(data).success;
}

export function isValidCreateNewsData(data: unknown): data is CreateNewsData {
  return createNewsSchema.safeParse(data).success;
}

export function isValidCreateNovelData(data: unknown): data is CreateNovelData {
  return createNovelSchema.safeParse(data).success;
}

// ✅ Sanitization Functions
export function sanitizeBlogData(data: CreateBlogData): CreateBlogData {
  const sanitized = { ...data };

  // Sanitize string fields
  if (sanitized.title) {
    sanitized.title = sanitized.title.trim();
  }
  if (sanitized.content) {
    sanitized.content = sanitized.content.trim();
  }
  if (sanitized.excerpt) {
    sanitized.excerpt = sanitized.excerpt.trim();
  }
  if (sanitized.featuredImage) {
    sanitized.featuredImage = sanitized.featuredImage.trim();
  }

  // Sanitize tags
  if (sanitized.tags) {
    sanitized.tags = sanitized.tags
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  return sanitized;
}

export function sanitizeNewsData(data: CreateNewsData): CreateNewsData {
  const sanitized = { ...data };

  // Sanitize string fields
  if (sanitized.title) {
    sanitized.title = sanitized.title.trim();
  }
  if (sanitized.content) {
    sanitized.content = sanitized.content.trim();
  }
  if (sanitized.excerpt) {
    sanitized.excerpt = sanitized.excerpt.trim();
  }
  if (sanitized.featuredImage) {
    sanitized.featuredImage = sanitized.featuredImage.trim();
  }
  if (sanitized.categoryId) {
    sanitized.categoryId = sanitized.categoryId.trim();
  }

  return sanitized;
}

export function sanitizeNovelData(data: CreateNovelData): CreateNovelData {
  const sanitized = { ...data };

  // Sanitize string fields
  if (sanitized.title) {
    sanitized.title = sanitized.title.trim();
  }
  if (sanitized.description) {
    sanitized.description = sanitized.description.trim();
  }
  if (sanitized.genre) {
    sanitized.genre = sanitized.genre.trim();
  }
  if (sanitized.coverImage) {
    sanitized.coverImage = sanitized.coverImage.trim();
  }

  // Sanitize tags
  if (sanitized.tags) {
    sanitized.tags = sanitized.tags
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  return sanitized;
}

// ✅ Helper Functions
export function parseTagsFromString(tagsString: string): string[] {
  return tagsString
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
}

export function formatTagsForDisplay(tags: string[]): string {
  return tags.join(', ');
}

export function generateExcerpt(content: string, maxLength: number = 200): string {
  const plainText = content.replace(/<[^>]*>/g, '');
  return plainText.length > maxLength
    ? plainText.substring(0, maxLength) + '...'
    : plainText;
}

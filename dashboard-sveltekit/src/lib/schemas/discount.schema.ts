// ✅ Site Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
// ✅ Discount Validation Schema
export const checkDiscountSchema = z.object({
  discountCode: z
    .string()
    .min(1, 'กรุณากรอกรหัสส่วนลด')
    .min(2, 'รหัสส่วนลดต้องมีอย่างน้อย 2 ตัวอักษร')
    .max(50, 'รหัสส่วนลดต้องไม่เกิน 50 ตัวอักษร'),
  orderAmount: z
    .number()
    .positive('ยอดสั่งซื้อต้องมากกว่า 0')
    .min(1, 'ยอดสั่งซื้อต้องมากกว่า 0'),
  target: z.enum(['all', 'package', 'category', 'product', 'user_group', 'first_time']).optional(),
});

export type CheckDiscountData = z.infer<typeof checkDiscountSchema>;

export function validateCheckDiscountData(data: unknown): {
  success: boolean;
  data?: CheckDiscountData;
  errors?: Record<string, string>;
} {
  try {
    const validatedData = checkDiscountSchema.parse(data);
    return { success: true, data: validatedData };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.issues.forEach((err: any) => {
        if (err.path) {
          errors[err.path.join('.')] = err.message;
        }
      });
      return { success: false, errors };
    }
    return { success: false, errors: { general: 'ข้อมูลไม่ถูกต้อง' } };
  }
}

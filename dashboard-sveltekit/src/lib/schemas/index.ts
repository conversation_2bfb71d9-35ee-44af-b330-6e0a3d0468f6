// ✅ Schemas Index - Export ทุกอย่างจากที่เดียว
import { z } from 'zod';

// ✅ Re-export specific types to avoid conflicts
export type {
  AuthResponse,
  ChangePasswordData as AuthChangePasswordData,
  ForgotPasswordData,
  ProfileUpdateData as AuthProfileUpdateData,
  RefreshTokenResponse,
  ResetPasswordData,
  // Auth Types
  SigninData,
  SignupData,
  VerifyEmailData,
} from './auth.schema';
// ✅ Export schemas (avoiding conflicts)
// ✅ Re-export validation functions
export {
  changePasswordSchema as authChangePasswordSchema,
  forgotPasswordSchema,
  isValidEmail as isValidAuthEmail,
  loginSchema,
  passwordResetSchema,
  profileUpdateSchema,
  registerSchema,
  resetPasswordSchema,
  sanitizeAuthData,
  signinSchema,
  signupSchema,
  validateChangePasswordData,
  validateForgotPasswordData,
  validateLoginForm,
  validateProfileUpdateData,
  validateRegisterForm,
  validateResetPasswordData,
  // Auth validations
  validateSigninData,
  validateSigninForm,
  validateSignupData,
  validateSignupForm,
  verifyEmailSchema,
} from './auth.schema';
export type { CreateCategoryData, UpdateCategoryData } from './category.schema';
export {
  createCategorySchema,
  isValidCategoryData,
  isValidUpdateCategoryData,
  sanitizeCategoryData,
  updateCategorySchema,
  // Category validations
  validateCreateCategoryData,
  validateUpdateCategoryData,
} from './category.schema';
export type {
  AddressData,
  CheckDomainData as CommonCheckDomainData,
  CreateSiteData as CommonCreateSiteData,
  FileUploadData,
  PaginationData,
  SearchData,
  SettingsData,
  SocialMediaData,
} from './common.schema';

// ✅ Site Schema exports
export {
  addressSchema,
  checkDomainSchema as commonCheckDomainSchema,
  createSiteSchema as commonCreateSiteSchema,
  emailSchema,
  fileUploadSchema,
  isValidAddressData,
  // isValidCheckDomainData,
  // isValidCreateSiteData,
  isValidEmail,
  isValidFileUploadData,
  isValidSearchData,
  isValidSettingsData,
  isValidSocialMediaData,
  isValidUrl,
  nameSchema,
  paginationSchema,
  passwordSchema,
  phoneSchema,
  sanitizeStringData,
  searchSchema,
  settingsSchema,
  socialMediaSchema,
  urlSchema,
  validateAddressForm,
  validateCheckDomainForm,
  // validateCreateSiteForm,
  validateFileUploadForm,
  // Common validations
  validateSearchForm,
  validateSettingsForm,
  validateSocialMediaForm,
} from './common.schema';
export type { CreateCustomerData, UpdateCustomerData } from './customer.schema';
export {
  createCustomerSchema,
  isValidCustomerData,
  isValidUpdateCustomerData,
  sanitizeCustomerData,
  updateCustomerSchema,
  // Customer validations
  validateCreateCustomerData,
  validateUpdateCustomerData,
} from './customer.schema';
export { validateCheckDiscountData } from './discount.schema';
export type { CreateOrderData, OrderItemData, UpdateOrderData } from './order.schema';
export {
  createOrderSchema,
  isValidOrderData,
  isValidOrderItemData,
  // isValidShippingAddressData,
  isValidUpdateOrderData,
  sanitizeOrderData,
  updateOrderSchema,
  // Order validations
  validateCreateOrderData,
  validateUpdateOrderData,
} from './order.schema';
export type { CreateProductData, ProductStockData, ProductVariantData, UpdateProductData } from './product.schema';
export {
  createProductSchema,
  isValidProductData,
  isValidProductStockData,
  productStockSchema,
  productVariantSchema,
  sanitizeProductData,
  updateProductSchema,
  // Product validations
  validateCreateProductData,
  validateProductStockData,
  validateUpdateProductData,
} from './product.schema';

// ✅ Review Schema exports
export type {
  CreateReviewData,
  ReviewActionData,
  ReviewFilterData,
  ReviewStatsFilterData,
  ReviewVisibilityData,
  UpdateReviewData,
} from './review.schema';
export {
  calculateAverageRating,
  createReviewSchema,
  getRatingDistribution,
  getRatingText,
  getReviewStats,
  getReviewStatusColor,
  getReviewStatusText,
  isValidCreateReviewData,
  isValidReviewActionData,
  isValidReviewFilterData,
  isValidReviewStatsFilterData,
  isValidReviewVisibilityData,
  isValidUpdateReviewData,
  reviewActionSchema,
  reviewFilterSchema,
  reviewStatsFilterSchema,
  reviewVisibilitySchema,
  sanitizeReviewData,
  updateReviewSchema,
  // Review validations
  validateCreateReviewData,
  validateReviewActionData,
  validateReviewFilterData,
  validateReviewStatsFilterData,
  validateReviewVisibilityData,
  validateUpdateReviewData,
} from './review.schema';

// ✅ Preorder Schema exports
export type {
  CreatePreorderData,
  PreorderActionData,
  PreorderFilterData,
  PreorderStatsFilterData,
  UpdatePreorderData,
} from './preorder.schema';
export {
  calculatePreorderStats,
  createPreorderSchema,
  getPreorderStatusColor,
  getPreorderStatusText,
  isValidCreatePreorderData,
  isValidPreorderActionData,
  isValidPreorderFilterData,
  isValidPreorderStatsFilterData,
  isValidUpdatePreorderData,
  preorderActionSchema,
  preorderFilterSchema,
  preorderStatsFilterSchema,
  sanitizePreorderData,
  updatePreorderSchema,
  // Preorder validations
  validateCreatePreorderData,
  validatePreorderActionData,
  validatePreorderFilterData,
  validatePreorderStatsFilterData,
  validateUpdatePreorderData,
} from './preorder.schema';

// ✅ Shipping Schema exports
export type {
  CreateShippingData,
  CreateShippingMethodData,
  ShippingAddressData,
  ShippingFilterData,
  UpdateShippingData,
  UpdateShippingMethodData,
} from './shipping.schema';
export {
  createShippingMethodSchema,
  createShippingSchema,
  isValidCreateShippingData,
  isValidCreateShippingMethodData,
  isValidShippingAddressData,
  isValidShippingFilterData,
  isValidUpdateShippingData,
  isValidUpdateShippingMethodData,
  sanitizeShippingData,
  sanitizeShippingMethodData,
  shippingFilterSchema,
  updateShippingMethodSchema,
  updateShippingSchema,
  // Shipping validations
  validateCreateShippingData,
  validateCreateShippingMethodData,
  validateShippingFilterData,
  validateUpdateShippingData,
  validateUpdateShippingMethodData,
} from './shipping.schema';
export type {
  CheckDiscountData,
  CheckDomainData,
  CreateSiteData,
  SitePaginationParams,
  UpdateSiteData,
} from './site.schema';
export {
  // buildDomainUrl,
  checkDiscountSchema,
  checkDomainSchema,
  createSiteSchema,
  // isValidCheckDiscountData,
  // isValidCheckDomainData,
  // isValidCreateSiteData,
  // isValidUpdateSiteData,
  // sanitizeFormData,
  sitePaginationSchema,
  updateSiteSchema,
  // validateCheckDiscountData,
  // validateCheckDomainData,
  // validateCreateSiteData,
  // validatePaginationParams,
  // validateUpdateSiteData,
} from './site.schema';
export type {
  CheckDomainData as SiteCheckDomainData,
  CreateSiteData as SiteCreateSiteData,
  // SitePaginationParams,
  // UpdateSiteData,
} from './site.schema';
export {
  buildDomainUrl,
  // sanitizeFormData,
  // Site validations
  // validateCreateSiteData,
  // validateDomainData,
  // validatePaginationParams,
  // validateUpdateSiteData,
} from './site.schema';
export type { ChangePasswordData as UserChangePasswordData, UpdateAvatarData, UpdateProfileData } from './user.schema';
export {
  changePasswordSchema as userChangePasswordSchema,
  formatUserDisplayName,
  getUserInitials,
  isValidChangePasswordData,
  isValidUpdateAvatarData,
  isValidUpdateProfileData,
  sanitizeUserData,
  updateAvatarSchema,
  updateProfileSchema,
  validateAvatarFile,
  validateChangePasswordData as validateUserChangePasswordData,
  // User validations
  validateUpdateProfileData,
} from './user.schema';

// ✅ Variant Schema exports
export type {
  CreateVariantData,
  DeleteVariantData,
  UpdateVariantData,
  Variant,
  VariantAttribute,
} from './variant.schema';
export {
  COMMON_VARIANT_ATTRIBUTES,
  createVariantSchema,
  deleteVariantSchema,
  formatVariantDisplayName,
  getStockStatus,
  getStockStatusColor,
  getVariantPrice,
  isVariantInStock,
  sanitizeVariantData,
  updateVariantSchema,
  validateCreateVariantData,
  validateDeleteVariantData,
  validateUpdateVariantData,
  variantSchema,
} from './variant.schema';

// ✅ Notification Schema exports
export type {
  CreateNotificationData,
  MarkAsReadData,
  Notification,
  NotificationFilterData,
  NotificationPriority,
  NotificationStatsData,
  NotificationStatus,
  NotificationType,
  UpdateNotificationData,
} from './notification.schema';
export {
  createNotificationSchema,
  formatNotificationDate,
  getNotificationPriorityColor,
  getNotificationTypeColor,
  getNotificationTypeDisplayName,
  getNotificationTypeIcon,
  markAsReadSchema,
  NOTIFICATION_PRIORITIES,
  NOTIFICATION_STATUSES,
  NOTIFICATION_TYPES,
  notificationFilterSchema,
  notificationSchema,
  notificationStatsSchema,
  sanitizeNotificationData,
  updateNotificationSchema,
  validateCreateNotificationData,
  validateMarkAsReadData,
  validateNotificationFilterData,
  validateUpdateNotificationData,
} from './notification.schema';

// ✅ Marketing Schema exports
export type {
  Campaign,
  CampaignFilterData,
  CampaignStatus,
  CampaignType,
  CreateCampaignData,
  CreateDiscountData,
  Discount,
  DiscountFilterData,
  DiscountStatus,
  DiscountType,
  MarketingAnalytics,
  UpdateCampaignData,
  UpdateDiscountData,
} from './marketing.schema';
export {
  calculateCampaignConversionRate,
  calculateCampaignCTR,
  calculateCampaignROAS,
  calculateDiscountUsagePercentage,
  CAMPAIGN_STATUSES,
  CAMPAIGN_TYPES,
  campaignFilterSchema,
  campaignSchema,
  createCampaignSchema,
  createDiscountSchema,
  DISCOUNT_STATUSES,
  DISCOUNT_TYPES,
  discountFilterSchema,
  discountSchema,
  formatDiscountValue,
  formatMarketingCurrency,
  formatMarketingDate,
  getCampaignStatusColor,
  getCampaignStatusDisplayName,
  getCampaignTypeDisplayName,
  getDiscountStatusColor,
  getDiscountStatusDisplayName,
  getDiscountTypeDisplayName,
  isCampaignActive,
  isDiscountExpired,
  marketingAnalyticsSchema,
  sanitizeCampaignData,
  sanitizeDiscountData,
  updateCampaignSchema,
  updateDiscountSchema,
  validateCampaignFilterData,
  validateCreateCampaignData,
  validateCreateDiscountData,
  validateDiscountFilterData,
  validateUpdateCampaignData,
  validateUpdateDiscountData,
} from './marketing.schema';

// ✅ Brand Schema exports
export type {
  Brand,
  BrandFilterData,
  BrandStatsData,
  CreateBrandData,
  DeleteBrandData,
  UpdateBrandData,
} from './brand.schema';
export {
  brandFilterSchema,
  brandSchema,
  brandStatsSchema,
  canDeleteBrand,
  createBrandSchema,
  deleteBrandSchema,
  formatBrandDate,
  getBrandInitials,
  getBrandStatusColor,
  getBrandStatusText,
  getDeleteBrandMessage,
  sanitizeBrandData,
  updateBrandSchema,
  validateBrandFilterData,
  validateBrandUrl,
  validateCreateBrandData,
  validateDeleteBrandData,
  validateUpdateBrandData,
} from './brand.schema';

// ✅ Helper Functions ช่วยให้ข้อมูล string ใน object สะอาดและพร้อมใช้งาน
export function sanitizeFormData<T extends Record<string, any>>(data: T, excludeFields: string[] = []): T {
  const sanitized: Record<string, any> = { ...data };
  Object.keys(sanitized).forEach(key => {
    if (
      typeof sanitized[key] === 'string'
      && !excludeFields.includes(key)
    ) {
      sanitized[key] = sanitized[key].trim();
    }
  });
  return sanitized as T;
}

// ✅ Validation Functions
export function validateZodSchema<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
): {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
} {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.issues.forEach((err: any) => {
        if (err.path) {
          errors[err.path.join('.')] = err.message;
        }
      });
      return { success: false, errors };
    }
    return { success: false, errors: { general: 'ข้อมูลไม่ถูกต้อง' } };
  }
}

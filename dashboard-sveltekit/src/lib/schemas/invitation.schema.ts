import { z } from 'zod';

/**
 * ✅ Invitation Schemas - สำหรับ validation ใน route level
 */

// ✅ Join with code schema
export const joinWithCodeSchema = z.object({
  inviteCode: z.string().min(1, 'กรุณากรอกโค้ดเชิญ').trim(),
});

// ✅ Accept invitation schema
export const acceptInvitationSchema = z.object({
  invitationId: z.string().min(1, 'ไม่พบรหัสคำเชิญ'),
});

// ✅ Reject invitation schema
export const rejectInvitationSchema = z.object({
  invitationId: z.string().min(1, 'ไม่พบรหัสคำเชิญ'),
});

// ✅ Invitation data schema
export const invitationSchema = z.object({
  _id: z.string(),
  siteId: z.string(),
  siteName: z.string(),
  fromUserId: z.string(),
  fromUserName: z.string(),
  fromUserEmail: z.string().email(),
  role: z.enum(['owner', 'admin', 'editor', 'viewer']),
  message: z.string().optional(),
  status: z.enum(['pending', 'accepted', 'rejected', 'expired']),
  createdAt: z.string(),
  expiresAt: z.string(),
});

// ✅ Invitations list schema
export const invitationsListSchema = z.object({
  invitations: z.array(invitationSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

// ✅ Export types
export type JoinWithCodeData = z.infer<typeof joinWithCodeSchema>;
export type AcceptInvitationData = z.infer<typeof acceptInvitationSchema>;
export type RejectInvitationData = z.infer<typeof rejectInvitationSchema>;
export type InvitationData = z.infer<typeof invitationSchema>;
export type InvitationsListData = z.infer<typeof invitationsListSchema>;

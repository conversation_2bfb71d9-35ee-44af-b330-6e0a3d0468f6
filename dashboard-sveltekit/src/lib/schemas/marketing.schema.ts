import { z } from 'zod';

// Campaign types
export const CAMPAIGN_TYPES = [
  'discount',
  'coupon',
  'affiliate',
  'email',
  'sms',
  'social_media',
  'content',
  'influencer',
  'referral',
  'loyalty',
  'seasonal',
  'flash_sale',
] as const;

export type CampaignType = typeof CAMPAIGN_TYPES[number];

// Campaign status
export const CAMPAIGN_STATUSES = ['draft', 'active', 'paused', 'completed', 'cancelled'] as const;
export type CampaignStatus = typeof CAMPAIGN_STATUSES[number];

// Discount types
export const DISCOUNT_TYPES = ['percentage', 'fixed', 'shipping', 'buy_x_get_y'] as const;
export type DiscountType = typeof DISCOUNT_TYPES[number];

// Discount status
export const DISCOUNT_STATUSES = ['active', 'inactive', 'expired', 'used_up'] as const;
export type DiscountStatus = typeof DISCOUNT_STATUSES[number];

// Base campaign schema
export const campaignSchema = z.object({
  _id: z.string().optional(),
  siteId: z.string().min(1, 'Site ID จำเป็น'),
  name: z.string().min(1, 'กรุณากรอกชื่อแคมเปญ'),
  type: z.enum(CAMPAIGN_TYPES),
  status: z.enum(CAMPAIGN_STATUSES).default('draft'),
  description: z.string().optional(),
  startDate: z.string().min(1, 'กรุณาระบุวันที่เริ่ม'),
  endDate: z.string().optional(),
  targetAudience: z.string().optional(),
  budget: z.number().min(0, 'งบประมาณต้องไม่ติดลบ').optional(),
  spent: z.number().min(0).default(0),
  impressions: z.number().min(0).default(0),
  clicks: z.number().min(0).default(0),
  conversions: z.number().min(0).default(0),
  conversionValue: z.number().min(0).default(0),
  settings: z.record(z.any()).optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Create campaign schema
export const createCampaignSchema = campaignSchema.omit({
  _id: true,
  spent: true,
  impressions: true,
  clicks: true,
  conversions: true,
  conversionValue: true,
  createdAt: true,
  updatedAt: true,
});

// Update campaign schema
export const updateCampaignSchema = campaignSchema.omit({
  siteId: true,
  createdAt: true,
  updatedAt: true,
}).partial();

// Base discount schema
export const discountSchema = z.object({
  _id: z.string().optional(),
  siteId: z.string().min(1, 'Site ID จำเป็น'),
  code: z.string().min(1, 'กรุณากรอกรหัสส่วนลด').regex(
    /^[A-Z0-9_-]+$/,
    'รหัสส่วนลดต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข และ - _ เท่านั้น',
  ),
  name: z.string().min(1, 'กรุณากรอกชื่อส่วนลด'),
  type: z.enum(DISCOUNT_TYPES),
  value: z.number().min(0, 'ค่าส่วนลดต้องไม่ติดลบ'),
  minOrderAmount: z.number().min(0, 'ยอดขั้นต่ำต้องไม่ติดลบ').optional(),
  maxDiscount: z.number().min(0, 'ส่วนลดสูงสุดต้องไม่ติดลบ').optional(),
  usageLimit: z.number().min(0, 'จำกัดการใช้งานต้องไม่ติดลบ').optional(),
  usedCount: z.number().min(0).default(0),
  userLimit: z.number().min(0, 'จำกัดต่อผู้ใช้ต้องไม่ติดลบ').optional(),
  status: z.enum(DISCOUNT_STATUSES).default('active'),
  startDate: z.string().min(1, 'กรุณาระบุวันที่เริ่ม'),
  endDate: z.string().optional(),
  applicableProducts: z.array(z.string()).optional(),
  applicableCategories: z.array(z.string()).optional(),
  excludeProducts: z.array(z.string()).optional(),
  excludeCategories: z.array(z.string()).optional(),
  customerGroups: z.array(z.string()).optional(),
  firstTimeOnly: z.boolean().default(false),
  stackable: z.boolean().default(false),
  settings: z.record(z.any()).optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Create discount schema
export const createDiscountSchema = discountSchema.omit({
  _id: true,
  usedCount: true,
  createdAt: true,
  updatedAt: true,
});

// Update discount schema
export const updateDiscountSchema = discountSchema.omit({
  siteId: true,
  createdAt: true,
  updatedAt: true,
}).partial();

// Marketing analytics schema
export const marketingAnalyticsSchema = z.object({
  totalCampaigns: z.number(),
  activeCampaigns: z.number(),
  totalDiscounts: z.number(),
  activeDiscounts: z.number(),
  totalBudget: z.number(),
  totalSpent: z.number(),
  totalImpressions: z.number(),
  totalClicks: z.number(),
  totalConversions: z.number(),
  totalConversionValue: z.number(),
  averageCTR: z.number(),
  averageConversionRate: z.number(),
  averageCPC: z.number(),
  averageROAS: z.number(),
  campaignsByType: z.record(z.number()),
  campaignsByStatus: z.record(z.number()),
  discountsByType: z.record(z.number()),
  discountsByStatus: z.record(z.number()),
  topPerformingCampaigns: z.array(z.object({
    _id: z.string(),
    name: z.string(),
    type: z.string(),
    conversions: z.number(),
    conversionValue: z.number(),
  })),
  recentActivity: z.array(z.object({
    type: z.string(),
    action: z.string(),
    target: z.string(),
    timestamp: z.string(),
  })),
});

// Filter schemas
export const campaignFilterSchema = z.object({
  type: z.enum(CAMPAIGN_TYPES).optional(),
  status: z.enum(CAMPAIGN_STATUSES).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export const discountFilterSchema = z.object({
  type: z.enum(DISCOUNT_TYPES).optional(),
  status: z.enum(DISCOUNT_STATUSES).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

// Type inference
export type Campaign = z.infer<typeof campaignSchema>;
export type CreateCampaignData = z.infer<typeof createCampaignSchema>;
export type UpdateCampaignData = z.infer<typeof updateCampaignSchema>;
export type Discount = z.infer<typeof discountSchema>;
export type CreateDiscountData = z.infer<typeof createDiscountSchema>;
export type UpdateDiscountData = z.infer<typeof updateDiscountSchema>;
export type MarketingAnalytics = z.infer<typeof marketingAnalyticsSchema>;
export type CampaignFilterData = z.infer<typeof campaignFilterSchema>;
export type DiscountFilterData = z.infer<typeof discountFilterSchema>;

// Validation functions
export function validateCreateCampaignData(data: unknown) {
  try {
    const validatedData = createCampaignSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateUpdateCampaignData(data: unknown) {
  try {
    const validatedData = updateCampaignSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateCreateDiscountData(data: unknown) {
  try {
    const validatedData = createDiscountSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateUpdateDiscountData(data: unknown) {
  try {
    const validatedData = updateDiscountSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateCampaignFilterData(data: unknown) {
  try {
    const validatedData = campaignFilterSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateDiscountFilterData(data: unknown) {
  try {
    const validatedData = discountFilterSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

// Sanitization functions
export function sanitizeCampaignData(data: any): CreateCampaignData | UpdateCampaignData {
  return {
    siteId: data.siteId?.toString().trim() || '',
    name: data.name?.toString().trim() || '',
    type: data.type || 'discount',
    status: data.status || 'draft',
    description: data.description?.toString().trim() || undefined,
    startDate: data.startDate?.toString().trim() || '',
    endDate: data.endDate?.toString().trim() || undefined,
    targetAudience: data.targetAudience?.toString().trim() || undefined,
    budget: data.budget ? parseFloat(data.budget.toString()) : undefined,
    settings: data.settings || undefined,
    ...(data._id && { _id: data._id.toString() }),
  };
}

export function sanitizeDiscountData(data: any): CreateDiscountData | UpdateDiscountData {
  return {
    siteId: data.siteId?.toString().trim() || '',
    code: data.code?.toString().trim().toUpperCase() || '',
    name: data.name?.toString().trim() || '',
    type: data.type || 'percentage',
    value: data.value ? parseFloat(data.value.toString()) : 0,
    minOrderAmount: data.minOrderAmount ? parseFloat(data.minOrderAmount.toString()) : undefined,
    maxDiscount: data.maxDiscount ? parseFloat(data.maxDiscount.toString()) : undefined,
    usageLimit: data.usageLimit ? parseInt(data.usageLimit.toString()) : undefined,
    userLimit: data.userLimit ? parseInt(data.userLimit.toString()) : undefined,
    status: data.status || 'active',
    startDate: data.startDate?.toString().trim() || '',
    endDate: data.endDate?.toString().trim() || undefined,
    applicableProducts: Array.isArray(data.applicableProducts) ? data.applicableProducts : undefined,
    applicableCategories: Array.isArray(data.applicableCategories) ? data.applicableCategories : undefined,
    excludeProducts: Array.isArray(data.excludeProducts) ? data.excludeProducts : undefined,
    excludeCategories: Array.isArray(data.excludeCategories) ? data.excludeCategories : undefined,
    customerGroups: Array.isArray(data.customerGroups) ? data.customerGroups : undefined,
    firstTimeOnly: Boolean(data.firstTimeOnly),
    stackable: Boolean(data.stackable),
    settings: data.settings || undefined,
    ...(data._id && { _id: data._id.toString() }),
  };
}

// Helper functions
export function getCampaignTypeDisplayName(type: CampaignType): string {
  const typeNames: Record<CampaignType, string> = {
    discount: 'ส่วนลด',
    coupon: 'คูปอง',
    affiliate: 'พันธมิตร',
    email: 'อีเมลมาร์เก็ตติ้ง',
    sms: 'SMS มาร์เก็ตติ้ง',
    social_media: 'โซเชียลมีเดีย',
    content: 'คอนเทนต์มาร์เก็ตติ้ง',
    influencer: 'อินฟลูเอนเซอร์',
    referral: 'แนะนำเพื่อน',
    loyalty: 'โปรแกรมสะสมแต้ม',
    seasonal: 'ตามฤดูกาล',
    flash_sale: 'ลดราคาด่วน',
  };

  return typeNames[type] || type;
}

export function getCampaignStatusDisplayName(status: CampaignStatus): string {
  const statusNames: Record<CampaignStatus, string> = {
    draft: 'ร่าง',
    active: 'ใช้งานอยู่',
    paused: 'หยุดชั่วคราว',
    completed: 'เสร็จสิ้น',
    cancelled: 'ยกเลิก',
  };

  return statusNames[status] || status;
}

export function getDiscountTypeDisplayName(type: DiscountType): string {
  const typeNames: Record<DiscountType, string> = {
    percentage: 'เปอร์เซ็นต์',
    fixed: 'จำนวนคงที่',
    shipping: 'ค่าจัดส่งฟรี',
    buy_x_get_y: 'ซื้อ X ได้ Y',
  };

  return typeNames[type] || type;
}

export function getDiscountStatusDisplayName(status: DiscountStatus): string {
  const statusNames: Record<DiscountStatus, string> = {
    active: 'ใช้งานอยู่',
    inactive: 'ไม่ใช้งาน',
    expired: 'หมดอายุ',
    used_up: 'ใช้หมดแล้ว',
  };

  return statusNames[status] || status;
}

export function getCampaignStatusColor(status: CampaignStatus): string {
  const colors: Record<CampaignStatus, string> = {
    draft: 'badge-neutral',
    active: 'badge-success',
    paused: 'badge-warning',
    completed: 'badge-info',
    cancelled: 'badge-error',
  };

  return colors[status] || 'badge-neutral';
}

export function getDiscountStatusColor(status: DiscountStatus): string {
  const colors: Record<DiscountStatus, string> = {
    active: 'badge-success',
    inactive: 'badge-neutral',
    expired: 'badge-error',
    used_up: 'badge-warning',
  };

  return colors[status] || 'badge-neutral';
}

export function formatDiscountValue(discount: Discount): string {
  switch (discount.type) {
    case 'percentage':
      return `${discount.value}%`;
    case 'fixed':
      return `฿${discount.value.toLocaleString()}`;
    case 'shipping':
      return 'ฟรี';
    case 'buy_x_get_y':
      return `ซื้อ ${discount.value} ได้ 1`;
    default:
      return discount.value.toString();
  }
}

export function calculateDiscountUsagePercentage(discount: Discount): number {
  if (!discount.usageLimit) return 0;
  return Math.min((discount.usedCount / discount.usageLimit) * 100, 100);
}

export function isDiscountExpired(discount: Discount): boolean {
  if (!discount.endDate) return false;
  return new Date(discount.endDate) < new Date();
}

export function isCampaignActive(campaign: Campaign): boolean {
  const now = new Date();
  const startDate = new Date(campaign.startDate);
  const endDate = campaign.endDate ? new Date(campaign.endDate) : null;

  return campaign.status === 'active'
    && startDate <= now
    && (!endDate || endDate >= now);
}

export function calculateCampaignROAS(campaign: Campaign): number {
  if (!campaign.spent || campaign.spent === 0) return 0;
  return campaign.conversionValue / campaign.spent;
}

export function calculateCampaignCTR(campaign: Campaign): number {
  if (!campaign.impressions || campaign.impressions === 0) return 0;
  return (campaign.clicks / campaign.impressions) * 100;
}

export function calculateCampaignConversionRate(campaign: Campaign): number {
  if (!campaign.clicks || campaign.clicks === 0) return 0;
  return (campaign.conversions / campaign.clicks) * 100;
}

export function formatMarketingDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('th-TH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

export function formatMarketingCurrency(amount: number): string {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(amount);
}

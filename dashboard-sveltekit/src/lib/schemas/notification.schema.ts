import { z } from 'zod';

// Notification types
export const NOTIFICATION_TYPES = [
  // Order notifications
  'order_created',
  'order_confirmed',
  'order_shipped',
  'order_delivered',
  'order_cancelled',
  'order_refunded',

  // Product notifications
  'product_low_stock',
  'product_out_of_stock',
  'product_back_in_stock',
  'product_review_added',

  // Customer notifications
  'customer_registered',
  'customer_login',
  'customer_password_changed',

  // Payment notifications
  'payment_success',
  'payment_failed',
  'payment_refunded',

  // Subscription notifications
  'subscription_created',
  'subscription_renewed',
  'subscription_cancelled',
  'subscription_expired',
  'subscription_expiry_warning',

  // System notifications
  'system_maintenance',
  'system_update',
  'system_error',

  // Marketing notifications
  'discount_created',
  'discount_expired',
  'campaign_started',
  'campaign_ended',

  // General notifications
  'general_info',
  'general_warning',
  'general_error',
] as const;

export type NotificationType = typeof NOTIFICATION_TYPES[number];

// Notification priority levels
export const NOTIFICATION_PRIORITIES = ['low', 'medium', 'high', 'urgent'] as const;
export type NotificationPriority = typeof NOTIFICATION_PRIORITIES[number];

// Notification status
export const NOTIFICATION_STATUSES = ['unread', 'read', 'archived'] as const;
export type NotificationStatus = typeof NOTIFICATION_STATUSES[number];

// Base notification schema
export const notificationSchema = z.object({
  _id: z.string().optional(),
  siteId: z.string().min(1, 'Site ID จำเป็น'),
  userId: z.string().optional(),
  type: z.enum(NOTIFICATION_TYPES),
  priority: z.enum(NOTIFICATION_PRIORITIES).default('medium'),
  status: z.enum(NOTIFICATION_STATUSES).default('unread'),
  title: z.string().min(1, 'กรุณากรอกหัวข้อการแจ้งเตือน'),
  message: z.string().min(1, 'กรุณากรอกข้อความการแจ้งเตือน'),
  data: z.record(z.any()).optional(),
  relatedId: z.string().optional(), // ID ของข้อมูลที่เกี่ยวข้อง เช่น orderId, productId
  relatedType: z.string().optional(), // ประเภทของข้อมูลที่เกี่ยวข้อง เช่น 'order', 'product'
  actionUrl: z.string().optional(), // URL สำหรับการดำเนินการ
  isRead: z.boolean().default(false),
  readAt: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Create notification schema
export const createNotificationSchema = notificationSchema.omit({
  _id: true,
  status: true,
  isRead: true,
  readAt: true,
  createdAt: true,
  updatedAt: true,
});

// Update notification schema
export const updateNotificationSchema = z.object({
  status: z.enum(NOTIFICATION_STATUSES).optional(),
  isRead: z.boolean().optional(),
});

// Mark as read schema
export const markAsReadSchema = z.object({
  notificationIds: z.array(z.string()).min(1, 'กรุณาระบุ Notification ID'),
});

// Filter notifications schema
export const notificationFilterSchema = z.object({
  type: z.enum(NOTIFICATION_TYPES).optional(),
  priority: z.enum(NOTIFICATION_PRIORITIES).optional(),
  status: z.enum(NOTIFICATION_STATUSES).optional(),
  isRead: z.boolean().optional(),
  userId: z.string().optional(),
  relatedType: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  search: z.string().optional(),
});

// Notification stats schema
export const notificationStatsSchema = z.object({
  total: z.number(),
  unread: z.number(),
  read: z.number(),
  archived: z.number(),
  byType: z.record(z.number()),
  byPriority: z.record(z.number()),
});

// Type inference
export type Notification = z.infer<typeof notificationSchema>;
export type CreateNotificationData = z.infer<typeof createNotificationSchema>;
export type UpdateNotificationData = z.infer<typeof updateNotificationSchema>;
export type MarkAsReadData = z.infer<typeof markAsReadSchema>;
export type NotificationFilterData = z.infer<typeof notificationFilterSchema>;
export type NotificationStatsData = z.infer<typeof notificationStatsSchema>;

// Validation functions
export function validateCreateNotificationData(data: unknown) {
  try {
    const validatedData = createNotificationSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateUpdateNotificationData(data: unknown) {
  try {
    const validatedData = updateNotificationSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateMarkAsReadData(data: unknown) {
  try {
    const validatedData = markAsReadSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateNotificationFilterData(data: unknown) {
  try {
    const validatedData = notificationFilterSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

// Sanitization functions
export function sanitizeNotificationData(data: any): CreateNotificationData {
  return {
    siteId: data.siteId?.toString().trim() || '',
    userId: data.userId?.toString().trim() || undefined,
    type: data.type || 'general_info',
    priority: data.priority || 'medium',
    title: data.title?.toString().trim() || '',
    message: data.message?.toString().trim() || '',
    data: data.data || undefined,
    relatedId: data.relatedId?.toString().trim() || undefined,
    relatedType: data.relatedType?.toString().trim() || undefined,
    actionUrl: data.actionUrl?.toString().trim() || undefined,
  };
}

// Helper functions
export function getNotificationTypeDisplayName(type: NotificationType): string {
  const typeNames: Record<NotificationType, string> = {
    // Order notifications
    order_created: 'คำสั่งซื้อใหม่',
    order_confirmed: 'ยืนยันคำสั่งซื้อ',
    order_shipped: 'จัดส่งสินค้า',
    order_delivered: 'ส่งสินค้าสำเร็จ',
    order_cancelled: 'ยกเลิกคำสั่งซื้อ',
    order_refunded: 'คืนเงิน',

    // Product notifications
    product_low_stock: 'สินค้าใกล้หมด',
    product_out_of_stock: 'สินค้าหมด',
    product_back_in_stock: 'สินค้ากลับมามีสต็อก',
    product_review_added: 'รีวิวใหม่',

    // Customer notifications
    customer_registered: 'ลูกค้าใหม่',
    customer_login: 'ลูกค้าเข้าสู่ระบบ',
    customer_password_changed: 'เปลี่ยนรหัสผ่าน',

    // Payment notifications
    payment_success: 'ชำระเงินสำเร็จ',
    payment_failed: 'ชำระเงินล้มเหลว',
    payment_refunded: 'คืนเงินสำเร็จ',

    // Subscription notifications
    subscription_created: 'สมัครแพ็คเกจ',
    subscription_renewed: 'ต่ออายุแพ็คเกจ',
    subscription_cancelled: 'ยกเลิกแพ็คเกจ',
    subscription_expired: 'แพ็คเกจหมดอายุ',
    subscription_expiry_warning: 'แพ็คเกจใกล้หมดอายุ',

    // System notifications
    system_maintenance: 'บำรุงรักษาระบบ',
    system_update: 'อัปเดตระบบ',
    system_error: 'ข้อผิดพลาดระบบ',

    // Marketing notifications
    discount_created: 'ส่วนลดใหม่',
    discount_expired: 'ส่วนลดหมดอายุ',
    campaign_started: 'เริ่มแคมเปญ',
    campaign_ended: 'จบแคมเปญ',

    // General notifications
    general_info: 'ข้อมูลทั่วไป',
    general_warning: 'คำเตือน',
    general_error: 'ข้อผิดพลาด',
  };

  return typeNames[type] || type;
}

export function getNotificationPriorityColor(priority: NotificationPriority): string {
  const colors: Record<NotificationPriority, string> = {
    low: 'badge-neutral',
    medium: 'badge-info',
    high: 'badge-warning',
    urgent: 'badge-error',
  };

  return colors[priority] || 'badge-neutral';
}

export function getNotificationTypeIcon(type: NotificationType): string {
  const icons: Record<NotificationType, string> = {
    // Order notifications
    order_created: 'heroicons:shopping-cart',
    order_confirmed: 'heroicons:check-circle',
    order_shipped: 'heroicons:truck',
    order_delivered: 'heroicons:check-badge',
    order_cancelled: 'heroicons:x-circle',
    order_refunded: 'heroicons:arrow-uturn-left',

    // Product notifications
    product_low_stock: 'heroicons:exclamation-triangle',
    product_out_of_stock: 'heroicons:x-circle',
    product_back_in_stock: 'heroicons:check-circle',
    product_review_added: 'heroicons:star',

    // Customer notifications
    customer_registered: 'heroicons:user-plus',
    customer_login: 'heroicons:arrow-right-on-rectangle',
    customer_password_changed: 'heroicons:key',

    // Payment notifications
    payment_success: 'heroicons:credit-card',
    payment_failed: 'heroicons:exclamation-circle',
    payment_refunded: 'heroicons:arrow-uturn-left',

    // Subscription notifications
    subscription_created: 'heroicons:plus-circle',
    subscription_renewed: 'heroicons:arrow-path',
    subscription_cancelled: 'heroicons:x-circle',
    subscription_expired: 'heroicons:clock',
    subscription_expiry_warning: 'heroicons:exclamation-triangle',

    // System notifications
    system_maintenance: 'heroicons:wrench-screwdriver',
    system_update: 'heroicons:arrow-up-circle',
    system_error: 'heroicons:exclamation-circle',

    // Marketing notifications
    discount_created: 'heroicons:tag',
    discount_expired: 'heroicons:clock',
    campaign_started: 'heroicons:megaphone',
    campaign_ended: 'heroicons:flag',

    // General notifications
    general_info: 'heroicons:information-circle',
    general_warning: 'heroicons:exclamation-triangle',
    general_error: 'heroicons:x-circle',
  };

  return icons[type] || 'heroicons:bell';
}

export function getNotificationTypeColor(type: NotificationType): string {
  const colors: Record<NotificationType, string> = {
    // Order notifications
    order_created: 'badge-success',
    order_confirmed: 'badge-info',
    order_shipped: 'badge-warning',
    order_delivered: 'badge-success',
    order_cancelled: 'badge-error',
    order_refunded: 'badge-warning',

    // Product notifications
    product_low_stock: 'badge-warning',
    product_out_of_stock: 'badge-error',
    product_back_in_stock: 'badge-success',
    product_review_added: 'badge-info',

    // Customer notifications
    customer_registered: 'badge-success',
    customer_login: 'badge-info',
    customer_password_changed: 'badge-warning',

    // Payment notifications
    payment_success: 'badge-success',
    payment_failed: 'badge-error',
    payment_refunded: 'badge-warning',

    // Subscription notifications
    subscription_created: 'badge-success',
    subscription_renewed: 'badge-info',
    subscription_cancelled: 'badge-error',
    subscription_expired: 'badge-error',
    subscription_expiry_warning: 'badge-warning',

    // System notifications
    system_maintenance: 'badge-warning',
    system_update: 'badge-info',
    system_error: 'badge-error',

    // Marketing notifications
    discount_created: 'badge-success',
    discount_expired: 'badge-warning',
    campaign_started: 'badge-info',
    campaign_ended: 'badge-neutral',

    // General notifications
    general_info: 'badge-info',
    general_warning: 'badge-warning',
    general_error: 'badge-error',
  };

  return colors[type] || 'badge-neutral';
}

export function formatNotificationDate(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'เมื่อสักครู่';
  }
  else if (diffInMinutes < 60) {
    return `${diffInMinutes} นาทีที่แล้ว`;
  }
  else if (diffInMinutes < 1440) { // 24 hours
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours} ชั่วโมงที่แล้ว`;
  }
  else if (diffInMinutes < 10080) { // 7 days
    const days = Math.floor(diffInMinutes / 1440);
    return `${days} วันที่แล้ว`;
  }
  else {
    return date.toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
}

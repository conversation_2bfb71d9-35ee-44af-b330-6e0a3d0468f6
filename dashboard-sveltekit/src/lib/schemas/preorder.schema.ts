// ✅ Preorder Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
export const createPreorderSchema = z.object({
  productId: z.string().min(1, 'กรุณาระบุ Product ID'),
  customerId: z.string().min(1, 'กรุณาระบุ Customer ID'),
  quantity: z.number().int().min(1, 'จำนวนต้องมากกว่า 0'),
  price: z.number().min(0, 'ราคาต้องไม่น้อยกว่า 0'),
  depositAmount: z.number().min(0, 'จำนวนเงินมัดจำต้องไม่น้อยกว่า 0').optional(),
  estimatedDeliveryDate: z.string().optional(),
  notes: z.string().max(500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร').optional(),
});

export const updatePreorderSchema = z.object({
  status: z.enum(['pending', 'confirmed', 'processing', 'ready', 'delivered', 'cancelled'], {
    message: 'สถานะต้องเป็น pending, confirmed, processing, ready, delivered, หรือ cancelled',
  }).optional(),
  estimatedDeliveryDate: z.string().optional(),
  actualDeliveryDate: z.string().optional(),
  notes: z.string().max(500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร').optional(),
  depositPaid: z.boolean().optional(),
  remainingAmount: z.number().min(0, 'จำนวนเงินคงเหลือต้องไม่น้อยกว่า 0').optional(),
});

export const preorderActionSchema = z.object({
  preorderId: z.string().min(1, 'กรุณาระบุ Preorder ID'),
});

export const preorderFilterSchema = z.object({
  page: z.number().int().min(1, 'หน้าต้องมากกว่า 0').optional().default(1),
  limit: z.number().int().min(1, 'จำนวนต่อหน้าต้องมากกว่า 0').max(100, 'จำนวนต่อหน้าต้องไม่เกิน 100').optional().default(20),
  search: z.string().max(100, 'คำค้นหาต้องไม่เกิน 100 ตัวอักษร').optional(),
  status: z.enum(['pending', 'confirmed', 'processing', 'ready', 'delivered', 'cancelled']).optional(),
  productId: z.string().optional(),
  customerId: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

export const preorderStatsFilterSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  status: z.enum(['pending', 'confirmed', 'processing', 'ready', 'delivered', 'cancelled']).optional(),
});

// ✅ Type inference from schemas
export type CreatePreorderData = z.infer<typeof createPreorderSchema>;
export type UpdatePreorderData = z.infer<typeof updatePreorderSchema>;
export type PreorderActionData = z.infer<typeof preorderActionSchema>;
export type PreorderFilterData = z.infer<typeof preorderFilterSchema>;
export type PreorderStatsFilterData = z.infer<typeof preorderStatsFilterSchema>;

// ✅ Validation Functions using Zod
export function validateCreatePreorderData(data: unknown): {
  success: boolean;
  data?: CreatePreorderData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createPreorderSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateUpdatePreorderData(data: unknown): {
  success: boolean;
  data?: UpdatePreorderData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = updatePreorderSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validatePreorderActionData(data: unknown): {
  success: boolean;
  data?: PreorderActionData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = preorderActionSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validatePreorderFilterData(data: unknown): {
  success: boolean;
  data?: PreorderFilterData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = preorderFilterSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validatePreorderStatsFilterData(data: unknown): {
  success: boolean;
  data?: PreorderStatsFilterData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = preorderStatsFilterSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

// ✅ Type Guards using Zod
export function isValidCreatePreorderData(data: unknown): data is CreatePreorderData {
  return createPreorderSchema.safeParse(data).success;
}

export function isValidUpdatePreorderData(data: unknown): data is UpdatePreorderData {
  return updatePreorderSchema.safeParse(data).success;
}

export function isValidPreorderActionData(data: unknown): data is PreorderActionData {
  return preorderActionSchema.safeParse(data).success;
}

export function isValidPreorderFilterData(data: unknown): data is PreorderFilterData {
  return preorderFilterSchema.safeParse(data).success;
}

export function isValidPreorderStatsFilterData(data: unknown): data is PreorderStatsFilterData {
  return preorderStatsFilterSchema.safeParse(data).success;
}

// ✅ Sanitization Functions
export function sanitizePreorderData(
  data: CreatePreorderData | UpdatePreorderData,
): CreatePreorderData | UpdatePreorderData {
  const sanitized = { ...data };

  // Sanitize string fields
  if ('productId' in sanitized && sanitized.productId) {
    sanitized.productId = sanitized.productId.trim();
  }
  if ('customerId' in sanitized && sanitized.customerId) {
    sanitized.customerId = sanitized.customerId.trim();
  }
  if (sanitized.notes) {
    sanitized.notes = sanitized.notes.trim();
  }
  if (sanitized.estimatedDeliveryDate) {
    sanitized.estimatedDeliveryDate = sanitized.estimatedDeliveryDate.trim();
  }
  if (sanitized.actualDeliveryDate) {
    sanitized.actualDeliveryDate = sanitized.actualDeliveryDate.trim();
  }

  return sanitized;
}

// ✅ Helper Functions
export function getPreorderStatusText(status: string): string {
  const statusTexts = {
    pending: 'รอการยืนยัน',
    confirmed: 'ยืนยันแล้ว',
    processing: 'กำลังดำเนินการ',
    ready: 'พร้อมส่งมอบ',
    delivered: 'ส่งมอบแล้ว',
    cancelled: 'ยกเลิก',
  };
  return statusTexts[status as keyof typeof statusTexts] || status;
}

export function getPreorderStatusColor(status: string): string {
  const statusColors = {
    pending: 'warning',
    confirmed: 'info',
    processing: 'primary',
    ready: 'success',
    delivered: 'success',
    cancelled: 'error',
  };
  return statusColors[status as keyof typeof statusColors] || 'neutral';
}

export function calculatePreorderStats(preorders: any[]) {
  return {
    total: preorders.length,
    pending: preorders.filter(p => p.status === 'pending').length,
    confirmed: preorders.filter(p => p.status === 'confirmed').length,
    processing: preorders.filter(p => p.status === 'processing').length,
    ready: preorders.filter(p => p.status === 'ready').length,
    delivered: preorders.filter(p => p.status === 'delivered').length,
    cancelled: preorders.filter(p => p.status === 'cancelled').length,
    totalValue: preorders.reduce((sum, p) => sum + (p.price * p.quantity), 0),
    totalDeposit: preorders.reduce((sum, p) => sum + (p.depositAmount || 0), 0),
  };
}

// ✅ Review Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
export const createReviewSchema = z.object({
  productId: z.string().min(1, 'กรุณาระบุ Product ID'),
  customerId: z.string().min(1, 'กรุณาระบุ Customer ID'),
  rating: z.number().int().min(1, 'คะแนนต้องไม่น้อยกว่า 1').max(5, 'คะแนนต้องไม่เกิน 5'),
  title: z.string().max(200, 'หัวข้อต้องไม่เกิน 200 ตัวอักษร').optional(),
  comment: z.string().min(1, 'กรุณาระบุความคิดเห็น').max(1000, 'ความคิดเห็นต้องไม่เกิน 1000 ตัวอักษร'),
  images: z.array(z.string().url('รูปแบบ URL ไม่ถูกต้อง')).max(5, 'รูปภาพต้องไม่เกิน 5 รูป').optional(),
});

export const updateReviewSchema = z.object({
  rating: z.number().int().min(1, 'คะแนนต้องไม่น้อยกว่า 1').max(5, 'คะแนนต้องไม่เกิน 5').optional(),
  title: z.string().max(200, 'หัวข้อต้องไม่เกิน 200 ตัวอักษร').optional(),
  comment: z.string().min(1, 'กรุณาระบุความคิดเห็น').max(1000, 'ความคิดเห็นต้องไม่เกิน 1000 ตัวอักษร').optional(),
  isApproved: z.boolean().optional(),
  isVisible: z.boolean().optional(),
  images: z.array(z.string().url('รูปแบบ URL ไม่ถูกต้อง')).max(5, 'รูปภาพต้องไม่เกิน 5 รูป').optional(),
});

export const reviewActionSchema = z.object({
  reviewId: z.string().min(1, 'กรุณาระบุ Review ID'),
});

export const reviewVisibilitySchema = z.object({
  reviewId: z.string().min(1, 'กรุณาระบุ Review ID'),
  isVisible: z.boolean(),
});

export const reviewFilterSchema = z.object({
  page: z.number().int().min(1, 'หน้าต้องมากกว่า 0').optional().default(1),
  limit: z.number().int().min(1, 'จำนวนต่อหน้าต้องมากกว่า 0').max(100, 'จำนวนต่อหน้าต้องไม่เกิน 100').optional().default(20),
  search: z.string().max(100, 'คำค้นหาต้องไม่เกิน 100 ตัวอักษร').optional(),
  productId: z.string().optional(),
  customerId: z.string().optional(),
  rating: z.number().int().min(1).max(5).optional(),
  isApproved: z.boolean().optional(),
  isVisible: z.boolean().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

export const reviewStatsFilterSchema = z.object({
  productId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// ✅ Type inference from schemas
export type CreateReviewData = z.infer<typeof createReviewSchema>;
export type UpdateReviewData = z.infer<typeof updateReviewSchema>;
export type ReviewActionData = z.infer<typeof reviewActionSchema>;
export type ReviewVisibilityData = z.infer<typeof reviewVisibilitySchema>;
export type ReviewFilterData = z.infer<typeof reviewFilterSchema>;
export type ReviewStatsFilterData = z.infer<typeof reviewStatsFilterSchema>;

// ✅ Validation Functions using Zod
export function validateCreateReviewData(data: unknown): {
  success: boolean;
  data?: CreateReviewData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createReviewSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateUpdateReviewData(data: unknown): {
  success: boolean;
  data?: UpdateReviewData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = updateReviewSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateReviewActionData(data: unknown): {
  success: boolean;
  data?: ReviewActionData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = reviewActionSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateReviewVisibilityData(data: unknown): {
  success: boolean;
  data?: ReviewVisibilityData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = reviewVisibilitySchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateReviewFilterData(data: unknown): {
  success: boolean;
  data?: ReviewFilterData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = reviewFilterSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateReviewStatsFilterData(data: unknown): {
  success: boolean;
  data?: ReviewStatsFilterData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = reviewStatsFilterSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

// ✅ Type Guards using Zod
export function isValidCreateReviewData(data: unknown): data is CreateReviewData {
  return createReviewSchema.safeParse(data).success;
}

export function isValidUpdateReviewData(data: unknown): data is UpdateReviewData {
  return updateReviewSchema.safeParse(data).success;
}

export function isValidReviewActionData(data: unknown): data is ReviewActionData {
  return reviewActionSchema.safeParse(data).success;
}

export function isValidReviewVisibilityData(data: unknown): data is ReviewVisibilityData {
  return reviewVisibilitySchema.safeParse(data).success;
}

export function isValidReviewFilterData(data: unknown): data is ReviewFilterData {
  return reviewFilterSchema.safeParse(data).success;
}

export function isValidReviewStatsFilterData(data: unknown): data is ReviewStatsFilterData {
  return reviewStatsFilterSchema.safeParse(data).success;
}

// ✅ Sanitization Functions
export function sanitizeReviewData(
  data: CreateReviewData | UpdateReviewData,
): CreateReviewData | UpdateReviewData {
  const sanitized = { ...data };

  // Sanitize string fields
  if ('productId' in sanitized && sanitized.productId) {
    sanitized.productId = sanitized.productId.trim();
  }
  if ('customerId' in sanitized && sanitized.customerId) {
    sanitized.customerId = sanitized.customerId.trim();
  }
  if (sanitized.title) {
    sanitized.title = sanitized.title.trim();
  }
  if (sanitized.comment) {
    sanitized.comment = sanitized.comment.trim();
  }

  // Sanitize images array
  if (sanitized.images) {
    sanitized.images = sanitized.images.map(url => url.trim()).filter(url => url.length > 0);
  }

  return sanitized;
}

// ✅ Helper Functions
export function getRatingText(rating: number): string {
  const ratingTexts = {
    1: 'แย่มาก',
    2: 'แย่',
    3: 'ปานกลาง',
    4: 'ดี',
    5: 'ดีมาก',
  };
  return ratingTexts[rating as keyof typeof ratingTexts] || 'ไม่ระบุ';
}

export function getReviewStatusText(isApproved: boolean, isVisible: boolean): string {
  if (!isApproved) return 'รอการอนุมัติ';
  if (!isVisible) return 'ซ่อนอยู่';
  return 'แสดงอยู่';
}

export function getReviewStatusColor(isApproved: boolean, isVisible: boolean): string {
  if (!isApproved) return 'warning';
  if (!isVisible) return 'error';
  return 'success';
}

// ✅ Review Statistics Helpers
export function calculateAverageRating(reviews: { rating: number; }[]): number {
  if (reviews.length === 0) return 0;
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
  return Math.round((sum / reviews.length) * 10) / 10; // Round to 1 decimal place
}

export function getRatingDistribution(reviews: { rating: number; }[]): Record<number, number> {
  const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  reviews.forEach(review => {
    if (review.rating >= 1 && review.rating <= 5) {
      distribution[review.rating as keyof typeof distribution]++;
    }
  });
  return distribution;
}

export function getReviewStats(reviews: { rating: number; isApproved: boolean; isVisible: boolean; }[]) {
  return {
    total: reviews.length,
    approved: reviews.filter(r => r.isApproved).length,
    pending: reviews.filter(r => !r.isApproved).length,
    visible: reviews.filter(r => r.isVisible).length,
    hidden: reviews.filter(r => !r.isVisible).length,
    averageRating: calculateAverageRating(reviews),
    ratingDistribution: getRatingDistribution(reviews),
  };
}

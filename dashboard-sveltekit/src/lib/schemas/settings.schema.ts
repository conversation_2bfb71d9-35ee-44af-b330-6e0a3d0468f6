// ✅ Settings Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
const themeSchema = z.object({
  primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'สีต้องเป็นรูปแบบ hex (#000000)'),
  secondaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'สีต้องเป็นรูปแบบ hex (#000000)'),
  fontFamily: z.string().min(1, 'กรุณาเลือกฟอนต์'),
});

const seoSchema = z.object({
  metaTitle: z.string().max(60, 'Meta Title ต้องไม่เกิน 60 ตัวอักษร').optional(),
  metaDescription: z.string().max(160, 'Meta Description ต้องไม่เกิน 160 ตัวอักษร').optional(),
  keywords: z.array(z.string()).max(10, 'Keywords ต้องไม่เกิน 10 คำ').optional(),
  ogImage: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional(),
});

const contactSchema = z.object({
  email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง').optional(),
  phone: z.string().max(20, 'เบอร์โทรศัพท์ต้องไม่เกิน 20 ตัวอักษร').optional(),
  address: z.string().max(500, 'ที่อยู่ต้องไม่เกิน 500 ตัวอักษร').optional(),
});

const socialSchema = z.object({
  facebook: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional().or(z.literal('')),
  instagram: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional().or(z.literal('')),
  twitter: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional().or(z.literal('')),
  line: z.string().max(50, 'Line ID ต้องไม่เกิน 50 ตัวอักษร').optional(),
});

const featuresSchema = z.object({
  enableCart: z.boolean(),
  enableReviews: z.boolean(),
  enableWishlist: z.boolean(),
  enableChat: z.boolean(),
});

const notificationsSchema = z.object({
  emailNotifications: z.boolean(),
  smsNotifications: z.boolean(),
  pushNotifications: z.boolean(),
});

const securitySchema = z.object({
  twoFactorAuth: z.boolean(),
  passwordPolicy: z.enum(['weak', 'medium', 'strong'], {
    message: 'นโยบายรหัสผ่านต้องเป็น weak, medium, หรือ strong',
  }),
  sessionTimeout: z.number().int().min(5, 'Session timeout ต้องไม่น้อยกว่า 5 นาที').max(
    1440,
    'Session timeout ต้องไม่เกิน 1440 นาที',
  ),
});

export const updateSiteSettingsSchema = z.object({
  siteName: z.string().min(1, 'กรุณากรอกชื่อเว็บไซต์').max(100, 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร'),
  siteDescription: z.string().max(500, 'คำอธิบายเว็บไซต์ต้องไม่เกิน 500 ตัวอักษร').optional(),
  siteUrl: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional().or(z.literal('')),
  logo: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional(),
  favicon: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional(),
  theme: themeSchema,
  seo: seoSchema,
  contact: contactSchema,
  social: socialSchema,
  features: featuresSchema,
  notifications: notificationsSchema,
  security: securitySchema,
});

// Payment Gateway Schemas
export const createPaymentGatewaySchema = z.object({
  name: z.string().min(1, 'กรุณากรอกชื่อ Payment Gateway').max(100, 'ชื่อต้องไม่เกิน 100 ตัวอักษร'),
  provider: z.enum(['stripe', 'omise', 'promptpay', 'truemoney'], {
    message: 'Provider ต้องเป็น stripe, omise, promptpay, หรือ truemoney',
  }),
  config: z.object({
    publicKey: z.string().optional(),
    secretKey: z.string().min(1, 'กรุณากรอก Secret Key'),
    webhookUrl: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional().or(z.literal('')),
  }),
  fees: z.object({
    percentage: z.number().min(0, 'ค่าธรรมเนียมต้องไม่น้อยกว่า 0').max(100, 'ค่าธรรมเนียมต้องไม่เกิน 100%'),
    fixed: z.number().min(0, 'ค่าธรรมเนียมคงที่ต้องไม่น้อยกว่า 0'),
  }),
});

export const updatePaymentGatewaySchema = createPaymentGatewaySchema.partial().extend({
  status: z.enum(['active', 'inactive']).optional(),
});

// ✅ Type inference from schemas
export type UpdateSiteSettingsData = z.infer<typeof updateSiteSettingsSchema>;
export type CreatePaymentGatewayData = z.infer<typeof createPaymentGatewaySchema>;
export type UpdatePaymentGatewayData = z.infer<typeof updatePaymentGatewaySchema>;
export type ThemeData = z.infer<typeof themeSchema>;
export type SeoData = z.infer<typeof seoSchema>;
export type ContactData = z.infer<typeof contactSchema>;
export type SocialData = z.infer<typeof socialSchema>;
export type FeaturesData = z.infer<typeof featuresSchema>;
export type NotificationsData = z.infer<typeof notificationsSchema>;
export type SecurityData = z.infer<typeof securitySchema>;

// ✅ Validation Functions using Zod
export function validateUpdateSiteSettingsData(data: unknown): {
  success: boolean;
  data?: UpdateSiteSettingsData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = updateSiteSettingsSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateCreatePaymentGatewayData(data: unknown): {
  success: boolean;
  data?: CreatePaymentGatewayData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createPaymentGatewaySchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateUpdatePaymentGatewayData(data: unknown): {
  success: boolean;
  data?: UpdatePaymentGatewayData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = updatePaymentGatewaySchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

// ✅ Type Guards using Zod
export function isValidUpdateSiteSettingsData(data: unknown): data is UpdateSiteSettingsData {
  return updateSiteSettingsSchema.safeParse(data).success;
}

export function isValidCreatePaymentGatewayData(data: unknown): data is CreatePaymentGatewayData {
  return createPaymentGatewaySchema.safeParse(data).success;
}

export function isValidUpdatePaymentGatewayData(data: unknown): data is UpdatePaymentGatewayData {
  return updatePaymentGatewaySchema.safeParse(data).success;
}

// ✅ Sanitization Functions
export function sanitizeSiteSettingsData(
  data: UpdateSiteSettingsData,
): UpdateSiteSettingsData {
  const sanitized = { ...data };

  // Sanitize string fields
  if (sanitized.siteName) {
    sanitized.siteName = sanitized.siteName.trim();
  }
  if (sanitized.siteDescription) {
    sanitized.siteDescription = sanitized.siteDescription.trim();
  }
  if (sanitized.siteUrl) {
    sanitized.siteUrl = sanitized.siteUrl.trim();
  }

  // Sanitize contact fields
  if (sanitized.contact.email) {
    sanitized.contact.email = sanitized.contact.email.trim().toLowerCase();
  }
  if (sanitized.contact.phone) {
    sanitized.contact.phone = sanitized.contact.phone.trim();
  }
  if (sanitized.contact.address) {
    sanitized.contact.address = sanitized.contact.address.trim();
  }

  // Sanitize social fields
  if (sanitized.social.facebook) {
    sanitized.social.facebook = sanitized.social.facebook.trim();
  }
  if (sanitized.social.instagram) {
    sanitized.social.instagram = sanitized.social.instagram.trim();
  }
  if (sanitized.social.twitter) {
    sanitized.social.twitter = sanitized.social.twitter.trim();
  }
  if (sanitized.social.line) {
    sanitized.social.line = sanitized.social.line.trim();
  }

  // Sanitize SEO keywords
  if (sanitized.seo.keywords) {
    sanitized.seo.keywords = sanitized.seo.keywords
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
  }

  return sanitized;
}

export function sanitizePaymentGatewayData(
  data: CreatePaymentGatewayData | UpdatePaymentGatewayData,
): CreatePaymentGatewayData | UpdatePaymentGatewayData {
  const sanitized = { ...data };

  // Sanitize string fields
  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }

  if (sanitized.config) {
    if (sanitized.config.publicKey) {
      sanitized.config.publicKey = sanitized.config.publicKey.trim();
    }
    if (sanitized.config.secretKey) {
      sanitized.config.secretKey = sanitized.config.secretKey.trim();
    }
    if (sanitized.config.webhookUrl) {
      sanitized.config.webhookUrl = sanitized.config.webhookUrl.trim();
    }
  }

  return sanitized;
}

// ✅ Helper Functions
export function getProviderDisplayName(provider: string): string {
  const providerNames = {
    stripe: 'Stripe',
    omise: 'Omise',
    promptpay: 'PromptPay',
    truemoney: 'TrueMoney',
  };
  return providerNames[provider as keyof typeof providerNames] || provider;
}

export function getPasswordPolicyText(policy: string): string {
  const policyTexts = {
    weak: 'อ่อน (6 ตัวอักษร)',
    medium: 'ปานกลาง (8 ตัวอักษร + ตัวเลข)',
    strong: 'แข็งแกร่ง (8 ตัวอักษร + ตัวเลข + สัญลักษณ์)',
  };
  return policyTexts[policy as keyof typeof policyTexts] || policy;
}

export function formatKeywordsForDisplay(keywords: string[]): string {
  return keywords.join(', ');
}

export function parseKeywordsFromString(keywordsString: string): string[] {
  return keywordsString
    .split(',')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0);
}

// ✅ Shipping Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
const shippingAddressSchema = z.object({
  name: z.string().min(1, 'กรุณากรอกชื่อผู้รับ').max(100, 'ชื่อผู้รับต้องไม่เกิน 100 ตัวอักษร'),
  phone: z.string().min(1, 'กรุณากรอกเบอร์โทรศัพท์').max(20, 'เบอร์โทรศัพท์ต้องไม่เกิน 20 ตัวอักษร'),
  address: z.string().min(1, 'กรุณากรอกที่อยู่').max(200, 'ที่อยู่ต้องไม่เกิน 200 ตัวอักษร'),
  district: z.string().min(1, 'กรุณากรอกเขต/อำเภอ').max(100, 'เขต/อำเภอต้องไม่เกิน 100 ตัวอักษร'),
  province: z.string().min(1, 'กรุณากรอกจังหวัด').max(100, 'จังหวัดต้องไม่เกิน 100 ตัวอักษร'),
  postalCode: z.string().regex(/^[0-9]{5}$/, 'รหัสไปรษณีย์ต้องเป็นตัวเลข 5 หลัก'),
});

export const createShippingSchema = z.object({
  orderId: z.string().min(1, 'กรุณาระบุ Order ID'),
  carrier: z.string().min(1, 'กรุณาระบุผู้ให้บริการขนส่ง').max(100, 'ผู้ให้บริการขนส่งต้องไม่เกิน 100 ตัวอักษร'),
  method: z.string().min(1, 'กรุณาระบุวิธีการจัดส่ง').max(100, 'วิธีการจัดส่งต้องไม่เกิน 100 ตัวอักษร'),
  shippingAddress: shippingAddressSchema,
  shippingCost: z.number().min(0, 'ค่าจัดส่งต้องไม่น้อยกว่า 0'),
  notes: z.string().max(500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร').optional(),
});

export const updateShippingSchema = z.object({
  trackingNumber: z.string().max(100, 'เลขติดตามต้องไม่เกิน 100 ตัวอักษร').optional(),
  status: z.enum(['preparing', 'shipped', 'in_transit', 'delivered', 'failed', 'returned'], {
    message: 'สถานะต้องเป็น preparing, shipped, in_transit, delivered, failed, หรือ returned',
  }).optional(),
  estimatedDeliveryDate: z.string().optional(),
  actualDeliveryDate: z.string().optional(),
  notes: z.string().max(500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร').optional(),
});

export const createShippingMethodSchema = z.object({
  name: z.string().min(1, 'กรุณาระบุชื่อวิธีการจัดส่ง').max(100, 'ชื่อวิธีการจัดส่งต้องไม่เกิน 100 ตัวอักษร'),
  carrier: z.string().min(1, 'กรุณาระบุผู้ให้บริการขนส่ง').max(100, 'ผู้ให้บริการขนส่งต้องไม่เกิน 100 ตัวอักษร'),
  cost: z.number().min(0, 'ค่าจัดส่งต้องไม่น้อยกว่า 0'),
  estimatedDays: z.number().int().min(1, 'ระยะเวลาจัดส่งต้องไม่น้อยกว่า 1 วัน').max(365, 'ระยะเวลาจัดส่งต้องไม่เกิน 365 วัน'),
  isActive: z.boolean().optional().default(true),
});

export const updateShippingMethodSchema = createShippingMethodSchema.partial();

export const shippingFilterSchema = z.object({
  page: z.number().int().min(1, 'หน้าต้องมากกว่า 0').optional().default(1),
  limit: z.number().int().min(1, 'จำนวนต่อหน้าต้องมากกว่า 0').max(100, 'จำนวนต่อหน้าต้องไม่เกิน 100').optional().default(20),
  search: z.string().max(100, 'คำค้นหาต้องไม่เกิน 100 ตัวอักษร').optional(),
  status: z.enum(['preparing', 'shipped', 'in_transit', 'delivered', 'failed', 'returned']).optional(),
  carrier: z.string().max(100, 'ผู้ให้บริการขนส่งต้องไม่เกิน 100 ตัวอักษร').optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// ✅ Type inference from schemas
export type CreateShippingData = z.infer<typeof createShippingSchema>;
export type UpdateShippingData = z.infer<typeof updateShippingSchema>;
export type CreateShippingMethodData = z.infer<typeof createShippingMethodSchema>;
export type UpdateShippingMethodData = z.infer<typeof updateShippingMethodSchema>;
export type ShippingAddressData = z.infer<typeof shippingAddressSchema>;
export type ShippingFilterData = z.infer<typeof shippingFilterSchema>;

// ✅ Validation Functions using Zod
export function validateCreateShippingData(data: unknown): {
  success: boolean;
  data?: CreateShippingData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createShippingSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateUpdateShippingData(data: unknown): {
  success: boolean;
  data?: UpdateShippingData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = updateShippingSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateCreateShippingMethodData(data: unknown): {
  success: boolean;
  data?: CreateShippingMethodData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = createShippingMethodSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateUpdateShippingMethodData(data: unknown): {
  success: boolean;
  data?: UpdateShippingMethodData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = updateShippingMethodSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

export function validateShippingFilterData(data: unknown): {
  success: boolean;
  data?: ShippingFilterData;
  error?: string;
  errors?: Record<string, string>;
} {
  const result = shippingFilterSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    error: firstError?.message || 'ข้อมูลไม่ถูกต้อง',
    errors,
  };
}

// ✅ Type Guards using Zod
export function isValidCreateShippingData(data: unknown): data is CreateShippingData {
  return createShippingSchema.safeParse(data).success;
}

export function isValidUpdateShippingData(data: unknown): data is UpdateShippingData {
  return updateShippingSchema.safeParse(data).success;
}

export function isValidCreateShippingMethodData(data: unknown): data is CreateShippingMethodData {
  return createShippingMethodSchema.safeParse(data).success;
}

export function isValidUpdateShippingMethodData(data: unknown): data is UpdateShippingMethodData {
  return updateShippingMethodSchema.safeParse(data).success;
}

export function isValidShippingAddressData(data: unknown): data is ShippingAddressData {
  return shippingAddressSchema.safeParse(data).success;
}

export function isValidShippingFilterData(data: unknown): data is ShippingFilterData {
  return shippingFilterSchema.safeParse(data).success;
}

// ✅ Sanitization Functions
export function sanitizeShippingData(
  data: CreateShippingData | UpdateShippingData,
): CreateShippingData | UpdateShippingData {
  const sanitized = { ...data };

  // Sanitize string fields
  if ('orderId' in sanitized && sanitized.orderId) {
    sanitized.orderId = sanitized.orderId.trim();
  }
  if ('carrier' in sanitized && sanitized.carrier) {
    sanitized.carrier = sanitized.carrier.trim();
  }
  if ('method' in sanitized && sanitized.method) {
    sanitized.method = sanitized.method.trim();
  }
  if (sanitized.trackingNumber) {
    sanitized.trackingNumber = sanitized.trackingNumber.trim();
  }
  if (sanitized.notes) {
    sanitized.notes = sanitized.notes.trim();
  }

  // Sanitize shipping address
  if ('shippingAddress' in sanitized && sanitized.shippingAddress) {
    sanitized.shippingAddress = { ...sanitized.shippingAddress };
    if (sanitized.shippingAddress.name) {
      sanitized.shippingAddress.name = sanitized.shippingAddress.name.trim();
    }
    if (sanitized.shippingAddress.phone) {
      sanitized.shippingAddress.phone = sanitized.shippingAddress.phone.trim();
    }
    if (sanitized.shippingAddress.address) {
      sanitized.shippingAddress.address = sanitized.shippingAddress.address.trim();
    }
    if (sanitized.shippingAddress.district) {
      sanitized.shippingAddress.district = sanitized.shippingAddress.district.trim();
    }
    if (sanitized.shippingAddress.province) {
      sanitized.shippingAddress.province = sanitized.shippingAddress.province.trim();
    }
    if (sanitized.shippingAddress.postalCode) {
      sanitized.shippingAddress.postalCode = sanitized.shippingAddress.postalCode.trim();
    }
  }

  return sanitized;
}

export function sanitizeShippingMethodData(
  data: CreateShippingMethodData | UpdateShippingMethodData,
): CreateShippingMethodData | UpdateShippingMethodData {
  const sanitized = { ...data };

  // Sanitize string fields
  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }
  if (sanitized.carrier) {
    sanitized.carrier = sanitized.carrier.trim();
  }

  return sanitized;
}

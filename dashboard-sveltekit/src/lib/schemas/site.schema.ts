// ✅ Site Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
export const createSiteSchema = z.object({
  siteName: z
    .string()
    .min(3, 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 3 ตัวอักษร')
    .max(100, 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร'),
  packageType: z.string().min(1, 'กรุณาเลือกแพ็คเกจ'),
  typeDomain: z.enum(['subdomain', 'custom'], {
    message: 'ประเภทโดเมนต้องเป็น subdomain หรือ custom',
  }),
  subDomain: z
    .string()
    .regex(/^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/, 'Subdomain ต้องประกอบด้วยตัวอักษรภาษาอังกฤษ ตัวเลข และ - เท่านั้น')
    .min(3, 'Subdomain ต้องมีอย่างน้อย 3 ตัวอักษร')
    .max(50, 'Subdomain ต้องไม่เกิน 50 ตัวอักษร')
    .optional(),
  mainDomain: z.string().optional(),
  customDomain: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.typeDomain === 'custom') {
    if (!data.customDomain) {
      ctx.addIssue({
        path: ['customDomain'],
        code: 'custom',
        message: 'กรุณากรอก custom domain',
      });
    }
    else if (
      !/^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/.test(data.customDomain)
    ) {
      ctx.addIssue({
        path: ['customDomain'],
        code: 'custom',
        message: 'รูปแบบ custom domain ไม่ถูกต้อง',
      });
    }
  }
});

export const checkDomainSchema = z.object({
  typeDomain: z.enum(['subdomain', 'custom'], {
    message: 'ประเภทโดเมนต้องเป็น subdomain หรือ custom',
  }),
  subDomain: z
    .string()
    .regex(/^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/, 'Subdomain ต้องประกอบด้วยตัวอักษรภาษาอังกฤษ ตัวเลข และ - เท่านั้น')
    .min(3, 'Subdomain ต้องมีอย่างน้อย 3 ตัวอักษร')
    .max(50, 'Subdomain ต้องไม่เกิน 50 ตัวอักษร')
    .optional(),
  mainDomain: z.string().optional(),
  customDomain: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.typeDomain === 'custom') {
    if (!data.customDomain) {
      ctx.addIssue({
        path: ['customDomain'],
        code: 'custom',
        message: 'กรุณากรอก custom domain',
      });
    }
    else if (
      !/^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/.test(data.customDomain)
    ) {
      ctx.addIssue({
        path: ['customDomain'],
        code: 'custom',
        message: 'รูปแบบ custom domain ไม่ถูกต้อง',
      });
    }
  }
});

export const updateSiteSchema = z.object({
  siteName: z
    .string()
    .min(3, 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 3 ตัวอักษร')
    .max(100, 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร')
    .optional(),
  description: z.string().max(500, 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร').optional(),
  logo: z.string().optional(),
  favicon: z.string().optional(),
  customCss: z.string().optional(),
  customJs: z.string().optional(),
});

export const sitePaginationSchema = z.object({
  page: z.string().regex(/^\d+$/, 'หน้าต้องเป็นตัวเลข').optional().default('1'),
  limit: z
    .string()
    .regex(/^\d+$/, 'จำนวนต่อหน้าต้องเป็นตัวเลข')
    .refine(val => parseInt(val) <= 100, 'จำนวนต่อหน้าต้องไม่เกิน 100')
    .optional()
    .default('10'),
  search: z.string().optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
});

// ✅ Discount Validation Schema
export const checkDiscountSchema = z.object({
  discountCode: z
    .string()
    .min(1, 'กรุณากรอกรหัสส่วนลด')
    .min(2, 'รหัสส่วนลดต้องมีอย่างน้อย 2 ตัวอักษร')
    .max(50, 'รหัสส่วนลดต้องไม่เกิน 50 ตัวอักษร'),
  orderAmount: z
    .number()
    .positive('ยอดสั่งซื้อต้องมากกว่า 0')
    .min(1, 'ยอดสั่งซื้อต้องมากกว่า 0'),
  target: z.enum(['all', 'package', 'category', 'product', 'user_group', 'first_time']).optional(),
});

// ✅ Types derived from Zod schemas
export type CreateSiteData = z.infer<typeof createSiteSchema>;
export type CheckDomainData = z.infer<typeof checkDomainSchema>;
export type UpdateSiteData = z.infer<typeof updateSiteSchema>;
export type SitePaginationParams = z.infer<typeof sitePaginationSchema>;
export type CheckDiscountData = z.infer<typeof checkDiscountSchema>;

export function buildDomainUrl(data: CheckDomainData): string {
  if (data.typeDomain === 'subdomain' && data.subDomain && data.mainDomain) {
    return `${data.subDomain}.${data.mainDomain}`;
  }

  if (data.typeDomain === 'custom' && data.customDomain) {
    return data.customDomain;
  }

  return '';
}

// export function validatePaginationParams(data: unknown): {
//   success: boolean;
//   data?: SitePaginationParams;
//   errors?: Record<string, string>;
// } {
//   try {
//     const validatedData = sitePaginationSchema.parse(data);
//     return { success: true, data: validatedData };
//   }
//   catch (error) {
//     if (error instanceof z.ZodError) {
//       const errors: Record<string, string> = {};
//       error.issues.forEach((err: any) => {
//         if (err.path) {
//           errors[err.path.join('.')] = err.message;
//         }
//       });
//       return { success: false, errors };
//     }
//     return { success: false, errors: { general: 'ข้อมูลไม่ถูกต้อง' } };
//   }
// }

// // ✅ Type Guards
// export function isValidCreateSiteData(data: unknown): data is CreateSiteData {
//   return createSiteSchema.safeParse(data).success;
// }

// export function isValidCheckDomainData(data: unknown): data is CheckDomainData {
//   return checkDomainSchema.safeParse(data).success;
// }

// export function isValidUpdateSiteData(data: unknown): data is UpdateSiteData {
//   return updateSiteSchema.safeParse(data).success;
// }

// export function isValidCheckDiscountData(data: unknown): data is CheckDiscountData {
//   return checkDiscountSchema.safeParse(data).success;
// }

import { z } from 'zod';

// Base variant schema
export const variantSchema = z.object({
  _id: z.string().optional(),
  name: z.string().min(1, 'กรุณากรอกชื่อตัวเลือกสินค้า'),
  sku: z.string().optional(),
  price: z.number().min(0, 'ราคาต้องไม่ติดลบ').optional(),
  stock: z.number().int().min(0, 'สต็อกต้องไม่ติดลบ').optional(),
  attributes: z.record(z.string()).refine(
    attrs => Object.keys(attrs).length > 0,
    'กรุณากรอกคุณสมบัติของตัวเลือกสินค้าอย่างน้อย 1 รายการ',
  ),
  isActive: z.boolean().default(true),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Create variant schema
export const createVariantSchema = variantSchema.omit({
  _id: true,
  createdAt: true,
  updatedAt: true,
});

// Update variant schema
export const updateVariantSchema = variantSchema.omit({
  createdAt: true,
  updatedAt: true,
});

// Delete variant schema
export const deleteVariantSchema = z.object({
  variantId: z.string().min(1, 'กรุณาระบุ Variant ID'),
});

// Type inference
export type Variant = z.infer<typeof variantSchema>;
export type CreateVariantData = z.infer<typeof createVariantSchema>;
export type UpdateVariantData = z.infer<typeof updateVariantSchema>;
export type DeleteVariantData = z.infer<typeof deleteVariantSchema>;

// Validation functions
export function validateCreateVariantData(data: unknown) {
  try {
    const validatedData = createVariantSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateUpdateVariantData(data: unknown) {
  try {
    const validatedData = updateVariantSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

export function validateDeleteVariantData(data: unknown) {
  try {
    const validatedData = deleteVariantSchema.parse(data);
    return { success: true, data: validatedData, error: null };
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => err.message).join(', ');
      return { success: false, data: null, error: errorMessage };
    }
    return { success: false, data: null, error: 'ข้อมูลไม่ถูกต้อง' };
  }
}

// Sanitization functions
export function sanitizeVariantData(data: any): CreateVariantData | UpdateVariantData {
  return {
    name: data.name?.toString().trim() || '',
    sku: data.sku?.toString().trim() || undefined,
    price: data.price ? parseFloat(data.price.toString()) : undefined,
    stock: data.stock ? parseInt(data.stock.toString()) : undefined,
    attributes: data.attributes || {},
    isActive: Boolean(data.isActive),
    ...(data._id && { _id: data._id.toString() }),
  };
}

// Helper functions
export function formatVariantDisplayName(variant: Variant): string {
  const attrs = Object.entries(variant.attributes || {})
    .map(([key, value]) => `${key}: ${value}`)
    .join(', ');
  return attrs ? `${variant.name} (${attrs})` : variant.name;
}

export function getVariantPrice(variant: Variant, basePrice: number): number {
  return variant.price || basePrice;
}

export function isVariantInStock(variant: Variant): boolean {
  return (variant.stock || 0) > 0;
}

export function getStockStatus(stock: number): 'high' | 'medium' | 'low' | 'out' {
  if (stock === 0) return 'out';
  if (stock <= 5) return 'low';
  if (stock <= 20) return 'medium';
  return 'high';
}

export function getStockStatusColor(status: string): string {
  switch (status) {
    case 'high':
      return 'badge-success';
    case 'medium':
      return 'badge-warning';
    case 'low':
      return 'badge-warning';
    case 'out':
      return 'badge-error';
    default:
      return 'badge-neutral';
  }
}

// Common attribute names for variants
export const COMMON_VARIANT_ATTRIBUTES = [
  'color', // สี
  'size', // ขนาด
  'material', // วัสดุ
  'style', // สไตล์
  'pattern', // ลาย
  'weight', // น้ำหนัก
  'dimension', // ขนาด/มิติ
  'capacity', // ความจุ
  'model', // รุ่น
  'version', // เวอร์ชัน
] as const;

export type VariantAttribute = typeof COMMON_VARIANT_ATTRIBUTES[number];

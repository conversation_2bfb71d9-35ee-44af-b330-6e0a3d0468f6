import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

export interface Activity {
  _id: string;
  type: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  entityType: string;
  entityId: string;
  entityName?: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  userRole?: string;
  siteId?: string;
  siteName?: string;
  customerId?: string;
  customerName?: string;
  customerEmail?: string;
  metadata?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  formattedDate?: string;
  icon?: string;
  color?: string;
}

export interface ActivityFilters {
  type?: string;
  entityType?: string;
  entityId?: string;
  userId?: string;
  siteId?: string;
  customerId?: string;
  priority?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  skip?: number;
}

export interface ActivityStats {
  total: number;
  byType: Record<string, number>;
  byEntityType: Record<string, number>;
  byPriority: Record<string, number>;
  byStatus: Record<string, number>;
  recent: Activity[];
}

/**
 * ✅ Activity Service - จัดการประวัติและความเคลื่อนไหวทั้งหมดในระบบ
 * - ดึงข้อมูลกิจกรรม
 * - สถิติกิจกรรม
 * - การกรองและค้นหา
 */
export class ActivityService extends BaseService {
  /**
   * ✅ ดึงกิจกรรมตาม filters
   */
  async getActivities(filters: ActivityFilters = {}, token: string): Promise<ApiResponse<Activity[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();

      // Add filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const result = await this.makeAuthenticatedRequest<{ data: Activity[]; }>(
        `/activity?${queryParams.toString()}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ ดึงกิจกรรมตาม ID
   */
  async getActivityById(id: string, token: string): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        `/activity/${id}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ ดึงกิจกรรมล่าสุด
   */
  async getRecentActivities(limit: number = 10, token: string): Promise<ApiResponse<Activity[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity[]; }>(
        `/activity/recent?limit=${limit}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ ดึงกิจกรรมตาม entity
   */
  async getActivitiesByEntity(
    entityType: string,
    entityId: string,
    limit: number = 20,
    token: string,
  ): Promise<ApiResponse<Activity[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity[]; }>(
        `/activity/entity/${entityType}/${entityId}?limit=${limit}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ ดึงกิจกรรมตาม user
   */
  async getUserActivities(userId: string, limit: number = 20, token: string): Promise<ApiResponse<Activity[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity[]; }>(
        `/activity/user/${userId}?limit=${limit}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ ดึงกิจกรรมตาม site
   */
  async getSiteActivities(siteId: string, limit: number = 20, token: string): Promise<ApiResponse<Activity[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity[]; }>(
        `/activity/site/${siteId}?limit=${limit}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ ดึงกิจกรรมตาม customer
   */
  async getCustomerActivities(
    customerId: string,
    limit: number = 20,
    token: string,
  ): Promise<ApiResponse<Activity[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity[]; }>(
        `/activity/customer/${customerId}?limit=${limit}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ ดึงสถิติกิจกรรม
   */
  async getActivityStats(filters: ActivityFilters = {}, token: string): Promise<ApiResponse<ActivityStats>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();

      // Add filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const result = await this.makeAuthenticatedRequest<{ data: ActivityStats; }>(
        `/activity/stats?${queryParams.toString()}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ สร้างกิจกรรมใหม่
   */
  async createActivity(data: any, token: string): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        '/activity',
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ สร้างกิจกรรม User
   */
  async createUserActivity(
    type: string,
    title: string,
    description: string,
    token: string,
    metadata?: Record<string, any>,
  ): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        '/activity/user',
        token,
        {
          method: 'POST',
          body: {
            type,
            title,
            description,
            metadata,
          },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ สร้างกิจกรรม Site
   */
  async createSiteActivity(
    type: string,
    siteId: string,
    siteName: string,
    title: string,
    description: string,
    token: string,
    metadata?: Record<string, any>,
  ): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        '/activity/site',
        token,
        {
          method: 'POST',
          body: {
            type,
            siteId,
            siteName,
            title,
            description,
            metadata,
          },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ สร้างกิจกรรม Product
   */
  async createProductActivity(
    type: string,
    productId: string,
    productName: string,
    siteId: string,
    title: string,
    description: string,
    token: string,
    metadata?: Record<string, any>,
  ): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        '/activity/product',
        token,
        {
          method: 'POST',
          body: {
            type,
            productId,
            productName,
            siteId,
            title,
            description,
            metadata,
          },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ สร้างกิจกรรม Order
   */
  async createOrderActivity(
    type: string,
    orderId: string,
    orderNumber: string,
    siteId: string,
    customerId: string,
    customerName: string,
    title: string,
    description: string,
    token: string,
    metadata?: Record<string, any>,
  ): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        '/activity/order',
        token,
        {
          method: 'POST',
          body: {
            type,
            orderId,
            orderNumber,
            siteId,
            customerId,
            customerName,
            title,
            description,
            metadata,
          },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ สร้างกิจกรรม Customer
   */
  async createCustomerActivity(
    type: string,
    customerId: string,
    customerName: string,
    customerEmail: string,
    siteId: string,
    title: string,
    description: string,
    token: string,
    metadata?: Record<string, any>,
  ): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        '/activity/customer',
        token,
        {
          method: 'POST',
          body: {
            type,
            customerId,
            customerName,
            customerEmail,
            siteId,
            title,
            description,
            metadata,
          },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ สร้างกิจกรรม System
   */
  async createSystemActivity(
    type: string,
    title: string,
    description: string,
    token: string,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    metadata?: Record<string, any>,
  ): Promise<ApiResponse<Activity>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Activity; }>(
        '/activity/system',
        token,
        {
          method: 'POST',
          body: {
            type,
            title,
            description,
            priority,
            metadata,
          },
        },
      );
      return result.data;
    });
  }
}

export const activityService = new ActivityService();

import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

/**
 * Addon data interfaces
 */
export interface Addon {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  isPurchased?: boolean; // ✅ เพิ่มสถานะการซื้อ
  isLifetime?: boolean; // ✅ เพิ่มสถานะซื้อถาวร
  price?: number;
  features?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * ✅ Addons Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class AddonsService extends BaseService {
  /**
   * ✅ Get Site Addons
   */
  async getSiteAddons(siteId: string, token: string): Promise<ApiResponse<Addon[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: Addon[];
      }>(`/addons/sites/${siteId}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Activate Addon
   */
  async activateAddon(
    siteId: string,
    addonId: string,
    token: string,
  ): Promise<ApiResponse<Addon>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!addonId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Addon ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Addon; }>(
        `/addons/sites/${siteId}/${addonId}/activate`,
        token,
        {
          method: 'POST',
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Deactivate Addon
   */
  async deactivateAddon(
    siteId: string,
    addonId: string,
    token: string,
  ): Promise<ApiResponse<Addon>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!addonId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Addon ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Addon; }>(
        `/addons/sites/${siteId}/${addonId}/deactivate`,
        token,
        {
          method: 'POST',
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Check if specific addon is active
   */
  async isAddonActive(
    siteId: string,
    addonId: string,
    token: string,
  ): Promise<ApiResponse<boolean>> {
    const addonsResult = await this.getSiteAddons(siteId, token);

    if (!addonsResult.success) {
      return {
        type: 'failure',
        success: false,
        error: addonsResult.error || 'ไม่สามารถตรวจสอบสถานะ addon ได้',
      };
    }

    const addon = addonsResult.data?.find(addon => addon.id === addonId);

    return {
      type: 'success',
      success: true,
      data: addon?.isActive || false,
    };
  }

  /**
   * ✅ Get specific addon by ID with fallback
   */
  async getAddonById(
    siteId: string,
    addonId: string,
    token: string,
  ): Promise<ApiResponse<Addon | null>> {
    const addonsResult = await this.getSiteAddons(siteId, token);

    if (!addonsResult.success) {
      // ✅ Fallback: ถ้า API error ให้ถือว่า addon ไม่ active
      console.warn(`Addons API error for site ${siteId}:`, addonsResult.error);
      return {
        type: 'success',
        success: true,
        data: null, // ถือว่าไม่มี addon หรือไม่ active
      };
    }

    const addon = addonsResult.data?.find(addon => addon.id === addonId);

    return {
      type: 'success',
      success: true,
      data: addon || null,
    };
  }

  /**
   * ✅ Check if specific addon is active with fallback
   * ถ้า API error ให้ถือว่า addon active เพื่อให้ระบบทำงานต่อได้
   */
  async isAddonActiveWithFallback(
    siteId: string,
    addonId: string,
    token: string,
  ): Promise<ApiResponse<boolean>> {
    const addonsResult = await this.getSiteAddons(siteId, token);

    if (!addonsResult.success) {
      // ✅ Fallback: ถ้า API error ให้ถือว่า addon active เพื่อให้ระบบทำงานต่อได้
      console.warn(`Addons API error for site ${siteId}, assuming addon '${addonId}' is active:`, addonsResult.error);
      return {
        type: 'success',
        success: true,
        data: true, // ถือว่า active เพื่อให้ระบบทำงานต่อได้
      };
    }

    const addon = addonsResult.data?.find(addon => addon.id === addonId);

    return {
      type: 'success',
      success: true,
      data: addon?.isActive || false,
    };
  }

  /**
   * ✅ Get addon with graceful fallback for display
   */
  async getAddonWithFallback(
    siteId: string,
    addonId: string,
    token: string,
  ): Promise<ApiResponse<Addon>> {
    const addonsResult = await this.getSiteAddons(siteId, token);

    if (!addonsResult.success) {
      // ✅ Fallback: ถ้า API error ให้ใช้ default addon object (ยังไม่ได้ซื้อ)
      console.warn(`Addons API error for site ${siteId}, using default addon '${addonId}':`, addonsResult.error);
      return {
        type: 'success',
        success: true,
        data: {
          id: addonId,
          name: addonId.charAt(0).toUpperCase() + addonId.slice(1),
          isActive: false,
          isPurchased: false, // ✅ Fallback ให้ถือว่ายังไม่ได้ซื้อ
          isLifetime: false,
          price: 299, // ✅ ราคาเริ่มต้น
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      };
    }

    const addon = addonsResult.data?.find(addon => addon.id === addonId);

    if (!addon) {
      // ✅ ถ้าไม่เจอ addon ให้ใช้ default (ยังไม่ได้ซื้อ)
      return {
        type: 'success',
        success: true,
        data: {
          id: addonId,
          name: addonId.charAt(0).toUpperCase() + addonId.slice(1),
          isActive: false,
          isPurchased: false, // ✅ ยังไม่ได้ซื้อ
          isLifetime: false,
          price: 99, // ✅ ราคาเริ่มต้น
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      };
    }

    return {
      type: 'success',
      success: true,
      data: addon,
    };
  }

  /**
   * ✅ Purchase/Rent Addon - ใช้ endpoint /rent ตาม backend
   */
  async purchaseAddon(
    siteId: string,
    addonId: string,
    isLifetime: boolean = false,
    token: string,
  ): Promise<ApiResponse<Addon>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!addonId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Addon ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Addon; }>(
        `/addons/sites/${siteId}/${addonId}/rent`,
        token,
        {
          method: 'POST',
          body: { isLifetime },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Check if addon can be activated (purchased and not active)
   */
  canActivateAddon(addon: Addon): boolean {
    return addon.isPurchased === true && addon.isActive === false;
  }

  /**
   * ✅ Check if addon can be purchased (not purchased yet)
   */
  canPurchaseAddon(addon: Addon): boolean {
    return addon.isPurchased !== true;
  }
}

export const addonsService = new AddonsService();

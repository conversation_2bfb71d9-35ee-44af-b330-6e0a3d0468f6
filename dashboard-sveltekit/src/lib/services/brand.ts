import { apiUrl } from '$lib/config';
import type {
  Brand,
  BrandFilterData,
  BrandStatsData,
  CreateBrandData,
  UpdateBrandData,
} from '$lib/schemas/brand.schema';

export interface BrandServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface BrandListResponse {
  brands: Brand[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  stats: BrandStatsData;
}

class BrandService {
  private baseUrl = `${apiUrl}/brand`;

  /**
   * ดึงรายการแบรนด์
   */
  async getBrands(
    siteId: string,
    filters: Partial<BrandFilterData>,
    token: string,
  ): Promise<BrandServiceResponse<BrandListResponse>> {
    try {
      const params = new URLSearchParams();

      // Add filters to params
      if (filters.search) params.append('search', filters.search);
      if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`${this.baseUrl}/${siteId}?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงข้อมูลแบรนด์',
        };
      }
    }
    catch (error) {
      console.error('Brand Service - Get brands error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ดึงข้อมูลแบรนด์เดียว
   */
  async getBrand(
    brandId: string,
    siteId: string,
    token: string,
  ): Promise<BrandServiceResponse<Brand>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/${brandId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงข้อมูลแบรนด์',
        };
      }
    }
    catch (error) {
      console.error('Brand Service - Get brand error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * สร้างแบรนด์ใหม่
   */
  async createBrand(
    brandData: CreateBrandData,
    token: string,
  ): Promise<BrandServiceResponse<Brand>> {
    try {
      const response = await fetch(`${this.baseUrl}/${brandData.siteId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(brandData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการสร้างแบรนด์',
        };
      }
    }
    catch (error) {
      console.error('Brand Service - Create brand error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * อัปเดตแบรนด์
   */
  async updateBrand(
    brandId: string,
    brandData: UpdateBrandData,
    siteId: string,
    token: string,
  ): Promise<BrandServiceResponse<Brand>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/${brandId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(brandData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการอัปเดตแบรนด์',
        };
      }
    }
    catch (error) {
      console.error('Brand Service - Update brand error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ลบแบรนด์
   */
  async deleteBrand(
    brandId: string,
    siteId: string,
    token: string,
  ): Promise<BrandServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/${brandId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการลบแบรนด์',
        };
      }
    }
    catch (error) {
      console.error('Brand Service - Delete brand error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * เปิด/ปิดใช้งานแบรนด์
   */
  async toggleBrandStatus(
    brandId: string,
    siteId: string,
    isActive: boolean,
    token: string,
  ): Promise<BrandServiceResponse<Brand>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/${brandId}/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ isActive }),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะแบรนด์',
        };
      }
    }
    catch (error) {
      console.error('Brand Service - Toggle brand status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ดึงสถิติแบรนด์
   */
  async getBrandStats(
    siteId: string,
    token: string,
  ): Promise<BrandServiceResponse<BrandStatsData>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงสถิติแบรนด์',
        };
      }
    }
    catch (error) {
      console.error('Brand Service - Get brand stats error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }
}

// Export singleton instance
export const brandService = new BrandService();

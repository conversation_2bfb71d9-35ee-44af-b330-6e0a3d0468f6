import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

/**
 * Content data interfaces
 */
export interface Blog {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  tags?: string[];
  published: boolean;
  featuredImage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface News {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  categoryId?: string;
  category?: {
    id: string;
    name: string;
  };
  published: boolean;
  featuredImage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Novel {
  id: string;
  title: string;
  description: string;
  genre?: string;
  tags?: string[];
  published: boolean;
  coverImage?: string;
  chaptersCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface NewsCategory {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * ✅ Content Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class ContentService extends BaseService {
  // ✅ Blog Methods
  async getBlogs(siteId: string, token: string): Promise<ApiResponse<Blog[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: Blog[];
      }>(`/content/blogs/sites/${siteId}`, token);
      return result.data;
    });
  }

  async createBlog(
    data: any,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Blog>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate required fields
    if (!data.title?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกหัวข้อบล็อก',
      };
    }

    if (!data.content?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกเนื้อหาบล็อก',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Blog; }>(
        `/content/blogs/sites/${siteId}`,
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  async deleteBlog(blogId: string, token: string): Promise<ApiResponse<{ message: string; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!blogId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Blog ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { message: string; }; }>(
        `/content/blogs/${blogId}`,
        token,
        {
          method: 'DELETE',
        },
      );
      return result.data;
    });
  }

  async toggleBlogPublish(
    blogId: string,
    published: boolean,
    token: string,
  ): Promise<ApiResponse<Blog>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!blogId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Blog ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Blog; }>(
        `/content/blogs/${blogId}/publish`,
        token,
        {
          method: 'PATCH',
          body: { published },
        },
      );
      return result.data;
    });
  }

  // ✅ News Methods
  async getNews(siteId: string, token: string): Promise<ApiResponse<News[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: News[];
      }>(`/content/news/sites/${siteId}`, token);
      return result.data;
    });
  }

  async getNewsCategories(siteId: string, token: string): Promise<ApiResponse<NewsCategory[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: NewsCategory[];
      }>(`/content/news/sites/${siteId}/categories`, token);
      return result.data;
    });
  }

  async createNews(
    data: any,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<News>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate required fields
    if (!data.title?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกหัวข้อข่าว',
      };
    }

    if (!data.content?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกเนื้อหาข่าว',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: News; }>(
        `/content/news/sites/${siteId}`,
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  async deleteNews(newsId: string, token: string): Promise<ApiResponse<{ message: string; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!newsId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'News ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { message: string; }; }>(
        `/content/news/${newsId}`,
        token,
        {
          method: 'DELETE',
        },
      );
      return result.data;
    });
  }

  async toggleNewsPublish(
    newsId: string,
    published: boolean,
    token: string,
  ): Promise<ApiResponse<News>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!newsId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'News ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: News; }>(
        `/content/news/${newsId}/publish`,
        token,
        {
          method: 'PATCH',
          body: { published },
        },
      );
      return result.data;
    });
  }

  // ✅ Novel Methods
  async getNovels(siteId: string, token: string): Promise<ApiResponse<Novel[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: Novel[];
      }>(`/content/novels/sites/${siteId}`, token);
      return result.data;
    });
  }

  async createNovel(
    data: any,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Novel>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate required fields
    if (!data.title?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกชื่อเรื่อง',
      };
    }

    if (!data.description?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกคำอธิบายเรื่อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Novel; }>(
        `/content/novels/sites/${siteId}`,
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  async deleteNovel(novelId: string, token: string): Promise<ApiResponse<{ message: string; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!novelId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Novel ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { message: string; }; }>(
        `/content/novels/${novelId}`,
        token,
        {
          method: 'DELETE',
        },
      );
      return result.data;
    });
  }

  async toggleNovelPublish(
    novelId: string,
    published: boolean,
    token: string,
  ): Promise<ApiResponse<Novel>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!novelId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Novel ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Novel; }>(
        `/content/novels/${novelId}/publish`,
        token,
        {
          method: 'PATCH',
          body: { published },
        },
      );
      return result.data;
    });
  }
}

export const contentService = new ContentService();

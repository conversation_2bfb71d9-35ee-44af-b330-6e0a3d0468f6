import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

/**
 * Dashboard data interfaces
 */
export interface DashboardData {
  totalSales: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  recentOrders: any[];
  topProducts: any[];
  salesChart: any[];
}

export interface DashboardStats {
  sales: {
    today: number;
    week: number;
    month: number;
    change: number;
  };
  orders: {
    today: number;
    week: number;
    month: number;
    change: number;
  };
  customers: {
    today: number;
    week: number;
    month: number;
    change: number;
  };
  products: {
    total: number;
    active: number;
    outOfStock: number;
  };
}

export interface RecentActivity {
  id: string;
  type: 'order' | 'customer' | 'product' | 'payment';
  title: string;
  description: string;
  timestamp: string;
  status: string;
}

/**
 * ✅ Dashboard Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class DashboardService extends BaseService {
  /**
   * ✅ Get dashboard overview data
   */
  async getDashboardData(siteId: string, token: string): Promise<ApiResponse<DashboardData>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: DashboardData;
      }>(`/dashboard/${siteId}/overview`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get dashboard statistics
   */
  async getDashboardStats(siteId: string, token: string): Promise<ApiResponse<DashboardStats>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: DashboardStats;
      }>(`/dashboard/${siteId}/stats`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get recent activities
   */
  async getRecentActivities(siteId: string, token: string): Promise<ApiResponse<RecentActivity[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: RecentActivity[];
      }>(`/dashboard/${siteId}/activities`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get dashboard widgets
   */
  async getDashboardWidgets(siteId: string, token: string): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: any;
      }>(`/dashboard/${siteId}/widgets`, token);
      return result.data;
    });
  }

  /**
   * ✅ Update dashboard settings
   */
  async updateDashboardSettings(
    siteId: string,
    token: string,
    settings: any,
  ): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: any;
      }>(`/dashboard/${siteId}/settings`, token, {
        method: 'PUT',
        body: settings,
      });
      return result.data;
    });
  }
}

export const dashboardService = new DashboardService();

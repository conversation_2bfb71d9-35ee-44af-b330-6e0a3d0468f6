import { getToken } from '$lib/api/client';
import type { ApiResponse, DiscountInfo, DiscountValidationRequest } from '$lib/types';
import { BaseService } from './base';

class DiscountService extends BaseService {
  /**
   * ตรวจสอบความถูกต้องของโค้ดส่วนลด
   */

  async validateDiscount(
    discountCode: string,
    validationData: Omit<DiscountValidationRequest, 'discountCode'> = {},
    token?: string,
  ): Promise<ApiResponse<DiscountInfo>> {
    if (!discountCode || discountCode.trim().length === 0) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกรหัสส่วนลด',
      };
    }

    // const validationResult = validateZodSchema(checkDomainSchema, sanitizedData);

    // if (!validationResult.success) {
    //   return {
    //     success: false,
    //     type: 'failure',
    //     message: Object.values(validationResult.errors ?? {}).join(', '),
    //   };
    // }

    const requestBody = {
      discountCode: discountCode.trim(),
      siteId: validationData.siteId,
      target: validationData.target || 'package',
      orderAmount: validationData.orderAmount || 0,
      items: validationData.items || [],
    };

    // ใช้ token ที่ส่งมา หรือดึงจาก auth store
    const authToken = token || getToken();

    return this.handleRequest(async () => {
      const result = await this.makePublicRequest<{ data: DiscountInfo; }>('/discount/validate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        ...(authToken && { headers: { Authorization: `Bearer ${authToken}` } }),
      });
      return result.data;
    });
  }

  /**
   * ดึงโค้ดส่วนลดตาม code
   */
  async getDiscountByCode(
    code: string,
    siteId: string,
    token?: string,
  ): Promise<ApiResponse<DiscountInfo>> {
    if (!code || !siteId) {
      return {
        success: false,
        type: 'failure',
        error: 'กรุณาระบุรหัสส่วนลดและ siteId',
      };
    }

    const authToken = token || getToken();

    return this.handleRequest(async () => {
      const result = await this.makePublicRequest<{ data: DiscountInfo; }>(
        `/discount/code/${code}?siteId=${siteId}`,
        {
          ...(authToken && { headers: { Authorization: `Bearer ${authToken}` } }),
        },
      );
      return result.data;
    });
  }

  /**
   * ดึงรายการส่วนลดที่ใช้งานได้
   */
  async getActiveDiscounts(siteId: string, token?: string): Promise<ApiResponse<DiscountInfo[]>> {
    if (!siteId) {
      return {
        success: false,
        type: 'failure',
        error: 'กรุณาระบุ siteId',
      };
    }

    const authToken = token || getToken();

    return this.handleRequest(async () => {
      const result = await this.makePublicRequest<{ data: DiscountInfo[]; }>(
        `/discount/active?siteId=${siteId}`,
        {
          ...(authToken && { headers: { Authorization: `Bearer ${authToken}` } }),
        },
      );
      return result.data;
    });
  }

  /**
   * ค้นหาส่วนลดที่ใช้ได้กับออเดอร์
   */
  async findApplicableDiscounts(
    siteId: string,
    orderData: {
      orderAmount: number;
      items?: unknown[];
      userId?: string;
      isFirstTime?: boolean;
    },
    token?: string,
  ): Promise<ApiResponse<DiscountInfo[]>> {
    if (!siteId) {
      return {
        success: false,
        type: 'failure',
        error: 'กรุณาระบุ siteId',
      };
    }

    const requestBody = {
      siteId,
      ...orderData,
    };

    const authToken = token || getToken();

    return this.handleRequest(async () => {
      const result = await this.makePublicRequest<{ data: DiscountInfo[]; }>(
        '/discount/find-applicable',
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          ...(authToken && { headers: { Authorization: `Bearer ${authToken}` } }),
        },
      );
      return result.data;
    });
  }
}

export const discountService = new DiscountService();

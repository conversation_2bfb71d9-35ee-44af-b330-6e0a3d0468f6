import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

/**
 * ✅ Invitation data interfaces
 */
export interface Invitation {
  _id: string;
  siteId: string;
  siteName: string;
  fromUserId: string;
  fromUserName: string;
  fromUserEmail: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  message?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: string;
  expiresAt: string;
}

export interface InvitationListResponse {
  invitations: Invitation[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * ✅ Invitation Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class InvitationService extends BaseService {
  /**
   * ✅ Get received invitations
   */
  async getReceivedInvitations(
    token: string,
  ): Promise<ApiResponse<{ invitations: Invitation[]; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { invitations: Invitation[]; }; }>(
        '/invitations/received',
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ Accept invitation
   */
  async acceptInvitation(
    invitationId: string,
    token: string,
  ): Promise<ApiResponse<{ message: string; }>> {
    console.log('🔍 Frontend: Accepting invitation ID:', invitationId);

    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!invitationId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Invitation ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { message: string; }; }>(
        `/invitations/${invitationId}/accept`,
        token,
        {
          method: 'POST',
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Reject invitation
   */
  async rejectInvitation(
    invitationId: string,
    token: string,
  ): Promise<ApiResponse<{ message: string; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!invitationId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Invitation ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { message: string; }; }>(
        `/invitations/${invitationId}/reject`,
        token,
        {
          method: 'POST',
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Join with invite code
   */
  async joinWithCode(
    inviteCode: string,
    token: string,
  ): Promise<ApiResponse<{ message: string; }>> {
    console.log('🔍 Frontend: Joining with code:', inviteCode);

    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!inviteCode?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'โค้ดเชิญไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { message: string; }; }>(
        `/invitations/${inviteCode}/accept`,
        token,
        {
          method: 'POST',
        },
      );
      return result.data;
    });
  }
}

// ✅ Export service instance
export const invitationService = new InvitationService();

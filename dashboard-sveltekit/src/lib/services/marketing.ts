import { apiUrl } from '$lib/config';
import type {
  Campaign,
  CampaignFilterData,
  CreateCampaignData,
  CreateDiscountData,
  Discount,
  DiscountFilterData,
  MarketingAnalytics,
  UpdateCampaignData,
  UpdateDiscountData,
} from '$lib/schemas/marketing.schema';

export interface MarketingServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CampaignListResponse {
  campaigns: Campaign[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface DiscountListResponse {
  discounts: Discount[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class MarketingService {
  private baseUrl = `${apiUrl}/marketing`;

  /**
   * ดึงรายการแคมเปญ
   */
  async getCampaigns(
    siteId: string,
    filters: Partial<CampaignFilterData>,
    token: string,
  ): Promise<MarketingServiceResponse<CampaignListResponse>> {
    try {
      const params = new URLSearchParams();

      // Add filters to params
      if (filters.type) params.append('type', filters.type);
      if (filters.status) params.append('status', filters.status);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.search) params.append('search', filters.search);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`${this.baseUrl}/${siteId}/campaigns?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงข้อมูลแคมเปญ',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Get campaigns error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ดึงรายการส่วนลด
   */
  async getDiscounts(
    siteId: string,
    filters: Partial<DiscountFilterData>,
    token: string,
  ): Promise<MarketingServiceResponse<DiscountListResponse>> {
    try {
      const params = new URLSearchParams();

      // Add filters to params
      if (filters.type) params.append('type', filters.type);
      if (filters.status) params.append('status', filters.status);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.search) params.append('search', filters.search);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`${apiUrl}/discount/${siteId}?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงข้อมูลส่วนลด',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Get discounts error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ดึงสถิติการตลาด
   */
  async getMarketingAnalytics(
    siteId: string,
    token: string,
  ): Promise<MarketingServiceResponse<MarketingAnalytics>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/analytics`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงสถิติการตลาด',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Get analytics error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * สร้างแคมเปญใหม่
   */
  async createCampaign(
    campaignData: CreateCampaignData,
    token: string,
  ): Promise<MarketingServiceResponse<Campaign>> {
    try {
      const response = await fetch(`${this.baseUrl}/${campaignData.siteId}/campaigns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(campaignData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการสร้างแคมเปญ',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Create campaign error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * อัปเดตแคมเปญ
   */
  async updateCampaign(
    campaignId: string,
    campaignData: UpdateCampaignData,
    siteId: string,
    token: string,
  ): Promise<MarketingServiceResponse<Campaign>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/campaigns/${campaignId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(campaignData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการอัปเดตแคมเปญ',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Update campaign error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ลบแคมเปญ
   */
  async deleteCampaign(
    campaignId: string,
    siteId: string,
    token: string,
  ): Promise<MarketingServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/campaigns/${campaignId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการลบแคมเปญ',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Delete campaign error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * สร้างส่วนลดใหม่
   */
  async createDiscount(
    discountData: CreateDiscountData,
    token: string,
  ): Promise<MarketingServiceResponse<Discount>> {
    try {
      const response = await fetch(`${apiUrl}/discount/${discountData.siteId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(discountData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการสร้างส่วนลด',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Create discount error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * อัปเดตส่วนลด
   */
  async updateDiscount(
    discountId: string,
    discountData: UpdateDiscountData,
    siteId: string,
    token: string,
  ): Promise<MarketingServiceResponse<Discount>> {
    try {
      const response = await fetch(`${apiUrl}/discount/${siteId}/${discountId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(discountData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการอัปเดตส่วนลด',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Update discount error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ลบส่วนลด
   */
  async deleteDiscount(
    discountId: string,
    siteId: string,
    token: string,
  ): Promise<MarketingServiceResponse> {
    try {
      const response = await fetch(`${apiUrl}/discount/${siteId}/${discountId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการลบส่วนลด',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Delete discount error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * เปิด/ปิดใช้งานแคมเปญ
   */
  async toggleCampaignStatus(
    campaignId: string,
    siteId: string,
    status: 'active' | 'paused',
    token: string,
  ): Promise<MarketingServiceResponse<Campaign>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/campaigns/${campaignId}/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ status }),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะแคมเปญ',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Toggle campaign status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * เปิด/ปิดใช้งานส่วนลด
   */
  async toggleDiscountStatus(
    discountId: string,
    siteId: string,
    status: 'active' | 'inactive',
    token: string,
  ): Promise<MarketingServiceResponse<Discount>> {
    try {
      const response = await fetch(`${apiUrl}/discount/${siteId}/${discountId}/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ status }),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะส่วนลด',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Toggle discount status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ตรวจสอบรหัสส่วนลด
   */
  async validateDiscountCode(
    code: string,
    siteId: string,
    token: string,
  ): Promise<MarketingServiceResponse<{ valid: boolean; discount?: Discount; reason?: string; }>> {
    try {
      const response = await fetch(`${apiUrl}/discount/${siteId}/validate/${code}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการตรวจสอบรหัสส่วนลด',
        };
      }
    }
    catch (error) {
      console.error('Marketing Service - Validate discount code error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }
}

// Export singleton instance
export const marketingService = new MarketingService();

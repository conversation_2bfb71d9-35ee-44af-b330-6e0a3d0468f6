import { apiUrl } from '$lib/config';
import type {
  CreateNotificationData,
  MarkAsReadData,
  Notification,
  NotificationFilterData,
  NotificationStatsData,
  UpdateNotificationData,
} from '$lib/schemas/notification.schema';

export interface NotificationServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface NotificationListResponse {
  notifications: Notification[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  stats: NotificationStatsData;
}

class NotificationService {
  private baseUrl = `${apiUrl}/notification`;

  /**
   * ดึงรายการการแจ้งเตือนทั้งหมด
   */
  async getNotifications(
    siteId: string,
    filters: Partial<NotificationFilterData>,
    token: string,
  ): Promise<NotificationServiceResponse<NotificationListResponse>> {
    try {
      const params = new URLSearchParams();

      // Add filters to params
      if (filters.type) params.append('type', filters.type);
      if (filters.priority) params.append('priority', filters.priority);
      if (filters.status) params.append('status', filters.status);
      if (filters.isRead !== undefined) params.append('isRead', filters.isRead.toString());
      if (filters.userId) params.append('userId', filters.userId);
      if (filters.relatedType) params.append('relatedType', filters.relatedType);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.search) params.append('search', filters.search);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`${this.baseUrl}/${siteId}?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงข้อมูลการแจ้งเตือน',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Get notifications error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ดึงสถิติการแจ้งเตือน
   */
  async getNotificationStats(
    siteId: string,
    token: string,
  ): Promise<NotificationServiceResponse<NotificationStatsData>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการดึงสถิติการแจ้งเตือน',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Get stats error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * สร้างการแจ้งเตือนใหม่
   */
  async createNotification(
    notificationData: CreateNotificationData,
    token: string,
  ): Promise<NotificationServiceResponse<Notification>> {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(notificationData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการสร้างการแจ้งเตือน',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Create notification error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * อัปเดตการแจ้งเตือน
   */
  async updateNotification(
    notificationId: string,
    updateData: UpdateNotificationData,
    token: string,
  ): Promise<NotificationServiceResponse<Notification>> {
    try {
      const response = await fetch(`${this.baseUrl}/${notificationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการอัปเดตการแจ้งเตือน',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Update notification error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ทำเครื่องหมายว่าอ่านแล้ว
   */
  async markAsRead(
    markAsReadData: MarkAsReadData,
    token: string,
  ): Promise<NotificationServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/mark-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(markAsReadData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการทำเครื่องหมายว่าอ่านแล้ว',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Mark as read error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ทำเครื่องหมายทั้งหมดว่าอ่านแล้ว
   */
  async markAllAsRead(
    siteId: string,
    token: string,
  ): Promise<NotificationServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/mark-all-read`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการทำเครื่องหมายทั้งหมดว่าอ่านแล้ว',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Mark all as read error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ลบการแจ้งเตือน
   */
  async deleteNotification(
    notificationId: string,
    token: string,
  ): Promise<NotificationServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการลบการแจ้งเตือน',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Delete notification error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ลบการแจ้งเตือนหลายรายการ
   */
  async deleteMultipleNotifications(
    notificationIds: string[],
    token: string,
  ): Promise<NotificationServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/bulk-delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ notificationIds }),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการลบการแจ้งเตือน',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Delete multiple notifications error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * เก็บถาวรการแจ้งเตือน
   */
  async archiveNotifications(
    notificationIds: string[],
    token: string,
  ): Promise<NotificationServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/archive`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ notificationIds }),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการเก็บถาวรการแจ้งเตือน',
        };
      }
    }
    catch (error) {
      console.error('Notification Service - Archive notifications error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();

import {
  type CreateOrderData,
  sanitizeOrderData,
  type UpdateOrderData,
  validateCreateOrderData,
  validateUpdateOrderData,
} from '$lib/schemas/order.schema';
import type { ApiResponse } from '$lib/types/common';
import type { Order } from '$lib/types/order';
import { BaseService } from './base';

/**
 * Order data interfaces
 */
export interface OrderListResponse {
  orders: Order[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * ✅ Order Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class OrderService extends BaseService {
  /**
   * ✅ Get orders with pagination and filters
   */
  async getOrders(
    siteId: string,
    token: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    },
  ): Promise<ApiResponse<{ orders: Order[]; total: number; page: number; limit: number; totalPages: number; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const result = await this.makeAuthenticatedRequest<{
        data: { orders: Order[]; total: number; page: number; limit: number; totalPages: number; };
      }>(`/order/dashboard/${siteId}/list?${queryParams}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get single order by ID
   */
  async getOrder(orderId: string, siteId: string, token: string): Promise<ApiResponse<Order>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!orderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Order ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Order; }>(
        `/order/dashboard/${siteId}/detail/${orderId}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new order
   */
  async createOrder(
    data: CreateOrderData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Order>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizeOrderData(data);
    const validation = validateCreateOrderData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Order; }>(
        `/order/dashboard/${siteId}/create`,
        token,
        {
          method: 'POST',
          body: validation.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing order
   */
  async updateOrder(
    orderId: string,
    data: UpdateOrderData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Order>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!orderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Order ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizeOrderData(data);
    const validation = validateUpdateOrderData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Order; }>(
        `/order/dashboard/${siteId}/update/${orderId}`,
        token,
        {
          method: 'PUT',
          body: validation.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete order
   */
  async deleteOrder(orderId: string, siteId: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!orderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Order ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/order/dashboard/${siteId}/delete/${orderId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Get order statistics
   */
  async getOrderStats(
    siteId: string,
    token: string,
    params?: {
      startDate?: string;
      endDate?: string;
    },
  ): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.startDate) queryParams.append('startDate', params.startDate);
      if (params?.endDate) queryParams.append('endDate', params.endDate);

      const result = await this.makeAuthenticatedRequest<{
        data: any;
      }>(`/order/dashboard/${siteId}/stats?${queryParams}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get order analytics
   */
  async getOrderAnalytics(
    siteId: string,
    token: string,
    params?: {
      period?: 'today' | 'week' | 'month' | 'year';
      startDate?: string;
      endDate?: string;
    },
  ): Promise<
    ApiResponse<{
      revenue: any;
      status: any;
      monthlyTrends: any;
    }>
  > {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.period) queryParams.append('period', params.period);
      if (params?.startDate) queryParams.append('startDate', params.startDate);
      if (params?.endDate) queryParams.append('endDate', params.endDate);

      const result = await this.makeAuthenticatedRequest<{
        data: {
          revenue: any;
          status: any;
          monthlyTrends: any;
        };
      }>(`/order/dashboard/${siteId}/analytics?${queryParams}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get recent orders
   */
  async getRecentOrders(
    siteId: string,
    token: string,
    limit: number = 10,
  ): Promise<ApiResponse<any[]>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: any[];
      }>(`/order/dashboard/${siteId}/recent?limit=${limit}`, token);
      return result.data;
    });
  }
}

export const orderService = new OrderService();

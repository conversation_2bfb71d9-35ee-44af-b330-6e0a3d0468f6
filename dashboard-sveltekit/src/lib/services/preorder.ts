import {
  type CreatePreorderData,
  sanitizePreorderData,
  type UpdatePreorderData,
  validateCreatePreorderData,
  validateUpdatePreorderData,
} from '$lib/schemas/preorder.schema';
import type { ApiResponse } from '$lib/types/common';
import type { Preorder } from '$lib/types/preorder';
import { BaseService } from './base';

/**
 * ✅ Preorder Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class PreorderService extends BaseService {
  /**
   * ✅ Get preorders with pagination and filters
   */
  async getPreorders(
    siteId: string,
    token: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      status?: 'pending' | 'confirmed' | 'cancelled';
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    },
  ): Promise<ApiResponse<{ preOrders: Preorder[]; pagination?: any; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const result = await this.makeAuthenticatedRequest<{
        data: { preOrders: Preorder[]; pagination?: any; };
      }>(`/preorder/dashboard/${siteId}/list?${queryParams}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get single preorder by ID
   */
  async getPreorder(
    preorderId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Preorder>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!preorderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Preorder ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Preorder; }>(
        `/preorder/dashboard/${siteId}/detail/${preorderId}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new preorder
   */
  async createPreorder(
    data: CreatePreorderData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Preorder>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizePreorderData(data);
    const validation = validateCreatePreorderData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Preorder; }>(
        `/preorder/dashboard/${siteId}/create`,
        token,
        {
          method: 'POST',
          body: validation.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing preorder
   */
  async updatePreorder(
    preorderId: string,
    data: UpdatePreorderData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Preorder>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!preorderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Preorder ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizePreorderData(data);
    const validation = validateUpdatePreorderData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Preorder; }>(
        `/preorder/dashboard/${siteId}/update/${preorderId}`,
        token,
        {
          method: 'PUT',
          body: validation.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete preorder
   */
  async deletePreorder(
    preorderId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!preorderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Preorder ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/preorder/dashboard/${siteId}/delete/${preorderId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Cancel preorder
   */
  async cancelPreorder(preorderId: string, siteId: string, token: string): Promise<ApiResponse<Preorder>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!preorderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Preorder ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Preorder; }>(
        `/preorder/dashboard/${siteId}/cancel/${preorderId}`,
        token,
        {
          method: 'PUT',
          body: { status: 'cancelled' },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Get preorder statistics
   */
  async getPreorderStats(siteId: string, token: string): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: any;
      }>(`/preorder/dashboard/${siteId}/stats`, token);
      return result.data;
    });
  }

  /**
   * ✅ Update preorder status
   */
  async updatePreorderStatus(
    preorderId: string,
    status: 'pending' | 'confirmed' | 'processing' | 'ready' | 'delivered' | 'cancelled',
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Preorder>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!preorderId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Preorder ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Preorder; }>(
        `/preorder/dashboard/${siteId}/status/${preorderId}`,
        token,
        {
          method: 'PUT',
          body: { status },
        },
      );
      return result.data;
    });
  }
}

export const preorderService = new PreorderService();

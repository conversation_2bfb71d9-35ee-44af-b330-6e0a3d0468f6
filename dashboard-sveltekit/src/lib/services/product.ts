import {
  type CreateProductData,
  type ProductStockData,
  sanitizeProductData,
  type UpdateProductData,
  validateCreateProductData,
  validateProductStockData,
  validateUpdateProductData,
} from '$lib/schemas/product.schema';
import type { ApiResponse } from '$lib/types/common';
import type { Product, ProductListResponse, ProductStatsResponse } from '$lib/types/product';
import { BaseService } from './base';

/**
 * ✅ Product Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class ProductService extends BaseService {
  /**
   * ✅ Get products with pagination and filters
   */
  async getProducts(
    siteId: string,
    token: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      categoryId?: string;
      status?: 'active' | 'inactive';
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    },
  ): Promise<ApiResponse<{ products: Product[]; total: number; page: number; limit: number; totalPages: number; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.categoryId) queryParams.append('categoryId', params.categoryId);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const result = await this.makeAuthenticatedRequest<{
        data: { products: Product[]; total: number; page: number; limit: number; totalPages: number; };
      }>(`/product/dashboard/${siteId}/list?${queryParams}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get single product by ID
   */
  async getProduct(
    productId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product; }>(
        `/product/dashboard/${siteId}/detail/${productId}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new product
   */
  async createProduct(
    data: CreateProductData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizeProductData(data);
    const validation = validateCreateProductData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    // Add siteId to the data
    const productData = {
      ...sanitizedData,
      siteId,
    };

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product; }>(
        `/product/dashboard/${siteId}/create`,
        token,
        {
          method: 'POST',
          body: productData,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing product
   */
  async updateProduct(
    productId: string,
    data: UpdateProductData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizeProductData(data);
    const validation = validateUpdateProductData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product; }>(
        `/product/dashboard/${siteId}/update/${productId}`,
        token,
        {
          method: 'PUT',
          body: sanitizedData,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete product
   */
  async deleteProduct(
    productId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/product/dashboard/${siteId}/delete/${productId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Get product statistics
   */
  async getProductStats(
    siteId: string,
    token: string,
  ): Promise<
    ApiResponse<
      { totalProducts: number; activeProducts: number; outOfStockProducts: number; lowStockProducts: number; }
    >
  > {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: { totalProducts: number; activeProducts: number; outOfStockProducts: number; lowStockProducts: number; };
      }>(`/product/dashboard/${siteId}/stats`, token);
      return result.data;
    });
  }

  /**
   * ✅ Update product stock
   */
  async updateProductStock(
    productId: string,
    data: ProductStockData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const validation = validateProductStockData(data);
    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product; }>(
        `/product/dashboard/${siteId}/stock/${productId}`,
        token,
        {
          method: 'PUT',
          body: data,
        },
      );
      return result.data;
    });
  }
  /**
   * ✅ Add product variant
   */
  async addProductVariant(
    productId: string,
    variantData: any,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product; }>(
        `/product/dashboard/${siteId}/variant/${productId}/add`,
        token,
        {
          method: 'POST',
          body: variantData,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update product variant
   */
  async updateProductVariant(
    productId: string,
    variantId: string,
    variantData: any,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Product>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!variantId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Variant ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Product; }>(
        `/product/dashboard/${siteId}/variant/${productId}/${variantId}`,
        token,
        {
          method: 'PUT',
          body: variantData,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete product variant
   */
  async deleteProductVariant(
    productId: string,
    variantId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!variantId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Variant ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/product/dashboard/${siteId}/variant/${productId}/${variantId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Check product stock
   */
  async checkProductStock(
    productId: string,
    quantity: number,
    siteId: string,
    token: string,
    variantId?: string,
  ): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!productId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Product ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: any; }>(
        `/product/dashboard/${siteId}/stock/${productId}/check`,
        token,
        {
          method: 'POST',
          body: { quantity, variantId },
        },
      );
      return result.data;
    });
  }
}

export const productService = new ProductService();

import {
  type CreateReviewData,
  sanitizeReviewData,
  type UpdateReviewData,
  validateCreateReviewData,
  validateUpdateReviewData,
} from '$lib/schemas/review.schema';
import type { ApiResponse } from '$lib/types/common';
import type { Review } from '$lib/types/review';
import { BaseService } from './base';

/**
 * ✅ Review Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class ReviewService extends BaseService {
  /**
   * ✅ Get reviews with pagination and filters
   */
  async getReviews(
    siteId: string,
    token: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      rating?: number;
      status?: 'pending' | 'approved' | 'rejected';
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    },
  ): Promise<ApiResponse<{ reviews: Review[]; pagination?: any; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.rating) queryParams.append('rating', params.rating.toString());
      if (params?.status) queryParams.append('status', params.status);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const result = await this.makeAuthenticatedRequest<{
        data: { reviews: Review[]; pagination?: any; };
      }>(`/review/dashboard/${siteId}/list?${queryParams}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get single review by ID
   */
  async getReview(
    reviewId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Review>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!reviewId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Review ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Review; }>(
        `/review/dashboard/${siteId}/detail/${reviewId}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new review
   */
  async createReview(
    data: CreateReviewData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Review>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizeReviewData(data);
    const validation = validateCreateReviewData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Review; }>(
        `/review/dashboard/${siteId}/create`,
        token,
        {
          method: 'POST',
          body: validation.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing review
   */
  async updateReview(
    reviewId: string,
    data: UpdateReviewData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Review>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!reviewId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Review ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizeReviewData(data);
    const validation = validateUpdateReviewData(sanitizedData);

    if (!validation.success) {
      return {
        type: 'failure',
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Review; }>(
        `/review/dashboard/${siteId}/update/${reviewId}`,
        token,
        {
          method: 'PUT',
          body: validation.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete review
   */
  async deleteReview(
    reviewId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!reviewId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Review ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/review/dashboard/${siteId}/delete/${reviewId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Approve review
   */
  async approveReview(
    reviewId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Review>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!reviewId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Review ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Review; }>(
        `/review/dashboard/${siteId}/approve/${reviewId}`,
        token,
        {
          method: 'PUT',
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Reject review
   */
  async rejectReview(
    reviewId: string,
    siteId: string,
    token: string,
    reason?: string,
  ): Promise<ApiResponse<Review>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!reviewId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Review ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Review; }>(
        `/review/dashboard/${siteId}/reject/${reviewId}`,
        token,
        {
          method: 'PUT',
          body: { reason },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Toggle review visibility
   */
  async toggleReviewVisibility(
    reviewId: string,
    isVisible: boolean,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Review>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!reviewId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Review ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Review; }>(
        `/review/dashboard/${siteId}/visibility/${reviewId}`,
        token,
        {
          method: 'PUT',
          body: { isVisible },
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Get review statistics
   */
  async getReviewStats(siteId: string, token: string): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: any;
      }>(`/review/dashboard/${siteId}/stats`, token);
      return result.data;
    });
  }
}

export const reviewService = new ReviewService();

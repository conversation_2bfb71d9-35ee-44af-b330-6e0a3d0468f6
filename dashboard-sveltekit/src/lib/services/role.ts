import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';
import { makeRequest } from './base';

/**
 * Role data interfaces
 */
export interface Role {
  _id: string;
  name: string;
  description?: string;
  permissions: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RoleListResponse {
  roles: Role[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface Invitation {
  _id: string;
  email: string;
  roleId: string;
  roleName: string;
  status: 'pending' | 'accepted' | 'expired';
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface InvitationListResponse {
  invitations: Invitation[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * ✅ Role Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class RoleService extends BaseService {
  /**
   * ✅ Create invitation
   */
  async createInvitation(
    siteId: string,
    data: {
      toEmail: string;
      role: 'owner' | 'admin' | 'editor' | 'viewer';
      message?: string;
    },
    token: string,
  ): Promise<ApiResponse<Invitation>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Invitation; }>(
        `/role/${siteId}/invitations`,
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Get sent invitations
   */
  async getSentInvitations(
    siteId: string,
    token: string,
  ): Promise<ApiResponse<{ invitations: Invitation[]; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: { invitations: Invitation[]; };
      }>(`/role/${siteId}/invitations/sent`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get roles with pagination
   */
  async getRoles(
    siteId: string,
    token: string,
  ): Promise<ApiResponse<{ roles: Role[]; total: number; page: number; limit: number; totalPages: number; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: { roles: Role[]; total: number; page: number; limit: number; totalPages: number; };
      }>(`/role/${siteId}/roles`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get invitations with pagination
   */
  async getInvitations(
    siteId: string,
    token: string,
  ): Promise<
    ApiResponse<{ invitations: Invitation[]; total: number; page: number; limit: number; totalPages: number; }>
  > {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: { invitations: Invitation[]; total: number; page: number; limit: number; totalPages: number; };
      }>(`/role/${siteId}/invitations`, token);
      return result.data;
    });
  }

  /**
   * ✅ Create new role
   */
  async createRole(
    data: {
      name: string;
      description?: string;
      permissions: string[];
    },
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Role>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Role; }>(
        `/role/${siteId}/roles`,
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing role
   */
  async updateRole(
    roleId: string,
    data: {
      name?: string;
      description?: string;
      permissions?: string[];
      isActive?: boolean;
    },
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Role>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!roleId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Role ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Role; }>(
        `/role/${siteId}/roles/${roleId}`,
        token,
        {
          method: 'PUT',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete role
   */
  async deleteRole(roleId: string, siteId: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!roleId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Role ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/role/${siteId}/roles/${roleId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Send invitation
   */
  async sendInvitation(
    data: {
      email: string;
      roleId: string;
    },
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Invitation>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Invitation; }>(
        `/role/${siteId}/invitations`,
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Cancel invitation
   */
  async cancelInvitation(
    invitationId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!invitationId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Invitation ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/role/${siteId}/invitations/${invitationId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Generate invitation link
   */
  async generateInvitationLink(invitationId: string, token: string): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!invitationId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Invitation ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: any; }>(
        `/invitations/${invitationId}/link`,
        token,
        {
          method: 'GET',
        },
      );
      return result.data;
    });
  }
}

export const roleService = new RoleService();

// รับคำเชิญด้วยโค้ด
export async function getInvitationByCode(inviteCode: string) {
  return makeRequest(`/invitations/code/${inviteCode}`, {
    method: 'GET',
  });
}

import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

/**
 * Settings data interfaces - Updated to match schema
 */
export interface SiteSettings {
  _id: string;
  siteName: string;
  siteDescription?: string;
  siteUrl?: string;
  logo?: string;
  favicon?: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
  };
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
    ogImage?: string;
  };
  contact: {
    email?: string;
    phone?: string;
    address?: string;
  };
  social: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    line?: string;
  };
  features: {
    enableCart: boolean;
    enableReviews: boolean;
    enableWishlist: boolean;
    enableChat: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
  };
  security: {
    twoFactorAuth: boolean;
    passwordPolicy: string;
    sessionTimeout: number;
  };
  updatedAt: string;
}

export interface PaymentGateway {
  _id: string;
  name: string;
  provider: 'stripe' | 'omise' | 'promptpay' | 'truemoney';
  status: 'active' | 'inactive';
  config: {
    publicKey?: string;
    secretKey?: string;
    webhookUrl?: string;
  };
  fees: {
    percentage: number;
    fixed: number;
  };
  createdAt: string;
}

/**
 * ✅ Settings Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class SettingsService extends BaseService {
  /**
   * ✅ Get site settings
   */
  async getSiteSettings(siteId: string, token: string): Promise<ApiResponse<SiteSettings>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: SiteSettings;
      }>(`/settings/dashboard/${siteId}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Update site settings
   */
  async updateSiteSettings(
    data: any,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<SiteSettings>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate required fields
    if (!data.siteName?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณาระบุชื่อเว็บไซต์',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: SiteSettings; }>(
        `/settings/dashboard/${siteId}`,
        token,
        {
          method: 'PUT',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Get payment gateways
   */
  async getPaymentGateways(siteId: string, token: string): Promise<ApiResponse<{ gateways: PaymentGateway[]; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: { gateways: PaymentGateway[]; };
      }>(`/settings/dashboard/${siteId}/payment-gateways`, token);
      return result.data;
    });
  }

  /**
   * ✅ Update payment gateway
   */
  async updatePaymentGateway(
    gatewayId: string,
    data: {
      isActive?: boolean;
      config?: Record<string, any>;
    },
    siteId: string,
    token: string,
  ): Promise<ApiResponse<PaymentGateway>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!gatewayId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Gateway ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: PaymentGateway; }>(
        `/settings/dashboard/${siteId}/payment-gateways/${gatewayId}`,
        token,
        {
          method: 'PUT',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Get notification settings
   */
  async getNotificationSettings(siteId: string, token: string): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: any;
      }>(`/settings/dashboard/${siteId}/notifications`, token);
      return result.data;
    });
  }

  /**
   * ✅ Update notification settings
   */
  async updateNotificationSettings(
    data: {
      emailNotifications?: boolean;
      smsNotifications?: boolean;
      pushNotifications?: boolean;
    },
    siteId: string,
    token: string,
  ): Promise<ApiResponse<any>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: any;
      }>(`/settings/dashboard/${siteId}/notifications`, token, {
        method: 'PUT',
        body: data,
      });
      return result.data;
    });
  }

  /**
   * ✅ Create payment gateway
   */
  async createPaymentGateway(
    data: any,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<PaymentGateway>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate required fields
    if (!data.name?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณากรอกชื่อ Payment Gateway',
      };
    }

    if (!data.provider?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'กรุณาเลือก Provider',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: PaymentGateway; }>(
        `/settings/dashboard/${siteId}/payment-gateways`,
        token,
        {
          method: 'POST',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete payment gateway
   */
  async deletePaymentGateway(
    gatewayId: string,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<{ message: string; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!gatewayId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Gateway ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: { message: string; }; }>(
        `/settings/dashboard/${siteId}/payment-gateways/${gatewayId}`,
        token,
        {
          method: 'DELETE',
        },
      );
      return result.data;
    });
  }
}

export const settingsService = new SettingsService();

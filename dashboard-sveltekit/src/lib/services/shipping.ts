import {
  type CreateShippingData,
  type CreateShippingMethodData,
  sanitizeShippingData,
  sanitizeShippingMethodData,
  type UpdateShippingData,
  validateCreateShippingData,
  validateCreateShippingMethodData,
  validateUpdateShippingData,
} from '$lib/schemas/shipping.schema';
import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

/**
 * Shipping data interfaces
 */
export interface Shipping {
  _id: string;
  orderId: string;
  trackingNumber?: string;
  carrier: string;
  method: string;
  status: 'preparing' | 'shipped' | 'in_transit' | 'delivered' | 'failed' | 'returned';
  shippingAddress: {
    name: string;
    phone: string;
    address: string;
    district: string;
    province: string;
    postalCode: string;
  };
  estimatedDeliveryDate?: string;
  actualDeliveryDate?: string;
  shippingCost: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ShippingMethod {
  _id: string;
  name: string;
  carrier: string;
  cost: number;
  estimatedDays: number;
  isActive: boolean;
}

/**
 * ✅ Shipping Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class ShippingService extends BaseService {
  /**
   * ✅ Get shippings with pagination and filters
   */
  async getShippings(
    siteId: string,
    token: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      status?: Shipping['status'];
      carrier?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    },
  ): Promise<ApiResponse<{ shippings: Shipping[]; pagination?: any; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.carrier) queryParams.append('carrier', params.carrier);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const result = await this.makeAuthenticatedRequest<{
        data: { shippings: Shipping[]; pagination?: any; };
      }>(`/shipping/${siteId}?${queryParams}`, token);
      return result.data;
    });
  }

  /**
   * ✅ Get single shipping by ID
   */
  async getShipping(shippingId: string, siteId: string, token: string): Promise<ApiResponse<Shipping>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!shippingId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Shipping ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Shipping; }>(
        `/shipping/${siteId}/${shippingId}`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ Create new shipping
   */
  async createShipping(
    data: CreateShippingData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Shipping>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input using schema
    const sanitizedData = sanitizeShippingData(data);
    const validationResult = validateCreateShippingData(sanitizedData);

    if (!validationResult.success) {
      return {
        type: 'failure',
        success: false,
        error: validationResult.error,
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Shipping; }>(
        `/shipping/${siteId}`,
        token,
        {
          method: 'POST',
          body: validationResult.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update existing shipping
   */
  async updateShipping(
    shippingId: string,
    data: UpdateShippingData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<Shipping>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!shippingId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Shipping ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input using schema
    const sanitizedData = sanitizeShippingData(data);
    const validationResult = validateUpdateShippingData(sanitizedData);

    if (!validationResult.success) {
      return {
        type: 'failure',
        success: false,
        error: validationResult.error,
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Shipping; }>(
        `/shipping/${siteId}/${shippingId}`,
        token,
        {
          method: 'PUT',
          body: validationResult.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete shipping
   */
  async deleteShipping(shippingId: string, siteId: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!shippingId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Shipping ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/shipping/${siteId}/${shippingId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }

  /**
   * ✅ Get shipping methods
   */
  async getShippingMethods(
    siteId: string,
    token: string,
  ): Promise<ApiResponse<{ methods: ShippingMethod[]; }>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: { methods: ShippingMethod[]; };
      }>(`/shipping/${siteId}/methods`, token);
      return result.data;
    });
  }

  /**
   * ✅ Create shipping method
   */
  async createShippingMethod(
    data: CreateShippingMethodData,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<ShippingMethod>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    // Validate input using schema
    const sanitizedData = sanitizeShippingMethodData(data);
    const validationResult = validateCreateShippingMethodData(sanitizedData);

    if (!validationResult.success) {
      return {
        type: 'failure',
        success: false,
        error: validationResult.error,
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: ShippingMethod; }>(
        `/shipping/${siteId}/methods`,
        token,
        {
          method: 'POST',
          body: validationResult.data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Update shipping method
   */
  async updateShippingMethod(
    methodId: string,
    data: Partial<CreateShippingMethodData>,
    siteId: string,
    token: string,
  ): Promise<ApiResponse<ShippingMethod>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!methodId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Method ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: ShippingMethod; }>(
        `/shipping/${siteId}/methods/${methodId}`,
        token,
        {
          method: 'PUT',
          body: data,
        },
      );
      return result.data;
    });
  }

  /**
   * ✅ Delete shipping method
   */
  async deleteShippingMethod(methodId: string, siteId: string, token: string): Promise<ApiResponse<void>> {
    if (!token?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    if (!methodId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Method ID ไม่ถูกต้อง',
      };
    }

    if (!siteId?.trim()) {
      return {
        type: 'failure',
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makeAuthenticatedRequest<void>(
        `/shipping/${siteId}/methods/${methodId}`,
        token,
        {
          method: 'DELETE',
        },
      );
    });
  }
}

export const shippingService = new ShippingService();

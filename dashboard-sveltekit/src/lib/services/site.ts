import {
  // type UpdateSiteData,
  // updateSiteSchema,
  type CheckDomainData,
  checkDomainSchema,
  type CreateSiteData,
  createSiteSchema,
  sanitizeFormData,
  validateZodSchema,
} from '$lib/schemas';

import type { ApiResponse } from '$lib/types/common';
import type { Site } from '$lib/types/site';
import { BaseService } from './base';

/**
 * ✅ Site Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
class SiteService extends BaseService {
  /**
   * ✅ Get single site by ID
   */
  async getSite(siteId: string, token: string): Promise<ApiResponse<Site>> {
    if (!siteId?.trim()) {
      return {
        success: false,
        type: 'failure',
        message: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!token?.trim()) {
      return {
        success: false,
        type: 'failure',
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: Site; }>(
        `/site/${siteId}/content`,
        token,
      );
      return result.data;
    });
  }

  /**
   * ✅ Check domain availability
   */
  async checkDomain(
    data: CheckDomainData,
  ): Promise<ApiResponse<{ available: boolean; message: string; fullDomain?: string; statusMessage?: string; }>> {
    // Validate input
    const sanitizedData = sanitizeFormData(data);
    const validationResult = validateZodSchema(checkDomainSchema, sanitizedData);

    if (!validationResult.success) {
      return {
        success: false,
        type: 'failure',
        message: Object.values(validationResult.errors ?? {}).join(', '),
      };
    }

    return this.handleRequest(async () => {
      const queryParams = new URLSearchParams();
      if (sanitizedData.typeDomain) queryParams.append('typeDomain', sanitizedData.typeDomain);
      if (sanitizedData.subDomain) queryParams.append('subDomain', sanitizedData.subDomain);
      if (sanitizedData.mainDomain) queryParams.append('mainDomain', sanitizedData.mainDomain);
      if (sanitizedData.customDomain) {
        queryParams.append('customDomain', sanitizedData.customDomain);
      }

      const result = await this.makePublicRequest<{
        data: { available: boolean; fullDomain: string; };
        message: string;
      }>(`/site/check-domain?${queryParams}`);

      return {
        available: result.data.available,
        message: result.message,
        fullDomain: result.data.fullDomain,
      };
    });
  }

  /**
   * ✅ Create site
   */
  async createSite(siteData: CreateSiteData, token: string): Promise<ApiResponse<Site>> {
    if (!token?.trim()) {
      return {
        success: false,
        type: 'failure',
        message: 'Token ไม่ถูกต้อง',
      };
    }

    // Validate input
    const sanitizedData = sanitizeFormData(siteData, ['siteName']);
    const validationResult = validateZodSchema(createSiteSchema, sanitizedData);
    console.log('sanitizedData', sanitizedData);

    if (!validationResult.success) {
      return {
        success: false,
        type: 'failure',
        message: Object.values(validationResult.errors ?? {}).join(', '),
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{
        data: { site: Site; resRegisterDomain: any; resCheck: any; };
      }>('/site/create', token, {
        method: 'POST',
        body: {
          name: sanitizedData.siteName,
          typeDomain: sanitizedData.typeDomain,
          subDomain: sanitizedData.subDomain,
          mainDomain: sanitizedData.mainDomain,
          customDomain: sanitizedData.customDomain,
          plan: sanitizedData.packageType,
        },
      });
      return result.data.site;
    });
  }

  /**
   * ✅ Create site with validation using Zod schema
   */
  async createSiteWithValidation(
    siteData: CreateSiteData,
    userMoneyPoint: number,
    token?: string,
  ): Promise<ApiResponse<Site>> {
    try {
      // ตรวจสอบเงินในบัญชี
      if (userMoneyPoint < 50) {
        return {
          success: false,
          type: 'failure',
          message: 'เงินในบัญชีไม่เพียงพอ (ต้องมีอย่างน้อย 50 บาท)',
        };
      }

      // ✅ Zod Schema Validation
      const sanitizedData = sanitizeFormData(siteData);
      const validationResult = validateZodSchema(createSiteSchema, sanitizedData);

      console.log('sanitizedData', validationResult);

      if (!validationResult.success) {
        return {
          success: false,
          type: 'failure',
          message: Object.values(validationResult.errors ?? {}).join(', '),
        };
      }

      // ใช้ validated data
      const validatedData = validationResult.data!;
      console.log('validatedData', validatedData);

      // ตรวจสอบโดเมนก่อนสร้าง
      const domainCheck = await this.checkDomain({
        typeDomain: validatedData.typeDomain,
        subDomain: validatedData.subDomain,
        mainDomain: validatedData.mainDomain,
        customDomain: validatedData.customDomain,
      });

      console.log('domainCheck', domainCheck);

      if (!domainCheck.success) {
        return {
          success: false,
          type: 'failure',
          message: domainCheck.error || 'ไม่สามารถตรวจสอบโดเมนได้',
        };
      }

      if (!domainCheck.data?.available) {
        return {
          success: false,
          type: 'failure',
          message: domainCheck.data?.message || 'โดเมนนี้ไม่สามารถใช้งานได้',
        };
      }

      // สร้างเว็บไซต์
      if (!token) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่ถูกต้อง',
        };
      }

      const createResult = await this.createSite(validatedData, token);
      console.log('createResult', createResult);

      return createResult;
    }
    catch (error) {
      console.error('Error creating site with validation:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์',
      };
    }
  }

  /**
   * ✅ Get user sites
   */
  async getUserSites(token?: string): Promise<ApiResponse<Site[]>> {
    try {
      if (!token?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่ถูกต้อง',
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{
          data: { sites: Site[]; };
        }>('/site/my-sites', token, {
          method: 'GET',
        });
        return result.data.sites;
      });
    }
    catch (error) {
      console.error('Error fetching user sites:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์ของผู้ใช้',
      };
    }
  }

  /**
   * ✅ Get user sites with pagination
   */
  async getUserSitesWithPagination(
    params: {
      page?: string;
      limit?: string;
      search?: string;
      status?: string;
    },
    token?: string,
  ): Promise<
    ApiResponse<{
      sites: Site[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    try {
      if (!token?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่ถูกต้อง',
        };
      }

      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page);
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.search) queryParams.append('search', params.search);
      if (params.status) queryParams.append('status', params.status);

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{
          data: {
            sites: Site[];
            pagination: {
              page: number;
              limit: number;
              total: number;
              totalPages: number;
            };
          };
        }>(`/site/my-sites?${queryParams}`, token, {
          method: 'GET',
        });
        return result.data;
      });
    }
    catch (error) {
      console.error('Error fetching user sites with pagination:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์ของผู้ใช้',
      };
    }
  }

  /**
   * ✅ Get site packages
   */
  async getSitePackages(): Promise<ApiResponse<any[]>> {
    try {
      return this.handleRequest(async () => {
        const result = await this.makePublicRequest<{ data: any[]; }>('/site/packages', {
          method: 'GET',
        });
        return result.data;
      });
    }
    catch (error) {
      console.error('Error fetching site packages:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ',
      };
    }
  }

  /**
   * ✅ Get site packages history
   */
  async getHistorySitePackages(siteId: string, token: string): Promise<ApiResponse<any[]>> {
    // const token = getToken();
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Site ID ไม่ถูกต้อง',
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่ถูกต้อง',
        };
      }
      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any[]; }>('/site/packages', token, {
          method: 'GET',
        });
        return result.data;
      });
    }
    catch (error) {
      console.error('Error fetching site packages:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ',
      };
    }
  }

  async rentPackage(
    siteId: string,
    subscriptionId: string,
    token: string,
  ): Promise<ApiResponse<any>> {
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Site ID ไม่ถูกต้อง',
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่  ',
        };
      }

      if (!subscriptionId?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Subscription ID ไม่ถูกต้อง',
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any; }>(
          `/site/${siteId}/renew`,
          token,
          {
            method: 'POST',
            body: { subscriptionId },
          },
        );
        return result.data;
      });
    }
    catch (error) {
      console.error('Error renewing subscription:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการต่ออายุsubscription',
      };
    }
  }

  /**
   * ✅ Get site theme settings
   */
  async getSiteTheme(siteId: string, token?: string): Promise<ApiResponse<any>> {
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Site ID ไม่ถูกต้อง',
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่ถูกต้อง',
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any; }>(
          `/site/${siteId}/theme`,
          token,
          {
            method: 'GET',
          },
        );
        return result.data;
      });
    }
    catch (error) {
      console.error('Error fetching site theme:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการดึงข้อมูลธีม',
      };
    }
  }

  /**
   * ✅ Update site theme settings
   */
  async updateSiteTheme(siteId: string, themeData: any, token?: string): Promise<ApiResponse<any>> {
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Site ID ไม่ถูกต้อง',
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่ถูกต้อง',
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any; }>(
          `/site/${siteId}/theme`,
          token,
          {
            method: 'PUT',
            body: JSON.stringify(themeData),
          },
        );
        return result.data;
      });
    }
    catch (error) {
      console.error('Error updating site theme:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการอัปเดตธีม',
      };
    }
  }

  /**
   * ✅ Reset site theme settings
   */
  async resetSiteTheme(siteId: string, token?: string): Promise<ApiResponse<any>> {
    try {
      if (!siteId?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Site ID ไม่ถูกต้อง',
        };
      }

      if (!token?.trim()) {
        return {
          success: false,
          type: 'failure',
          message: 'Token ไม่ถูกต้อง',
        };
      }

      return this.handleRequest(async () => {
        const result = await this.makeAuthenticatedRequest<{ data: any; }>(
          `/site/${siteId}/theme/reset`,
          token,
          {
            method: 'POST',
          },
        );
        return result.data;
      });
    }
    catch (error) {
      console.error('Error resetting site theme:', error);
      return {
        success: false,
        type: 'failure',
        message: 'เกิดข้อผิดพลาดในการรีเซ็ตธีม',
      };
    }
  }
  /**
   * ✅ Update site information
   */
  // async updateSite(
  //   siteId: string,
  //   data: UpdateSiteData,
  //   token: string,
  // ): Promise<ApiResponse<Site>> {
  //   if (!siteId?.trim()) {
  //     return {
  //       success: false,
  //       type: 'failure',
  //       message: 'Site ID ไม่ถูกต้อง',
  //     };
  //   }

  //   if (!token?.trim()) {
  //     return {
  //       success: false,
  //       type: 'failure',
  //       message: 'Token ไม่ถูกต้อง',
  //     };
  //   }

  //   // Validate input
  //   const sanitizedData = sanitizeFormData(data);
  //   const validationError = validateUpdateSiteData(sanitizedData);

  //   if (validationError) {
  //     return {
  //       success: false,
  //       type: 'failure',
  //       message: validationError.errors?.general || 'ข้อมูลไม่ถูกต้อง',
  //     };
  //   }

  //   return this.handleRequest(async () => {
  //     const result = await this.makeAuthenticatedRequest<{ data: Site; }>(`/site/${siteId}`, token, {
  //       method: 'PUT',
  //       body: sanitizedData,
  //     });
  //     return result.data;
  //   });
  // }

  /**
   * ✅ Delete site
   */
  // async deleteSite(siteId: string, token: string): Promise<ApiResponse<void>> {
  //   if (!siteId?.trim()) {
  //     return {
  //       success: false,
  //       type: 'failure',
  //       message: 'Site ID ไม่ถูกต้อง',
  //     };
  //   }

  //   if (!token?.trim()) {
  //     return {
  //       success: false,
  //       type: 'failure',
  //       message: 'Token ไม่ถูกต้อง',
  //     };
  //   }

  //   return this.handleRequest(async () => {
  //     await this.makeAuthenticatedRequest<void>(`/site/${siteId}`, token, {
  //       method: 'DELETE',
  //     });
  //   });
  // }

  /**
   * ✅ Get site statistics
   */
  async getSiteStats(siteId: string, token: string): Promise<ApiResponse<any>> {
    if (!siteId?.trim()) {
      return {
        success: false,
        type: 'failure',
        message: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!token?.trim()) {
      return {
        success: false,
        type: 'failure',
        message: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: any; }>(
        `/site/${siteId}/stats`,
        token,
      );
      return result.data;
    });
  }
}

export const siteService = new SiteService();

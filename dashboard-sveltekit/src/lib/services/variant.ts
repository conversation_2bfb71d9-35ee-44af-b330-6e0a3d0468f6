import { apiUrl } from '$lib/config';
import type { CreateVariantData, UpdateVariantData, Variant } from '$lib/schemas/variant.schema';

export interface VariantServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

class VariantService {
  private baseUrl = `${apiUrl}/product/dashboard`;

  /**
   * เพิ่มตัวเลือกสินค้าใหม่
   */
  async createVariant(
    variantData: CreateVariantData,
    productId: string,
    siteId: string,
    token: string,
  ): Promise<VariantServiceResponse<Variant>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/variant/${productId}/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(variantData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการเพิ่มตัวเลือกสินค้า',
        };
      }
    }
    catch (error) {
      console.error('Variant Service - Create variant error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * อัปเดตตัวเลือกสินค้า
   */
  async updateVariant(
    variantData: UpdateVariantData,
    variantId: string,
    productId: string,
    siteId: string,
    token: string,
  ): Promise<VariantServiceResponse<Variant>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/variant/${productId}/${variantId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(variantData),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการอัปเดตตัวเลือกสินค้า',
        };
      }
    }
    catch (error) {
      console.error('Variant Service - Update variant error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * ลบตัวเลือกสินค้า
   */
  async deleteVariant(
    variantId: string,
    productId: string,
    siteId: string,
    token: string,
  ): Promise<VariantServiceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/variant/${productId}/${variantId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการลบตัวเลือกสินค้า',
        };
      }
    }
    catch (error) {
      console.error('Variant Service - Delete variant error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * เปิด/ปิดใช้งานตัวเลือกสินค้า
   */
  async toggleVariantStatus(
    variantId: string,
    productId: string,
    siteId: string,
    isActive: boolean,
    token: string,
  ): Promise<VariantServiceResponse<Variant>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/variant/${productId}/${variantId}/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ isActive }),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะตัวเลือกสินค้า',
        };
      }
    }
    catch (error) {
      console.error('Variant Service - Toggle variant status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }

  /**
   * อัปเดตสต็อกตัวเลือกสินค้า
   */
  async updateVariantStock(
    variantId: string,
    productId: string,
    siteId: string,
    stock: number,
    token: string,
  ): Promise<VariantServiceResponse<Variant>> {
    try {
      const response = await fetch(`${this.baseUrl}/${siteId}/variant/${productId}/${variantId}/stock`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ stock }),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP Error: ${response.status}`,
        };
      }

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'เกิดข้อผิดพลาดในการอัปเดตสต็อก',
        };
      }
    }
    catch (error) {
      console.error('Variant Service - Update variant stock error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
      };
    }
  }
}

// Export singleton instance
export const variantService = new VariantService();

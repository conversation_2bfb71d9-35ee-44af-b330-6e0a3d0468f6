import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { authService } from '../services/auth';

export interface User {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  cover?: string;
  isEmailVerified: boolean;
  moneyPoint: number;
  goldPoint: number;
  role?: 'admin' | 'user' | 'moderator';
  status?: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface SigninData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignupData {
  email: string;
  password: string;
  confirmPassword: string;
}

class AuthStore {
  private _user = $state<User | null>(null);
  private _isLoading = $state(false);
  private _error = $state<string | null>(null);
  private _isInitialized = $state(false);
  private _broadcastChannel: BroadcastChannel | null = null;
  private _storageListener: ((event: StorageEvent) => void) | null = null;
  private _currentTabId: string = '';

  constructor() {
    if (browser) {
      this._currentTabId = Math.random().toString(36).substring(2, 11);
      this.setupCrossTabSync();
      this._isInitialized = true;
    }
  }

  get user() {
    return this._user;
  }

  get isAuthenticated() {
    return this._user !== null;
  }

  get isLoading() {
    return this._isLoading;
  }

  get error() {
    return this._error;
  }

  get isAdmin() {
    return this._user?.role === 'admin';
  }

  get isModerator() {
    return this._user?.role === 'moderator' || this._user?.role === 'admin';
  }

  get isInitialized() {
    return this._isInitialized;
  }

  // Token status for debugging
  get tokenStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      hasUser: !!this._user,
      userId: this._user?._id,
      userEmail: this._user?.email,
      note: 'Tokens are managed by server-side httpOnly cookies',
    };
  }

  // ตั้งค่า user จาก SSR
  setUserFromSSR(user: User | null) {
    this._user = user;
    this._isInitialized = true;
  }

  private setupCrossTabSync() {
    // ใช้ BroadcastChannel API สำหรับ modern browsers
    if ('BroadcastChannel' in window) {
      try {
        this._broadcastChannel = new BroadcastChannel('auth-sync');
        this._broadcastChannel.onmessage = event => {
          this.handleAuthMessage(event.data);
        };
      }
      catch (error) {
        console.error('Failed to create BroadcastChannel:', error);
      }
    }

    // ใช้ localStorage events สำหรับ fallback
    this._storageListener = event => {
      if (event.key === 'auth-event' && event.newValue) {
        try {
          const data = JSON.parse(event.newValue);
          this.handleAuthMessage(data);
        }
        catch (error) {
          console.error('Error parsing auth event:', error);
        }
      }
    };

    window.addEventListener('storage', this._storageListener);
  }

  private broadcastAuthEvent(type: string, payload?: any) {
    const message = {
      type,
      payload,
      timestamp: Date.now(),
      tabId: this._currentTabId, // ใช้ tabId เดียวกัน
    };
    console.log('Broadcasting auth event:', message);

    // ส่งผ่าน BroadcastChannel (ใช้ try-catch แทนการตรวจสอบ readyState)
    if (this._broadcastChannel) {
      try {
        this._broadcastChannel.postMessage(message);
        console.log('Message sent via BroadcastChannel');
      }
      catch (error) {
        console.error('Failed to send message via BroadcastChannel:', error);
      }
    }

    // ส่งผ่าน localStorage (fallback) - เพิ่มการตรวจสอบ
    if (browser && type !== 'token-refresh') {
      // ไม่ส่ง token-refresh ผ่าน localStorage
      try {
        localStorage.setItem('auth-event', JSON.stringify(message));
        // ลบ event หลังจากส่ง
        setTimeout(() => {
          localStorage.removeItem('auth-event');
        }, 100);
        console.log('Message sent via localStorage');
      }
      catch (error) {
        console.error('Failed to send message via localStorage:', error);
      }
    }
  }

  private handleAuthMessage(data: { type: string; payload?: any; tabId?: string; }) {
    // ตรวจสอบว่าเป็น event จากแท็บเดียวกันหรือไม่
    if (data.tabId === this._currentTabId) {
      return;
    }

    switch (data.type) {
      case 'login':
        if (data.payload) {
          this._user = data.payload.user;
          this._isInitialized = true;

          // Redirect ไป dashboard ถ้าอยู่ที่หน้า signin
          if (window.location.pathname === '/signin') {
            goto('/dashboard');
          }
        }
        break;
      case 'signout':
        this._user = null;
        this._isInitialized = true;
        goto('/signin');
        break;
      case 'user-updated':
        if (data.payload) {
          this._user = data.payload.user;
        }
        break;
    }
  }

  private loadUserFromStorage() {
    try {
      // ✅ HttpOnly cookies ไม่สามารถอ่านได้จาก JavaScript
      // ใน Token Rotation System, tokens จะถูกจัดการโดย server
      console.log('AuthStore: HttpOnly cookies cannot be read from client-side JavaScript');
      console.log('AuthStore: Tokens will be managed by server-side hooks');

      // ✅ ไม่ต้องโหลด tokens จาก cookies เพราะเป็น httpOnly
      // user data และ tokens จะมาจาก SSR ผ่าน setUserFromSSR()
    }
    catch (error) {
      console.error('AuthStore: Error in loadUserFromStorage:', error);
    }
  }

  private saveUserToStorage(user: User) {
    if (browser) {
      // ข้อมูล user และ tokens จะเก็บใน httpOnly cookies เท่านั้น
      // เก็บเฉพาะใน memory สำหรับการใช้งานใน client
      this._user = user;
    }
  }

  private clearStorage() {
    if (browser) {
      // ลบ cookies อย่างละเอียด (ต้อง match กับ server-side)
      const cookiesToClear = [
        'auth_token',
        'refreshToken',
        'session_id',
        'session',
        'csrf_token',
        'remember_me',
      ];

      cookiesToClear.forEach(cookieName => {
        // ลบ cookie หลายแบบเพื่อให้แน่ใจ
        const expireDate = 'Thu, 01 Jan 1970 00:00:00 UTC';

        // Basic deletion
        document.cookie = `${cookieName}=; expires=${expireDate}; path=/;`;

        // With current domain
        document.cookie = `${cookieName}=; expires=${expireDate}; path=/; domain=${window.location.hostname};`;

        // With dot domain (for subdomains)
        if (window.location.hostname.includes('.')) {
          const rootDomain = window.location.hostname.split('.').slice(-2).join('.');
          document.cookie = `${cookieName}=; expires=${expireDate}; path=/; domain=.${rootDomain};`;
        }

        // With secure and sameSite (for production)
        document.cookie = `${cookieName}=; expires=${expireDate}; path=/; secure; samesite=strict;`;
      });
    }
  }

  // Manual refresh token (simplified for Token Rotation System)
  async refreshToken() {
    try {
      // ใน Token Rotation System, refresh จะทำโดย server hooks
      // Client-side ไม่สามารถอ่าน httpOnly cookies ได้
      console.log('AuthStore: Manual refresh not available in Token Rotation System');
      console.log('AuthStore: Tokens are managed by server-side hooks automatically');

      // ให้ reload page เพื่อให้ hooks ทำงาน
      if (
        confirm(
          'ใน Token Rotation System, การ refresh จะทำโดย server อัตโนมัติ\nคุณต้องการรีโหลดหน้าเว็บเพื่อให้ระบบตรวจสอบ tokens ใหม่หรือไม่?',
        )
      ) {
        window.location.reload();
        return true;
      }

      return false;
    }
    catch (error) {
      console.error('AuthStore: Manual token refresh error:', error);
      return false;
    }
  }

  async signin(credentials: SigninData): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      // ใช้ authService แทนการเรียก API โดยตรง
      const result = await authService.signin({
        ...credentials,
        rememberMe: credentials.rememberMe ?? false,
      });

      if (result.success && result.data) {
        this._user = result.data.user;
        this.saveUserToStorage(result.data.user);

        // ส่ง event ไปยังแท็บอื่น
        this.broadcastAuthEvent('login', {
          user: result.data.user,
        });

        // Redirect ไป dashboard
        setTimeout(() => {
          goto('/dashboard');
        }, 1500);

        return true;
      }
      else {
        throw new Error(result.error || 'Login failed');
      }
    }
    catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      this._error = errorMessage;
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  async signup(credentials: SignupData): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      // ใช้ authService แทนการเรียก API โดยตรง
      const result = await authService.signup({
        ...credentials,
        agreeToTerms: true, // หรือรับค่าจากฟอร์มถ้ามี
      });

      if (result.success && result.data) {
        this._user = result.data.user;
        this.saveUserToStorage(result.data.user);

        // ส่ง event ไปยังแท็บอื่น
        this.broadcastAuthEvent('login', {
          user: result.data.user,
        });

        // Redirect ไป dashboard
        setTimeout(() => {
          goto('/dashboard');
        }, 1500);

        return true;
      }
      else {
        throw new Error(result.error || 'Registration failed');
      }
    }
    catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      this._error = errorMessage;
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  async signout() {
    this._isLoading = true;

    try {
      // Use server action for secure signout
      const formData = new FormData();

      // Add CSRF token if available
      const csrfToken = this.getCSRFToken();
      if (csrfToken) {
        formData.append('csrf_token', csrfToken);
      }

      // Use authService for consistent error handling
      const result = await authService.serverSignout(formData);

      if (result.success) {
        console.log('AuthStore: Server signout successful');
      }
      else {
        // Check if it's a redirect error (status 302/301)
        if (
          result.error?.includes('302')
          || result.error?.includes('301')
          || result.error?.includes('redirect')
        ) {
          console.log('AuthStore: Server signout successful with redirect');
        }
        else {
          console.warn('AuthStore: Server signout failed, falling back to client-side cleanup', result.error);
          throw new Error(result.error);
        }
      }

      // Clear local state immediately
      this.clearAuthState();

      // Broadcast signout to other tabs
      this.broadcastAuthEvent('signout');

      // Redirect to signin
      goto('/signin');

      return;
    }
    catch (error) {
      console.error('AuthStore: Server signout error:', error);
    }

    // Fallback: Client-side cleanup if server signout fails
    try {
      // Try API signout as fallback
      const result = await authService.signout();
      console.log('AuthStore: API signout result', result);
    }
    catch (error) {
      console.error('AuthStore: API signout failed:', error);
    }
    finally {
      // Always clear local state
      this.clearAuthState();

      // Broadcast signout to other tabs
      this.broadcastAuthEvent('signout');

      // Force redirect to signin
      window.location.href = '/signin?signout=client';
    }
  }

  /**
   * Clear authentication state
   */
  private clearAuthState() {
    console.log('AuthStore: Clearing authentication state');

    this._user = null;
    this._isLoading = false;

    this.clearStorage();
  }

  /**
   * ✅ Get CSRF token from cookies
   */
  private getCSRFToken(): string | null {
    if (!browser) return null;

    const cookies = document.cookie.split(';').reduce(
      (acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      },
      {} as Record<string, string>,
    );

    return cookies['csrf_token'] || null;
  }

  /**
   * ✅ Get access token from cookies (HttpOnly cookies cannot be read from client-side)
   */
  private getTokenFromCookies(): string | null {
    if (!browser) return null;

    // ✅ HttpOnly cookies ไม่สามารถอ่านได้จาก JavaScript
    // ใน Token Rotation System, การจัดการ tokens จะทำโดย server
    console.log('AuthStore: Cannot read httpOnly cookies from client-side');
    return null;
  }

  /**
   * ✅ Get refresh token from cookies (HttpOnly cookies cannot be read from client-side)
   */
  private getRefreshTokenFromCookies(): string | null {
    if (!browser) return null;

    // ✅ HttpOnly cookies ไม่สามารถอ่านได้จาก JavaScript
    // ใน Token Rotation System, การจัดการ tokens จะทำโดย server
    console.log('AuthStore: Cannot read httpOnly cookies from client-side');
    return null;
  }

  async refreshUser() {
    try {
      // ใน Token Rotation System, ไม่สามารถอ่าน access token จาก client ได้
      console.log('AuthStore: No access token available for refreshUser in Token Rotation System');
      console.log('AuthStore: User data will be refreshed via server-side hooks');
      return;
    }
    catch (error) {
      console.error('AuthStore: Failed to refresh user:', error);
    }
  }

  clearError() {
    this._error = null;
  }

  // ฟังก์ชันสำหรับอัปเดตข้อมูล user
  updateUser(user: User) {
    console.log('AuthStore: Updating user data', {
      oldUser: this._user,
      newUser: user,
      oldMoneyPoint: this._user?.moneyPoint,
      newMoneyPoint: user.moneyPoint,
    });

    this._user = user;
    this.saveUserToStorage(user);

    // ส่ง event ไปยังแท็บอื่น
    this.broadcastAuthEvent('user-updated', { user });

    console.log('AuthStore: User updated successfully', {
      currentUser: this._user,
      moneyPoint: this._user?.moneyPoint,
    });
  }

  /**
   * Check token health and validity (simplified for Token Rotation System)
   */
  async checkTokenHealth(): Promise<{
    isValid: boolean;
    needsRefresh: boolean;
    timeUntilExpiry?: number;
    error?: string;
  }> {
    try {
      // ใน Token Rotation System, ไม่สามารถตรวจสอบ tokens จาก client ได้
      console.log('AuthStore: Token health check not available in Token Rotation System');
      console.log('AuthStore: Tokens are managed by server-side hooks');

      return {
        isValid: this.isAuthenticated,
        needsRefresh: false,
        error: this.isAuthenticated ? undefined : 'User not authenticated',
      };
    }
    catch (error) {
      console.error('AuthStore: Token health check failed:', error);
      return {
        isValid: false,
        needsRefresh: false,
        error: error instanceof Error ? error.message : 'Health check failed',
      };
    }
  }

  /**
   * Smart refresh - only refresh when needed (simplified for Token Rotation System)
   */
  async smartRefresh(): Promise<boolean> {
    const healthCheck = await this.checkTokenHealth();

    if (healthCheck.isValid) {
      console.log('AuthStore: User is authenticated, no refresh needed');
      return true;
    }

    console.log('AuthStore: User not authenticated, refresh handled by server-side hooks');
    return false;
  }

  // Cleanup method สำหรับ destroy store
  destroy() {
    // ไม่ปิด BroadcastChannel เพื่อให้ยังสามารถส่ง message ได้
    // BroadcastChannel จะถูกปิดอัตโนมัติเมื่อ tab ถูกปิด
    if (this._storageListener) {
      window.removeEventListener('storage', this._storageListener);
    }
  }
}

export const authStore = new AuthStore();

import { browser } from '$app/environment';
import { LogCategory, logger } from '../utils/logger';

export interface Site {
  _id: string;
  name: string;
  typeDomain: 'subdomain' | 'custom';
  fullDomain: string;
  isActive: boolean;
  expiredAt: string;
  createdAt: string;
  updatedAt: string;
  userId?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  themeSettings?: any;
  seoSettings?: any;
  navigationSettings?: any;
  pageSettings?: any;
  loadingSettings?: any;
  // เพิ่มข้อมูลสถานะใหม่
  isExpired?: boolean;
  daysUntilExpiry?: number;
  hasAccess?: boolean;
}

class SiteStore {
  private _site = $state<Site | null>(null);
  private _loading = $state(false);
  private _error = $state<string | null>(null);
  private _broadcastChannel: BroadcastChannel | null = null;
  private _storageListener: ((event: StorageEvent) => void) | null = null;
  private _currentTabId: string = '';

  constructor() {
    if (browser) {
      this._currentTabId = Math.random().toString(36).substring(2, 11);
      this.setupCrossTabSync();

      logger.info(LogCategory.SYSTEM, 'site_store_initialized', 'Site store initialized', {
        tabId: this._currentTabId,
        hasSite: !!this._site,
      });
    }
  }

  get site() {
    return this._site;
  }

  get loading() {
    return this._loading;
  }

  get error() {
    return this._error;
  }

  private setupCrossTabSync() {
    // ใช้ BroadcastChannel API สำหรับ modern browsers
    if ('BroadcastChannel' in window) {
      try {
        this._broadcastChannel = new BroadcastChannel('site-sync');
        this._broadcastChannel.onmessage = event => {
          this.handleSiteMessage(event.data);
        };
      }
      catch (error) {
        console.error('Failed to create BroadcastChannel for site:', error);
      }
    }

    // ใช้ localStorage events สำหรับ fallback
    this._storageListener = event => {
      if (event.key === 'site-event' && event.newValue) {
        try {
          const data = JSON.parse(event.newValue);
          this.handleSiteMessage(data);
        }
        catch (error) {
          console.error('Error parsing site event:', error);
        }
      }
    };

    window.addEventListener('storage', this._storageListener);
  }

  private handleSiteMessage(data: { type: string; payload?: any; tabId?: string; }) {
    // ตรวจสอบว่าเป็น event จากแท็บเดียวกันหรือไม่
    if (data.tabId === this._currentTabId) {
      return;
    }

    switch (data.type) {
      case 'site-change':
        if (data.payload) {
          this._site = data.payload.site;
          this._error = null;
        }
        break;
      case 'site-error':
        if (data.payload) {
          this._error = data.payload.error;
          this._loading = false;
        }
        break;
      case 'site-loading':
        if (data.payload) {
          this._loading = data.payload.loading;
        }
        break;
    }
  }

  private broadcastSiteEvent(type: string, payload?: any) {
    const message = {
      type,
      payload,
      timestamp: Date.now(),
      tabId: this._currentTabId,
    };

    // ส่งผ่าน BroadcastChannel
    if (this._broadcastChannel) {
      try {
        this._broadcastChannel.postMessage(message);
      }
      catch (error) {
        console.error('Failed to send site message via BroadcastChannel:', error);
      }
    }

    // ส่งผ่าน localStorage (fallback)
    if (browser) {
      try {
        localStorage.setItem('site-event', JSON.stringify(message));
        setTimeout(() => {
          localStorage.removeItem('site-event');
        }, 100);
      }
      catch (error) {
        console.error('Failed to send site message via localStorage:', error);
      }
    }
  }

  setSite(site: Site | null, broadcast: boolean = true) {
    const previousSite = this._site;
    this._site = site;
    this._error = null;

    logger.info(LogCategory.SYSTEM, 'site_changed', 'Site changed', {
      previousSiteId: previousSite?._id,
      newSiteId: site?._id,
      siteName: site?.name,
      tabId: this._currentTabId,
    });

    if (broadcast) {
      this.broadcastSiteEvent('site-change', { site });
    }
  }

  setLoading(loading: boolean, broadcast: boolean = true) {
    this._loading = loading;

    if (broadcast) {
      this.broadcastSiteEvent('site-loading', { loading });
    }
  }

  setError(error: string | null, broadcast: boolean = true) {
    this._error = error;
    this._loading = false;

    logger.error(LogCategory.SYSTEM, 'site_error', 'Site error occurred', {
      error,
      tabId: this._currentTabId,
    });

    if (broadcast) {
      this.broadcastSiteEvent('site-error', { error });
    }
  }

  reset(broadcast: boolean = true) {
    this._site = null;
    this._loading = false;
    this._error = null;

    logger.info(LogCategory.SYSTEM, 'site_reset', 'Site store reset', {
      tabId: this._currentTabId,
    });

    if (broadcast) {
      this.broadcastSiteEvent('site-change', { site: null });
    }
  }

  // Helper methods
  get isExpired() {
    if (!this._site?.expiredAt) return false;
    return new Date(this._site.expiredAt) < new Date();
  }

  get daysUntilExpiry() {
    if (!this._site?.expiredAt) return null;
    const expiredDate = new Date(this._site.expiredAt);
    const now = new Date();
    const diffTime = expiredDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  get hasAccess() {
    return this._site?.isActive && !this.isExpired;
  }

  destroy() {
    if (this._storageListener) {
      window.removeEventListener('storage', this._storageListener);
    }
  }
}

export const siteStore = new SiteStore();

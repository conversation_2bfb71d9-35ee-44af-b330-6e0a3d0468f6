import { browser } from '$app/environment';
import { apiClient, createAuthHeaders } from '$lib/api/client';
import type { User } from './auth.svelte.ts';

export interface UserFilters {
  search?: string;
  role?: string;
  status?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'lastLogin' | 'email' | 'username';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface UsersResponse {
  users: User[];
  total: number;
  page: number;
  totalPages: number;
}

class UsersStore {
  private _users = $state<User[]>([]);
  private _total = $state(0);
  private _page = $state(1);
  private _totalPages = $state(0);
  private _isLoading = $state(false);
  private _error = $state<string | null>(null);
  private _filters = $state<UserFilters>({
    search: '',
    role: '',
    status: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    page: 1,
    limit: 10,
  });

  constructor() {
    if (browser) {
      this.loadUsers();
    }
  }

  get users() {
    return this._users;
  }

  get total() {
    return this._total;
  }

  get page() {
    return this._page;
  }

  get totalPages() {
    return this._totalPages;
  }

  get isLoading() {
    return this._isLoading;
  }

  get error() {
    return this._error;
  }

  get filters() {
    return this._filters;
  }

  private getAuthHeaders() {
    const token = browser ? localStorage.getItem('token') : null;
    return token ? createAuthHeaders(token) : {};
  }

  async loadUsers(filters?: Partial<UserFilters>) {
    this._isLoading = true;
    this._error = null;

    if (filters) {
      this._filters = { ...this._filters, ...filters };
    }

    try {
      const params = new URLSearchParams();
      Object.entries(this._filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, value.toString());
        }
      });

      const data: UsersResponse = await apiClient.request(`/api/users?${params}`, {
        headers: this.getAuthHeaders(),
      });

      this._users = data.users;
      this._total = data.total;
      this._page = data.page;
      this._totalPages = data.totalPages;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to load users';
    }
    finally {
      this._isLoading = false;
    }
  }

  async createUser(userData: Omit<User, '_id' | 'createdAt' | 'updatedAt'>): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      const newUser: User = await apiClient.request('/api/users', {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: userData,
      });

      this._users = [newUser, ...this._users];
      this._total += 1;

      return true;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to create user';
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  async updateUser(id: string, userData: Partial<User>): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      const updatedUser: User = await apiClient.request(`/api/users/${id}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: userData,
      });

      const index = this._users.findIndex(u => u._id === id);

      if (index !== -1) {
        this._users[index] = updatedUser;
      }

      return true;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to update user';
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  async deleteUser(id: string): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      await apiClient.request(`/api/users/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      this._users = this._users.filter(u => u._id !== id);
      this._total -= 1;

      return true;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to delete user';
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  setFilters(filters: Partial<UserFilters>) {
    this._filters = { ...this._filters, ...filters };
    this.loadUsers();
  }

  setPage(page: number) {
    this._filters.page = page;
    this.loadUsers();
  }

  clearError() {
    this._error = null;
  }

  reset() {
    this._users = [];
    this._total = 0;
    this._page = 1;
    this._totalPages = 0;
    this._filters = {
      search: '',
      role: '',
      status: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
      page: 1,
      limit: 10,
    };
  }
}

export const usersStore = new UsersStore();

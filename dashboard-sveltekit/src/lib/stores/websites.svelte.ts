import { browser } from '$app/environment';
import { apiClient, createAuthHeaders } from '$lib/api/client';

export interface Website {
  id: string;
  name: string;
  url: string;
  description: string;
  template: string;
  domain: string;
  ssl: boolean;
  status: 'active' | 'inactive' | 'maintenance';
  userId: string;
  createdAt: string;
  updatedAt: string;
  lastBackup?: string;
  analytics?: {
    visitors: number;
    pageViews: number;
    bounceRate: number;
  };
}

export interface WebsiteFilters {
  search?: string;
  status?: string;
  template?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'visitors';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface WebsitesResponse {
  websites: Website[];
  total: number;
  page: number;
  totalPages: number;
}

class WebsitesStore {
  private _websites = $state<Website[]>([]);
  private _total = $state(0);
  private _page = $state(1);
  private _totalPages = $state(0);
  private _isLoading = $state(false);
  private _error = $state<string | null>(null);
  private _filters = $state<WebsiteFilters>({
    search: '',
    status: '',
    template: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    page: 1,
    limit: 12,
  });

  constructor() {
    if (browser) {
      this.loadWebsites();
    }
  }

  get websites() {
    return this._websites;
  }

  get total() {
    return this._total;
  }

  get page() {
    return this._page;
  }

  get totalPages() {
    return this._totalPages;
  }

  get isLoading() {
    return this._isLoading;
  }

  get error() {
    return this._error;
  }

  get filters() {
    return this._filters;
  }

  private getAuthHeaders() {
    const token = browser ? localStorage.getItem('token') : null;
    return token ? createAuthHeaders(token) : {};
  }

  async loadWebsites(filters?: Partial<WebsiteFilters>) {
    this._isLoading = true;
    this._error = null;

    if (filters) {
      this._filters = { ...this._filters, ...filters };
    }

    try {
      const params = new URLSearchParams();
      Object.entries(this._filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, value.toString());
        }
      });

      const data: WebsitesResponse = await apiClient.request(`/api/websites?${params}`, {
        headers: this.getAuthHeaders(),
      });

      this._websites = data.websites;
      this._total = data.total;
      this._page = data.page;
      this._totalPages = data.totalPages;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to load websites';
    }
    finally {
      this._isLoading = false;
    }
  }

  async createWebsite(
    websiteData: Omit<Website, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      const newWebsite: Website = await apiClient.request('/api/websites', {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: websiteData,
      });

      this._websites = [newWebsite, ...this._websites];
      this._total += 1;

      return true;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to create website';
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  async updateWebsite(id: string, websiteData: Partial<Website>): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      const updatedWebsite: Website = await apiClient.request(`/api/websites/${id}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: websiteData,
      });

      const index = this._websites.findIndex(w => w.id === id);

      if (index !== -1) {
        this._websites[index] = updatedWebsite;
      }

      return true;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to update website';
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  async deleteWebsite(id: string): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      await apiClient.request(`/api/websites/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      this._websites = this._websites.filter(w => w.id !== id);
      this._total -= 1;

      return true;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to delete website';
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  async backupWebsite(id: string): Promise<boolean> {
    this._isLoading = true;
    this._error = null;

    try {
      await apiClient.request(`/api/websites/${id}/backup`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      // อัปเดต lastBackup
      const index = this._websites.findIndex(w => w.id === id);
      if (index !== -1) {
        this._websites[index].lastBackup = new Date().toISOString();
      }

      return true;
    }
    catch (error) {
      this._error = error instanceof Error ? error.message : 'Failed to backup website';
      return false;
    }
    finally {
      this._isLoading = false;
    }
  }

  setFilters(filters: Partial<WebsiteFilters>) {
    this._filters = { ...this._filters, ...filters };
    this.loadWebsites();
  }

  setPage(page: number) {
    this._filters.page = page;
    this.loadWebsites();
  }

  clearError() {
    this._error = null;
  }

  reset() {
    this._websites = [];
    this._total = 0;
    this._page = 1;
    this._totalPages = 0;
    this._filters = {
      search: '',
      status: '',
      template: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
      page: 1,
      limit: 12,
    };
  }
}

export const websitesStore = new WebsitesStore();

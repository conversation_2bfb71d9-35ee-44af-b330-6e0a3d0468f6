export interface DiscountValidationRequest {
  discountCode: string;
  siteId?: string;
  target?: 'all' | 'package' | 'category' | 'product' | 'user_group' | 'first_time';
  orderAmount?: number;
  items?: any[];
}

export interface DiscountInfo {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed';
  name?: string;
  description?: string;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  isFirstTimeOnly?: boolean;
  validUntil?: string;
}

// Type definitions
export interface DiscountConditions {
  minOrderAmount?: number;
  maxOrderAmount?: number;
  applicableCategories?: string[];
  applicableProducts?: string[];
  excludedProducts?: string[];
  userGroups?: string[];
  firstTimeOnly?: boolean;
  usageLimit?: number;
  usageLimitPerUser?: number;
  dayOfWeek?: string[];
  customerTier?: string[];
  bundleProducts?: string[];
  tierRules?: any[];
}

export interface DiscountData {
  _id: string;
  name: string;
  description: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  maxDiscountAmount?: number;
  target: string;
  conditions: DiscountConditions;
  status: 'active' | 'inactive' | 'scheduled' | 'expired';
  startDate: string;
  endDate: string;
  totalUsage: number;
  totalDiscountAmount: number;
  usageHistory: any[];
  createdAt: string;
  updatedAt: string;
}

export type { ApiResponse } from './common';

/**
 * Preorder Types
 */
export interface Preorder {
  _id: string;
  productId: string;
  productName: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  quantity: number;
  price: number;
  totalAmount: number;
  depositAmount?: number;
  remainingAmount?: number;
  depositPaid: boolean;
  status: 'pending' | 'confirmed' | 'processing' | 'ready' | 'delivered' | 'cancelled';
  estimatedDeliveryDate?: string;
  actualDeliveryDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PreorderListResponse {
  preOrders: Preorder[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PreorderResponse {
  success: boolean;
  data?: Preorder | null;
  error?: string;
}

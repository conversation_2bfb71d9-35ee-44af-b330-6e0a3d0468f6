/**
 * Review Types
 */
export interface Review {
  _id: string;
  productId: string;
  productName: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  rating: number;
  title?: string;
  comment: string;
  isApproved: boolean;
  isVisible: boolean;
  images?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface ReviewListResponse {
  reviews: Review[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ReviewResponse {
  success: boolean;
  data?: Review | null;
  error?: string;
}

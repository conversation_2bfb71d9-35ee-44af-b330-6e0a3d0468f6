// ✅ Site Types - API Response Types และ Data Structures

// ✅ Site Entity (Database Model)
export interface Site {
  _id: string;
  name: string; // ✅ ปรับให้ตรงกับ backend
  siteName?: string; // backward compatibility
  typeDomain: 'subdomain' | 'custom';
  subDomain?: string;
  mainDomain?: string;
  customDomain?: string;
  fullDomain?: string; // ✅ เพิ่ม fullDomain จาก backend
  plan: string; // ✅ ปรับให้ตรงกับ backend
  packageType?: string; // backward compatibility
  status: 'active' | 'inactive' | 'pending';
  isActive?: boolean; // ✅ เพิ่มจาก backend
  userId: string;
  expiredAt?: string; // ✅ เพิ่มจาก backend
  createdAt: string;
  updatedAt: string;
}

// ✅ API Response Types
export interface SiteApiResponse {
  success: boolean;
  data?: Site;
  message?: string;
  error?: string;
}

export interface SitesApiResponse {
  success: boolean;
  data?: Site[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
  error?: string;
}

export interface CreateSiteApiResponse {
  success: boolean;
  data?: {
    site: Site;
    domain: string;
  };
  message?: string;
  error?: string;
}

export interface CheckDomainApiResponse {
  success: boolean;
  data?: {
    available: boolean;
    domain: string;
    message?: string;
  };
  message?: string;
  error?: string;
}

// ✅ Package Types
export interface PackageInfo {
  type: string;
  name: string;
  description: string;
  days: number;
  moneyPoint: number;
  features: string[];
}

export interface PackageApiResponse {
  success: boolean;
  data?: PackageInfo[];
  message?: string;
  error?: string;
}

// ✅ Site Statistics Types
export interface SiteStats {
  totalProducts: number;
  totalOrders: number;
  totalCustomers: number;
  totalRevenue: number;
  monthlyGrowth: number;
}

export interface SiteStatsApiResponse {
  success: boolean;
  data?: SiteStats;
  message?: string;
  error?: string;
}

// ✅ Site Settings Types
export interface SiteSettings {
  logo?: string;
  favicon?: string;
  customCss?: string;
  customJs?: string;
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
}

export interface SiteSettingsApiResponse {
  success: boolean;
  data?: SiteSettings;
  message?: string;
  error?: string;
}

// ✅ Import types from common
export type { ApiResponse } from './common';

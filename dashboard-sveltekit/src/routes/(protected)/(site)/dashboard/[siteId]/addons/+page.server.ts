import { addonsService } from '$lib/services/addons';
import { LogCategory, logger } from '$lib/utils/logger';
import { error, fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, fetch }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // ✅ ใช้ Addons Service แทน direct fetch
    const addonsResult = await addonsService.getSiteAddons(siteId, locals.token!);
    const addons = addonsResult.success ? addonsResult.data : [];

    // ดึงข้อมูล subscription ปัจจุบัน (ถ้ามี API)
    let subscription = null;
    try {
      const subscriptionResponse = await fetch(`/api/sites/${siteId}/subscription`);
      subscription = subscriptionResponse.ok ? await subscriptionResponse.json() : null;
    }
    catch (err) {
      console.warn('Subscription API not available:', err);
    }

    // ✅ เพิ่มข้อมูล addons ที่มีให้เลือก พร้อมสถานะ
    const availableAddons = [
      {
        id: 'news',
        name: 'ระบบข่าวสาร',
        description: 'จัดการข่าวสารและประชาสัมพันธ์',
        icon: 'mdi:newspaper',
        price: 299,
        features: ['สร้างข่าวสาร', 'จัดหมวดหมู่', 'SEO ข่าวสาร', 'แสดงผลหน้าเว็บ'],
      },
      {
        id: 'blog',
        name: 'ระบบบล็อก',
        description: 'เขียนบทความและแชร์เนื้อหา',
        icon: 'mdi:post',
        price: 399,
        features: ['เขียนบทความ', 'ระบบแท็ก', 'ความคิดเห็น', 'แชร์โซเชียล'],
      },
      {
        id: 'novel',
        name: 'ระบบนิยาย',
        description: 'เผยแพร่นิยายและเรื่องสั้น',
        icon: 'mdi:book-open-page-variant',
        price: 599,
        features: ['เขียนนิยาย', 'แบ่งตอน', 'ระบบจองอ่าน', 'ระบบเรตติ้ง'],
      },
    ];

    // ✅ รวมข้อมูลสถานะกับ available addons
    const addonsWithStatus = availableAddons.map(available => {
      const existing = addons.find(addon => addon.id === available.id);
      return {
        ...available,
        isActive: existing?.isActive || false,
        isPurchased: existing?.isPurchased || false,
        isLifetime: existing?.isLifetime || false,
      };
    });

    return {
      addons: addonsWithStatus,
      subscription,
      siteId,
    };
  }
  catch (err) {
    console.error('Error loading addons:', err);

    // ✅ Fallback: ให้ข้อมูล default addons
    const defaultAddons = [
      {
        id: 'news',
        name: 'ระบบข่าวสาร',
        description: 'จัดการข่าวสารและประชาสัมพันธ์',
        icon: 'mdi:newspaper',
        price: 299,
        features: ['สร้างข่าวสาร', 'จัดหมวดหมู่', 'SEO ข่าวสาร', 'แสดงผลหน้าเว็บ'],
        isActive: false,
        isPurchased: false,
        isLifetime: false,
      },
      {
        id: 'blog',
        name: 'ระบบบล็อก',
        description: 'เขียนบทความและแชร์เนื้อหา',
        icon: 'mdi:post',
        price: 399,
        features: ['เขียนบทความ', 'ระบบแท็ก', 'ความคิดเห็น', 'แชร์โซเชียล'],
        isActive: false,
        isPurchased: false,
        isLifetime: false,
      },
      {
        id: 'novel',
        name: 'ระบบนิยาย',
        description: 'เผยแพร่นิยายและเรื่องสั้น',
        icon: 'mdi:book-open-page-variant',
        price: 599,
        features: ['เขียนนิยาย', 'แบ่งตอน', 'ระบบจองอ่าน', 'ระบบเรตติ้ง'],
        isActive: false,
        isPurchased: false,
        isLifetime: false,
      },
    ];

    return {
      addons: defaultAddons,
      subscription: null,
      siteId,
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Rent Addon - Hybrid Approach
   * Route API + Service Pattern
   */
  rentAddon: async ({ request, params, locals, getClientAddress, fetch }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'rent',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const rentData = {
        addonId: formData.get('addonId')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!rentData.addonId) {
        return fail(400, {
          message: 'กรุณาเลือกระบบเสริมที่ต้องการเช่า',
          type: 'rent',
          fields: {
            addonId: 'กรุณาเลือกระบบเสริม',
          },
        });
      }

      logger.info(LogCategory.ADDON, 'rent_addon_attempt', 'Addon rent attempt', {
        siteId,
        addonId: rentData.addonId,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Addons Service แทน direct fetch
      const result = await addonsService.purchaseAddon(siteId, rentData.addonId, false, locals.token);

      if (!result.success) {
        logger.warn(LogCategory.ADDON, 'rent_addon_failed', 'Addon rent failed', {
          siteId,
          addonId: rentData.addonId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถเช่าระบบเสริมได้',
          type: 'rent',
        });
      }

      logger.info(LogCategory.ADDON, 'rent_addon_success', 'Addon rent successful', {
        siteId,
        addonId: rentData.addonId,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: 'เช่าระบบเสริมเรียบร้อยแล้ว',
        type: 'rent',
        data: result,
      };
    }
    catch (error) {
      logger.error(LogCategory.ADDON, 'rent_addon_error', 'Addon rent error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเช่าระบบเสริม',
        type: 'rent',
      });
    }
  },

  /**
   * ✅ Cancel Addon - Hybrid Approach
   * Route API + Service Pattern
   */
  cancelAddon: async ({ request, params, locals, getClientAddress, fetch }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'cancel',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const cancelData = {
        addonId: formData.get('addonId')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!cancelData.addonId) {
        return fail(400, {
          message: 'กรุณาเลือกระบบเสริมที่ต้องการยกเลิก',
          type: 'cancel',
          fields: {
            addonId: 'กรุณาเลือกระบบเสริม',
          },
        });
      }

      logger.info(LogCategory.ADDON, 'cancel_addon_attempt', 'Addon cancel attempt', {
        siteId,
        addonId: cancelData.addonId,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Addons Service แทน direct fetch
      const result = await addonsService.deactivateAddon(siteId, cancelData.addonId, locals.token);

      if (!result.success) {
        logger.warn(LogCategory.ADDON, 'cancel_addon_failed', 'Addon cancel failed', {
          siteId,
          addonId: cancelData.addonId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถยกเลิกระบบเสริมได้',
          type: 'cancel',
        });
      }

      logger.info(LogCategory.ADDON, 'cancel_addon_success', 'Addon cancel successful', {
        siteId,
        addonId: cancelData.addonId,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: 'ยกเลิกระบบเสริมเรียบร้อยแล้ว',
        type: 'cancel',
        data: result,
      };
    }
    catch (error) {
      logger.error(LogCategory.ADDON, 'cancel_addon_error', 'Addon cancel error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการยกเลิกระบบเสริม',
        type: 'cancel',
      });
    }
  },

  /**
   * ✅ Activate Addon - Hybrid Approach
   * Route API + Service Pattern
   */
  activateAddon: async ({ request, params, locals, getClientAddress }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'activate',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const activateData = {
        addonId: formData.get('addonId')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!activateData.addonId) {
        return fail(400, {
          message: 'กรุณาเลือกระบบเสริมที่ต้องการเปิดใช้งาน',
          type: 'activate',
          fields: {
            addonId: 'กรุณาเลือกระบบเสริม',
          },
        });
      }

      logger.info(LogCategory.ADDON, 'activate_addon_attempt', 'Addon activate attempt', {
        siteId,
        addonId: activateData.addonId,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Addons Service แทน direct fetch
      const result = await addonsService.activateAddon(siteId, activateData.addonId, locals.token);

      if (!result.success) {
        logger.warn(LogCategory.ADDON, 'activate_addon_failed', 'Addon activate failed', {
          siteId,
          addonId: activateData.addonId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถเปิดใช้งานระบบเสริมได้',
          type: 'activate',
        });
      }

      logger.info(LogCategory.ADDON, 'activate_addon_success', 'Addon activate successful', {
        siteId,
        addonId: activateData.addonId,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: 'เปิดใช้งานระบบเสริมเรียบร้อยแล้ว',
        type: 'activate',
        data: result,
      };
    }
    catch (error) {
      logger.error(LogCategory.ADDON, 'activate_addon_error', 'Addon activate error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปิดใช้งานระบบเสริม',
        type: 'activate',
      });
    }
  },
};

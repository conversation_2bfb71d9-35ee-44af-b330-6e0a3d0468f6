import {
  sanitizeBrandData,
  validateBrandFilterData,
  validateCreateBrandData,
  validateDeleteBrandData,
  validateUpdateBrandData,
} from '$lib/schemas/brand.schema';
import { brandService } from '$lib/services/brand';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, url }) => {
  const { siteId } = params;

  try {
    if (!locals.token || !locals.user) {
      return {
        siteId,
        brands: [],
        stats: null,
        error: 'กรุณาเข้าสู่ระบบ',
      };
    }

    // Parse filter parameters
    const rawFilters = {
      search: url.searchParams.get('search'),
      isActive: url.searchParams.get('isActive') === 'true' ? true
        : url.searchParams.get('isActive') === 'false' ? false : undefined,
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '20'),
    };

    // Validate filters using schema
    const filterValidation = validateBrandFilterData(rawFilters);
    const filters = filterValidation.success ? filterValidation.data! : { page: 1, limit: 20 };

    // Call service for business logic
    const brandsResult = await brandService.getBrands(siteId, filters, locals.token!);

    if (!brandsResult.success) {
      console.log('Brands Page Server: Failed to fetch brands:', brandsResult.error);
      return {
        siteId,
        brands: [],
        stats: null,
        error: brandsResult.error,
      };
    }

    return {
      siteId,
      brands: brandsResult.data?.brands || [],
      stats: brandsResult.data?.stats || null,
      error: null,
    };
  }
  catch (error) {
    console.error('Brands Page Server: Error in load function:', error);
    return {
      siteId,
      brands: [],
      stats: null,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  createBrand: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'createBrand',
        });
      }

      const formData = await request.formData();

      const rawData = {
        siteId,
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        logoUrl: formData.get('logoUrl') as string,
        websiteUrl: formData.get('websiteUrl') as string,
        isActive: formData.get('isActive') === 'on',
      };

      // Sanitize data
      const sanitizedData = sanitizeBrandData(rawData);

      // Validate using schema
      const validationResult = validateCreateBrandData(sanitizedData);
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'createBrand',
        });
      }

      // Call service for business logic
      const result = await brandService.createBrand(
        validationResult.data!,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'createBrand',
        });
      }

      return {
        success: true,
        brand: result.data,
        message: 'สร้างแบรนด์สำเร็จ',
        type: 'createBrand',
      };
    }
    catch (error) {
      console.error('Create brand error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างแบรนด์',
        type: 'createBrand',
      });
    }
  },

  updateBrand: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'updateBrand',
        });
      }

      const formData = await request.formData();
      const brandId = formData.get('brandId') as string;

      if (!brandId) {
        return fail(400, {
          message: 'กรุณาระบุ Brand ID',
          type: 'updateBrand',
        });
      }

      const rawData = {
        _id: brandId,
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        logoUrl: formData.get('logoUrl') as string,
        websiteUrl: formData.get('websiteUrl') as string,
        isActive: formData.get('isActive') === 'on',
      };

      // Sanitize data
      const sanitizedData = sanitizeBrandData(rawData);

      // Validate using schema
      const validationResult = validateUpdateBrandData(sanitizedData);
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'updateBrand',
        });
      }

      // Call service for business logic
      const result = await brandService.updateBrand(
        brandId,
        validationResult.data!,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'updateBrand',
        });
      }

      return {
        success: true,
        brand: result.data,
        message: 'อัปเดตแบรนด์สำเร็จ',
        type: 'updateBrand',
      };
    }
    catch (error) {
      console.error('Update brand error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตแบรนด์',
        type: 'updateBrand',
      });
    }
  },

  deleteBrand: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'deleteBrand',
        });
      }

      const formData = await request.formData();
      const brandId = formData.get('brandId') as string;

      // Validate using schema
      const validationResult = validateDeleteBrandData({ brandId });
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'deleteBrand',
        });
      }

      // Call service for business logic
      const result = await brandService.deleteBrand(brandId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteBrand',
        });
      }

      return {
        success: true,
        message: 'ลบแบรนด์สำเร็จ',
        type: 'deleteBrand',
      };
    }
    catch (error) {
      console.error('Delete brand error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบแบรนด์',
        type: 'deleteBrand',
      });
    }
  },

  toggleBrandStatus: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'toggleBrandStatus',
        });
      }

      const formData = await request.formData();
      const brandId = formData.get('brandId') as string;
      const isActive = formData.get('isActive') === 'true';

      if (!brandId) {
        return fail(400, {
          message: 'กรุณาระบุ Brand ID',
          type: 'toggleBrandStatus',
        });
      }

      // Call service for business logic
      const result = await brandService.toggleBrandStatus(brandId, siteId, isActive, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'toggleBrandStatus',
        });
      }

      return {
        success: true,
        brand: result.data,
        message: `${isActive ? 'เปิด' : 'ปิด'}การใช้งานแบรนด์สำเร็จ`,
        type: 'toggleBrandStatus',
      };
    }
    catch (error) {
      console.error('Toggle brand status error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะแบรนด์',
        type: 'toggleBrandStatus',
      });
    }
  },
};

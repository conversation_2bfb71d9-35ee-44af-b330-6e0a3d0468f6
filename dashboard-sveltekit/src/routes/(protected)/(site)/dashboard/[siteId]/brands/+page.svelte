<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';

	import { Badge, Button, Card, Input, Table } from '$lib/components/ui';
	import { 
		getBrandStatusColor,
		getBrandStatusText,
		formatBrandDate,
		canDeleteBrand,
		getDeleteBrandMessage,
		getBrandInitials
	} from '$lib/schemas/brand.schema';
	import type { Brand, BrandStatsData } from '$lib/schemas/brand.schema';

	// รับข้อมูลจาก server
	const { data, form } = $props<{
		data: {
			siteId: string;
			brands: Brand[];
			stats: BrandStatsData | null;
			error?: string;
		};
		form?: any;
	}>();

	const brands = $derived(data.brands || []);
	const stats = $derived(data.stats);
	const formResult = $derived(form);

	// State management
	let isLoading = $state(false);
	let searchQuery = $state('');
	let createDialogOpen = $state(false);
	let editDialogOpen = $state(false);
	let selectedBrand: Brand | null = $state(null);

	// ฟอร์มสร้าง/แก้ไขแบรนด์
	let brandForm = $state({
		name: '',
		description: '',
		logoUrl: '',
		websiteUrl: '',
		isActive: true,
	});

	const filteredBrands = $derived(
		brands.filter(
			brand =>
				brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				(brand.description && brand.description.toLowerCase().includes(searchQuery.toLowerCase()))
		)
	);

	// Handle form results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			if (result.data?.type === 'createBrand') {
				showSuccessMessage(result.data?.message || 'สร้างแบรนด์สำเร็จ');
				resetForm();
				createDialogOpen = false;
			} else if (result.data?.type === 'updateBrand') {
				showSuccessMessage(result.data?.message || 'อัปเดตแบรนด์สำเร็จ');
				resetForm();
				editDialogOpen = false;
			} else if (result.data?.type === 'deleteBrand') {
				showSuccessMessage(result.data?.message || 'ลบแบรนด์สำเร็จ');
			} else if (result.data?.type === 'toggleBrandStatus') {
				showSuccessMessage(result.data?.message || 'เปลี่ยนสถานะแบรนด์สำเร็จ');
			}
			// Refresh data
			invalidateAll();
		} else if (result.type === 'failure') {
			// Handle error
			showErrorMessage(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// Helper functions
	function showSuccessMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-success">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 3 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 3000);
	}

	function showErrorMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-error">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 5 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 5000);
	}

	// เปิดฟอร์มแก้ไข
	function openEditDialog(brand: Brand) {
		selectedBrand = brand;
		brandForm = {
			name: brand.name,
			description: brand.description || '',
			logoUrl: brand.logoUrl || '',
			websiteUrl: brand.websiteUrl || '',
			isActive: brand.isActive,
		};
		editDialogOpen = true;
	}

	// รีเซ็ตฟอร์ม
	function resetForm() {
		brandForm = {
			name: '',
			description: '',
			logoUrl: '',
			websiteUrl: '',
			isActive: true,
		};
		selectedBrand = null;
	}


</script>

<svelte:head>
	<title>จัดการแบรนด์ - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-4 sm:space-y-4">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการแบรนด์</h1>
			<p class="text-muted-foreground">จัดการแบรนด์สินค้าในเว็บไซต์ของคุณ</p>
		</div>

		<Button onclick={() => (createDialogOpen = true)}>
			<Icon icon="heroicons:plus" class="h-4 w-4 mr-2" />
			เพิ่มแบรนด์ใหม่
		</Button>
	</div>

	<!-- Error Alert -->
	{#if data.error}
		<div class="alert alert-error">
			<Icon icon="heroicons:exclamation-triangle" class="w-5 h-5" />
			<span>{data.error}</span>
		</div>
	{/if}

	<!-- ค้นหา -->
	<Card>
		<div class="relative">
			<Icon
				icon="heroicons:magnifying-glass"
				class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
			/>
			<Input placeholder="ค้นหาแบรนด์..." bind:value={searchQuery} class="pl-10" />
		</div>
	</Card>

	{#if isLoading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<Card>
			<div class="flex items-center gap-2">
				<Icon icon="heroicons:building-storefront" class="h-5 w-5" />
				แบรนด์ทั้งหมด ({filteredBrands.length})
			</div>
			<div class="p-4">
				{#if filteredBrands.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery ? 'ไม่พบแบรนด์ที่ค้นหา' : 'ยังไม่มีแบรนด์'}
					</div>
				{:else}
					<Table>
						<table class="table table-zebra w-full">
							<thead>
								<tr>
									<th>แบรนด์</th>
									<th>คำอธิบาย</th>
									<th>สินค้า</th>
									<th>สถานะ</th>
									<th>วันที่สร้าง</th>
									<th class="text-right">การจัดการ</th>
								</tr>
							</thead>
							<tbody>
								{#each filteredBrands as brand (brand._id)}
									<tr>
										<td>
											<div class="flex items-center gap-3">
												{#if brand.logoUrl}
													<img
														src={brand.logoUrl}
														alt={brand.name}
														class="h-8 w-8 rounded object-cover"
														onerror={(e) => {
															const target = e.target as HTMLImageElement;
															target.style.display = 'none';
														}}
													/>
												{:else}
													<div class="h-8 w-8 rounded bg-muted flex items-center justify-center text-sm font-medium">
														{getBrandInitials(brand.name)}
													</div>
												{/if}
												<div>
													<p class="font-medium">{brand.name}</p>
													{#if brand.websiteUrl}
														<a
															href={brand.websiteUrl}
															target="_blank"
															class="text-xs text-blue-600 hover:underline"
														>
															{brand.websiteUrl}
														</a>
													{/if}
												</div>
											</div>
										</td>
										<td>
											<p class="text-sm text-muted-foreground max-w-xs truncate">
												{brand.description || '-'}
											</p>
										</td>
										<td>
											<Badge variant="outline">
												{brand.productCount} สินค้า
											</Badge>
										</td>
										<td>
											<Badge class={getBrandStatusColor(brand.isActive)}>
												{getBrandStatusText(brand.isActive)}
											</Badge>
										</td>
										<td class="text-sm text-muted-foreground">
											{formatBrandDate(brand.createdAt!)}
										</td>
										<td class="text-right">
											<div class="flex items-center justify-end gap-2">
												<form
													method="POST"
													action="?/toggleBrandStatus"
													use:enhance={() => {
														isLoading = true;
														return async ({ result }) => {
															handleFormResult(result);
														};
													}}
												>
													<input type="hidden" name="brandId" value={brand._id} />
													<input type="hidden" name="isActive" value={(!brand.isActive).toString()} />
													<Button type="submit" variant="ghost" size="sm" disabled={isLoading}>
														{#if brand.isActive}
															<Icon icon="heroicons:eye-slash" class="h-4 w-4" />
														{:else}
															<Icon icon="heroicons:eye" class="h-4 w-4" />
														{/if}
													</Button>
												</form>
												<Button variant="ghost" size="sm" onclick={() => openEditDialog(brand)}>
													<Icon icon="heroicons:pencil" class="h-4 w-4" />
												</Button>
												<form
													method="POST"
													action="?/deleteBrand"
													use:enhance={() => {
														if (!canDeleteBrand(brand)) {
															alert(getDeleteBrandMessage(brand));
															return;
														}
														if (!confirm(getDeleteBrandMessage(brand))) {
															return;
														}
														isLoading = true;
														return async ({ result }) => {
															handleFormResult(result);
														};
													}}
												>
													<input type="hidden" name="brandId" value={brand._id} />
													<Button
														type="submit"
														variant="ghost"
														size="sm"
														disabled={!canDeleteBrand(brand) || isLoading}
													>
														<Icon icon="heroicons:trash" class="h-4 w-4" />
													</Button>
												</form>
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</Table>
				{/if}
			</div>
		</Card>
	{/if}
</div>

<!-- Create Brand Modal -->
{#if createDialogOpen}
	<div class="modal modal-open">
		<div class="modal-box max-w-2xl">
			<h3 class="font-bold text-lg mb-4">เพิ่มแบรนด์ใหม่</h3>
			
			<form
				method="POST"
				action="?/createBrand"
				use:enhance={() => {
					isLoading = true;
					return async ({ result }) => {
						handleFormResult(result);
					};
				}}
			>
				<div class="space-y-4">
					<div class="space-y-2">
						<label class="label" for="create-brand-name">
							<span class="label-text">ชื่อแบรนด์ *</span>
						</label>
						<input
							id="create-brand-name"
							type="text"
							name="name"
							placeholder="ชื่อแบรนด์"
							bind:value={brandForm.name}
							class="input input-bordered w-full"
							required
						/>
					</div>

					<div class="space-y-2">
						<label class="label" for="create-brand-description">
							<span class="label-text">คำอธิบาย</span>
						</label>
						<textarea
							id="create-brand-description"
							name="description"
							placeholder="คำอธิบายเกี่ยวกับแบรนด์"
							bind:value={brandForm.description}
							class="textarea textarea-bordered w-full"
							rows="3"
						></textarea>
					</div>

					<div class="space-y-2">
						<label class="label" for="create-brand-logo">
							<span class="label-text">URL โลโก้</span>
						</label>
						<input
							id="create-brand-logo"
							type="url"
							name="logoUrl"
							placeholder="https://example.com/logo.png"
							bind:value={brandForm.logoUrl}
							class="input input-bordered w-full"
						/>
					</div>

					<div class="space-y-2">
						<label class="label" for="create-brand-website">
							<span class="label-text">เว็บไซต์แบรนด์</span>
						</label>
						<input
							id="create-brand-website"
							type="url"
							name="websiteUrl"
							placeholder="https://brand-website.com"
							bind:value={brandForm.websiteUrl}
							class="input input-bordered w-full"
						/>
					</div>

					<div class="form-control">
						<label class="label cursor-pointer">
							<span class="label-text">เปิดใช้งาน</span>
							<input
								type="checkbox"
								name="isActive"
								bind:checked={brandForm.isActive}
								class="checkbox"
							/>
						</label>
					</div>
				</div>

				<div class="modal-action">
					<Button type="button" variant="outline" onclick={() => {
						createDialogOpen = false;
						resetForm();
					}}>
						ยกเลิก
					</Button>
					<Button type="submit" disabled={isLoading}>
						{#if isLoading}
							<span class="loading loading-spinner loading-sm mr-2"></span>
						{:else}
							<Icon icon="heroicons:plus" class="h-4 w-4 mr-2" />
						{/if}
						สร้างแบรนด์
					</Button>
				</div>
			</form>
		</div>
	</div>
{/if}

<!-- Edit Brand Modal -->
{#if editDialogOpen}
	<div class="modal modal-open">
		<div class="modal-box max-w-2xl">
			<h3 class="font-bold text-lg mb-4">แก้ไขแบรนด์</h3>
			
			<form
				method="POST"
				action="?/updateBrand"
				use:enhance={() => {
					isLoading = true;
					return async ({ result }) => {
						handleFormResult(result);
					};
				}}
			>
				<input type="hidden" name="brandId" value={selectedBrand?._id} />
				
				<div class="space-y-4">
					<div class="space-y-2">
						<label class="label" for="edit-brand-name">
							<span class="label-text">ชื่อแบรนด์ *</span>
						</label>
						<input
							id="edit-brand-name"
							type="text"
							name="name"
							placeholder="ชื่อแบรนด์"
							bind:value={brandForm.name}
							class="input input-bordered w-full"
							required
						/>
					</div>

					<div class="space-y-2">
						<label class="label" for="edit-brand-description">
							<span class="label-text">คำอธิบาย</span>
						</label>
						<textarea
							id="edit-brand-description"
							name="description"
							placeholder="คำอธิบายเกี่ยวกับแบรนด์"
							bind:value={brandForm.description}
							class="textarea textarea-bordered w-full"
							rows="3"
						></textarea>
					</div>

					<div class="space-y-2">
						<label class="label" for="edit-brand-logo">
							<span class="label-text">URL โลโก้</span>
						</label>
						<input
							id="edit-brand-logo"
							type="url"
							name="logoUrl"
							placeholder="https://example.com/logo.png"
							bind:value={brandForm.logoUrl}
							class="input input-bordered w-full"
						/>
					</div>

					<div class="space-y-2">
						<label class="label" for="edit-brand-website">
							<span class="label-text">เว็บไซต์แบรนด์</span>
						</label>
						<input
							id="edit-brand-website"
							type="url"
							name="websiteUrl"
							placeholder="https://brand-website.com"
							bind:value={brandForm.websiteUrl}
							class="input input-bordered w-full"
						/>
					</div>

					<div class="form-control">
						<label class="label cursor-pointer">
							<span class="label-text">เปิดใช้งาน</span>
							<input
								type="checkbox"
								name="isActive"
								bind:checked={brandForm.isActive}
								class="checkbox"
							/>
						</label>
					</div>
				</div>

				<div class="modal-action">
					<Button type="button" variant="outline" onclick={() => {
						editDialogOpen = false;
						resetForm();
					}}>
						ยกเลิก
					</Button>
					<Button type="submit" disabled={isLoading}>
						{#if isLoading}
							<span class="loading loading-spinner loading-sm mr-2"></span>
						{:else}
							<Icon icon="heroicons:pencil" class="h-4 w-4 mr-2" />
						{/if}
						อัปเดตแบรนด์
					</Button>
				</div>
			</form>
		</div>
	</div>
{/if}

import { addonsService } from '$lib/services/addons';
import { error, redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ params, locals, url }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  // ✅ ดึงข้อมูล addons ทั้งหมด
  const addonsResult = await addonsService.getSiteAddons(siteId, locals.token!);
  const addons = addonsResult.success ? addonsResult.data : [];

  // ✅ ตรวจสอบ addon ที่ต้องการจาก URL path
  const pathSegments = url.pathname.split('/');
  const contentTypeIndex = pathSegments.findIndex(segment => segment === 'contents');
  const contentType = pathSegments[contentTypeIndex + 1]; // news, blogs, novels

  if (contentType && ['news', 'blog', 'novels'].includes(contentType)) {
    // แปลง novels เป็น novel สำหรับตรวจสอบ addon
    const addonId = contentType === 'novels' ? 'novel' : contentType;

    // ตรวจสอบว่า addon นี้ active หรือไม่
    const addon = addons.find(a => a.id === addonId);
    const isActive = addon?.isActive || false;
    const isPurchased = addon?.isPurchased || false;

    // ถ้ายังไม่ได้ซื้อหรือไม่ active ให้ redirect ไปหน้าเตือน
    if (!isPurchased || !isActive) {
      throw redirect(302, `/dashboard/${siteId}/contents/addon-required?addon=${addonId}&type=${contentType}`);
    }
  }

  return {
    addons,
    siteId,
  };
};

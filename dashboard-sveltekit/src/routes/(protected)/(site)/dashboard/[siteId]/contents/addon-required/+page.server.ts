import { addonsService } from '$lib/services/addons';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, url }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  // ✅ ดึงข้อมูลจาก query parameters
  const addonId = url.searchParams.get('addon');
  const contentType = url.searchParams.get('type');

  if (!addonId || !contentType) {
    throw error(400, 'Missing addon or type parameter');
  }

  // ✅ ดึงข้อมูล addon ที่ต้องการ
  const addonResult = await addonsService.getAddonWithFallback(siteId, addonId, locals.token!);
  const addon = addonResult.data;

  // ✅ ข้อมูล addon ที่มีให้เลือก
  const addonInfo = {
    news: {
      name: 'ระบบข่าวสาร',
      description: 'จัดการข่าวสารและประชาสัมพันธ์',
      icon: 'mdi:newspaper',
      price: 299,
      features: ['สร้างข่าวสาร', 'จัดหมวดหมู่', 'SEO ข่าวสาร', 'แสดงผลหน้าเว็บ'],
    },
    blog: {
      name: 'ระบบบล็อก',
      description: 'เขียนบทความและแชร์เนื้อหา',
      icon: 'mdi:post',
      price: 399,
      features: ['เขียนบทความ', 'ระบบแท็ก', 'ความคิดเห็น', 'แชร์โซเชียล'],
    },
    novel: {
      name: 'ระบบนิยาย',
      description: 'เผยแพร่นิยายและเรื่องสั้น',
      icon: 'mdi:book-open-page-variant',
      price: 599,
      features: ['เขียนนิยาย', 'แบ่งตอน', 'ระบบจองอ่าน', 'ระบบเรตติ้ง'],
    },
  };

  const currentAddonInfo = addonInfo[addonId as keyof typeof addonInfo];

  return {
    addon,
    addonInfo: currentAddonInfo,
    addonId,
    contentType,
    siteId,
  };
};

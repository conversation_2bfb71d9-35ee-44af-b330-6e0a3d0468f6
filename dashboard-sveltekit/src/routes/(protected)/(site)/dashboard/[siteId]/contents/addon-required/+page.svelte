<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';

	// ✅ Props from server
	const { data } = $props<{
		data: any;
	}>();

	const siteId = page.params.siteId;

	// ✅ Helper functions
	function goToAddons() {
		goto(`/dashboard/${siteId}/addons`);
	}

	function goToDashboard() {
		goto(`/dashboard/${siteId}`);
	}

	// ✅ ตรวจสอบสถานะ addon
	const isAddonPurchased = data.addon?.isPurchased || false;
	const isAddonActive = data.addon?.isActive || false;
	const canActivate = isAddonPurchased && !isAddonActive;
</script>

<svelte:head>
	<title>ต้องการระบบเสริม - {data.addonInfo?.name}</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="max-w-2xl mx-auto">
		<!-- Header -->
		<div class="text-center mb-8">
			<div class="p-4 bg-warning/10 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
				<Icon icon="mdi:lock" class="w-10 h-10 text-warning" />
			</div>
			<h1 class="text-3xl font-bold text-warning mb-2">ต้องการระบบเสริม</h1>
			<p class="text-base-content/70">คุณต้องเช่าระบบเสริมนี้ก่อนจึงจะสามารถใช้งานได้</p>
		</div>

		<!-- Addon Info Card -->
		<div class="card bg-base-100 shadow-xl border mb-6">
			<div class="card-body">
				<div class="flex items-center gap-4 mb-4">
					<div class="p-3 bg-primary/10 rounded-lg">
						<Icon icon={data.addonInfo?.icon} class="w-8 h-8 text-primary" />
					</div>
					<div>
						<h2 class="card-title text-xl">{data.addonInfo?.name}</h2>
						<p class="text-base-content/70">{data.addonInfo?.description}</p>
					</div>
				</div>

				<!-- ฟีเจอร์ -->
				<div class="mb-4">
					<h4 class="font-semibold mb-2">ฟีเจอร์ที่จะได้รับ:</h4>
					<ul class="space-y-2">
						{#each data.addonInfo?.features || [] as feature}
							<li class="flex items-center gap-2">
								<Icon icon="mdi:check-circle" class="w-5 h-5 text-success" />
								<span>{feature}</span>
							</li>
						{/each}
					</ul>
				</div>

				<!-- ราคา -->
				<div class="flex items-center justify-between mb-4">
					<div class="text-2xl font-bold text-primary">
						฿{data.addonInfo?.price?.toLocaleString()}
						<span class="text-sm font-normal text-base-content/70">/เดือน</span>
					</div>
				</div>

				<!-- สถานะปัจจุบัน -->
				<div class="alert mb-4">
					<Icon icon="mdi:information" class="w-6 h-6" />
					<div>
						{#if !isAddonPurchased}
							<h3 class="font-bold">ยังไม่ได้เช่าระบบเสริมนี้</h3>
							<div class="text-sm">คุณต้องเช่าระบบเสริมนี้ก่อนจึงจะสามารถใช้งานได้</div>
						{:else if canActivate}
							<h3 class="font-bold">เช่าแล้วแต่ยังไม่เปิดใช้งาน</h3>
							<div class="text-sm">คุณได้เช่าระบบเสริมนี้แล้ว แต่ยังไม่ได้เปิดใช้งาน</div>
						{:else}
							<h3 class="font-bold">ระบบเสริมไม่พร้อมใช้งาน</h3>
							<div class="text-sm">กรุณาตรวจสอบสถานะระบบเสริมในหน้าจัดการ</div>
						{/if}
					</div>
				</div>

				<!-- ปุ่มดำเนินการ -->
				<div class="card-actions justify-end">
					{#if !isAddonPurchased}
						<!-- ยังไม่ได้เช่า: แสดงปุ่มไปหน้าเช่า -->
						<button 
							class="btn btn-primary"
							onclick={goToAddons}
						>
							<Icon icon="mdi:cart-plus" class="w-4 h-4" />
							เช่าระบบเสริมนี้
						</button>
					{:else if canActivate}
						<!-- เช่าแล้วแต่ยังไม่เปิดใช้งาน: แสดงปุ่มไปหน้าจัดการ -->
						<button 
							class="btn btn-primary"
							onclick={goToAddons}
						>
							<Icon icon="mdi:play" class="w-4 h-4" />
							เปิดใช้งานระบบเสริม
						</button>
					{:else}
						<!-- กรณีอื่นๆ: แสดงปุ่มไปหน้าจัดการ -->
						<button 
							class="btn btn-primary"
							onclick={goToAddons}
						>
							<Icon icon="mdi:cog" class="w-4 h-4" />
							จัดการระบบเสริม
						</button>
					{/if}
					
					<button 
						class="btn btn-ghost"
						onclick={goToDashboard}
					>
						<Icon icon="mdi:arrow-left" class="w-4 h-4" />
						กลับหน้าหลัก
					</button>
				</div>
			</div>
		</div>

		<!-- ข้อมูลเพิ่มเติม -->
		<div class="bg-base-200 rounded-lg p-6">
			<h3 class="text-lg font-bold mb-4">ข้อมูลสำคัญ</h3>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
				<div>
					<h4 class="font-semibold mb-2">การเช่าใช้งาน:</h4>
					<ul class="space-y-1 text-base-content/70">
						<li>• ระบบเสริมจะเรียกเก็บเงินรายเดือน</li>
						<li>• สามารถยกเลิกได้ทุกเมื่อ</li>
						<li>• ข้อมูลจะถูกเก็บไว้ 30 วันหลังยกเลิก</li>
					</ul>
				</div>
				<div>
					<h4 class="font-semibold mb-2">การใช้งาน:</h4>
					<ul class="space-y-1 text-base-content/70">
						<li>• เช่าแล้วต้องเปิดใช้งานก่อนใช้</li>
						<li>• สามารถปิด/เปิดใช้งานได้ตลอดเวลา</li>
						<li>• ระบบที่ปิดใช้งานจะไม่แสดงในเว็บไซต์</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>
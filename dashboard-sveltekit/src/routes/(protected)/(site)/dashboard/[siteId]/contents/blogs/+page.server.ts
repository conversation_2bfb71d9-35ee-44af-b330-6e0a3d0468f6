import { addonsService } from '$lib/services/addons';
import { contentService } from '$lib/services/content';
import { LogCategory, logger } from '$lib/utils/logger';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  if (!siteId) {
    throw error(400, 'Site ID is required');
  }

  try {
    // ✅ ใช้ Addons Service พร้อม proper validation
    const blogAddonResult = await addonsService.getAddonWithFallback(siteId, 'blog', locals.token!);
    const blogAddon = blogAddonResult.data;

    // ✅ ตรวจสอบว่า addon ได้ซื้อและ active หรือไม่
    if (!blogAddon?.isPurchased || !blogAddon?.isActive) {
      throw redirect(302, `/dashboard/${siteId}/contents/addon-required?addon=blog&type=blogs`);
    }

    // ✅ ใช้ Content Service แทน direct fetch
    const blogsResult = await contentService.getBlogs(siteId, locals.token!);
    const blogs = blogsResult.success ? blogsResult.data : [];

    return {
      blogs,
      siteId,
      addon: blogAddon,
    };
  }
  catch (err) {
    if (err instanceof Response) throw err;
    console.error('Error loading blogs:', err);
    throw error(500, 'Internal server error');
  }
};

export const actions: Actions = {
  /**
   * ✅ Delete Blog - Hybrid Approach
   * Route API + Service Pattern
   */
  deleteBlog: async ({ request, params, locals, getClientAddress }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'delete',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const deleteData = {
        blogId: formData.get('blogId')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!deleteData.blogId) {
        return fail(400, {
          message: 'กรุณาเลือกบล็อกที่ต้องการลบ',
          type: 'delete',
          fields: {
            blogId: 'กรุณาเลือกบล็อก',
          },
        });
      }

      logger.info(LogCategory.CONTENT, 'delete_blog_attempt', 'Blog delete attempt', {
        siteId,
        blogId: deleteData.blogId,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.deleteBlog(deleteData.blogId, locals.token!);

      if (!result.success) {
        logger.warn(LogCategory.CONTENT, 'delete_blog_failed', 'Blog delete failed', {
          siteId,
          blogId: deleteData.blogId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถลบบล็อกได้',
          type: 'delete',
        });
      }

      logger.info(LogCategory.CONTENT, 'delete_blog_success', 'Blog delete successful', {
        siteId,
        blogId: deleteData.blogId,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: 'ลบบล็อกเรียบร้อยแล้ว',
        type: 'delete',
      };
    }
    catch (error) {
      logger.error(LogCategory.CONTENT, 'delete_blog_error', 'Blog delete error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบบล็อก',
        type: 'delete',
      });
    }
  },

  /**
   * ✅ Toggle Blog Publish Status - Hybrid Approach
   * Route API + Service Pattern
   */
  togglePublish: async ({ request, params, locals, getClientAddress }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'toggle',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const toggleData = {
        blogId: formData.get('blogId')?.toString()?.trim(),
        published: formData.get('published') === 'true',
      };

      // ✅ Basic validation at route level
      if (!toggleData.blogId) {
        return fail(400, {
          message: 'กรุณาเลือกบล็อกที่ต้องการเปลี่ยนสถานะ',
          type: 'toggle',
          fields: {
            blogId: 'กรุณาเลือกบล็อก',
          },
        });
      }

      logger.info(LogCategory.CONTENT, 'toggle_blog_publish_attempt', 'Blog publish toggle attempt', {
        siteId,
        blogId: toggleData.blogId,
        published: toggleData.published,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.toggleBlogPublish(
        toggleData.blogId,
        toggleData.published,
        locals.token,
      );

      if (!result.success) {
        logger.warn(LogCategory.CONTENT, 'toggle_blog_publish_failed', 'Blog publish toggle failed', {
          siteId,
          blogId: toggleData.blogId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถเปลี่ยนสถานะได้',
          type: 'toggle',
        });
      }

      logger.info(LogCategory.CONTENT, 'toggle_blog_publish_success', 'Blog publish toggle successful', {
        siteId,
        blogId: toggleData.blogId,
        published: toggleData.published,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: toggleData.published ? 'เผยแพร่บล็อกแล้ว' : 'ซ่อนบล็อกแล้ว',
        type: 'toggle',
      };
    }
    catch (error) {
      logger.error(LogCategory.CONTENT, 'toggle_blog_publish_error', 'Blog publish toggle error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ',
        type: 'toggle',
      });
    }
  },
};

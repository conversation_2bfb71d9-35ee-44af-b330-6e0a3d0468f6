<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import { enhance } from '$app/forms';
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';

	// ✅ Props from server
	const { data, form } = $props<{
		data: any;
		form?: any;
	}>();

	const siteId = $derived(page.params.siteId);

	// ✅ Client state management
	let isDeleteLoading = $state(false);
	let isToggleLoading = $state(false);

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			if (formResult.type === 'delete') {
				showSuccess('ลบบล็อกสำเร็จ!', formResult.message);
			} else if (formResult.type === 'toggle') {
				showSuccess('เปลี่ยนสถานะสำเร็จ!', formResult.message);
			}
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleDeleteResult(result: any) {
		isDeleteLoading = false;

		if (result.type === 'success') {
			showSuccess('ลบบล็อกสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('ลบบล็อกไม่สำเร็จ', result.data?.message);
		}
	}

	function handleToggleResult(result: any) {
		isToggleLoading = false;

		if (result.type === 'success') {
			showSuccess('เปลี่ยนสถานะสำเร็จ!', result.data?.message);
		} else if (result.type === 'failure') {
			showError('เปลี่ยนสถานะไม่สำเร็จ', result.data?.message);
		}
	}

	// ✅ ลบบล็อกด้วย form action
	function deleteBlog(blogId: string) {
		const form = document.getElementById(`delete-form-${blogId}`) as HTMLFormElement;
		if (form) {
			form.requestSubmit();
		}
	}

	// ✅ เปลี่ยนสถานะการเผยแพร่ด้วย form action
	function togglePublish(blogId: string, currentStatus: boolean) {
		const form = document.getElementById(`toggle-form-${blogId}`) as HTMLFormElement;
		if (form) {
			// Update hidden input value
			const publishedInput = form.querySelector('input[name="published"]') as HTMLInputElement;
			if (publishedInput) {
				publishedInput.value = (!currentStatus).toString();
			}
			form.requestSubmit();
		}
	}
</script>

<svelte:head>
	<title>จัดการบล็อก - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="flex items-center justify-between mb-6">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-3">
				<Icon icon="mdi:post" class="text-primary" />
				จัดการบล็อก
			</h1>
			<p class="text-base-content/70 mt-2">เขียนและจัดการบทความบล็อก</p>
		</div>
		<a href="/dashboard/{siteId}/contents/blogs/create" class="btn btn-primary">
			<Icon icon="mdi:plus" class="w-4 h-4" />
			เขียนบล็อกใหม่
		</a>
	</div>

	<!-- สถิติ -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-primary">
				<Icon icon="mdi:post" class="w-8 h-8" />
			</div>
			<div class="stat-title">บล็อกทั้งหมด</div>
			<div class="stat-value text-primary">{data.blogs?.length || 0}</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-success">
				<Icon icon="mdi:check-circle" class="w-8 h-8" />
			</div>
			<div class="stat-title">เผยแพร่แล้ว</div>
			<div class="stat-value text-success">
				{data.blogs?.filter((b: any) => b.published).length || 0}
			</div>
		</div>
		<div class="stat bg-base-100 rounded-lg shadow">
			<div class="stat-figure text-warning">
				<Icon icon="mdi:clock" class="w-8 h-8" />
			</div>
			<div class="stat-title">แบบร่าง</div>
			<div class="stat-value text-warning">
				{data.blogs?.filter((b: any) => !b.published).length || 0}
			</div>
		</div>
	</div>

	<!-- ✅ Hidden Forms for Hybrid Approach -->
	{#if data.blogs && data.blogs.length > 0}
		{#each data.blogs as blog}
			<!-- Delete Form -->
			<form
				id="delete-form-{blog.id}"
				method="POST"
				action="?/deleteBlog"
				use:enhance={() => {
					isDeleteLoading = true;
					showLoading('กำลังลบบล็อก...');

					return async ({ result }) => {
						handleDeleteResult(result);
					};
				}}
				class="hidden"
			>
				<input type="hidden" name="blogId" value={blog.id} />
			</form>

			<!-- Toggle Publish Form -->
			<form
				id="toggle-form-{blog.id}"
				method="POST"
				action="?/togglePublish"
				use:enhance={() => {
					isToggleLoading = true;
					showLoading('กำลังเปลี่ยนสถานะ...');

					return async ({ result }) => {
						handleToggleResult(result);
					};
				}}
				class="hidden"
			>
				<input type="hidden" name="blogId" value={blog.id} />
				<input type="hidden" name="published" value={blog.published.toString()} />
			</form>
		{/each}
	{/if}

	<!-- ตารางบล็อก -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<div class="overflow-x-auto">
				<table class="table table-zebra">
					<thead>
						<tr>
							<th>หัวข้อ</th>
							<th>แท็ก</th>
							<th>สถานะ</th>
							<th>วันที่สร้าง</th>
							<th>การดำเนินการ</th>
						</tr>
					</thead>
					<tbody>
						{#if data.blogs && data.blogs.length > 0}
							{#each data.blogs as blog}
								<tr>
									<td>
										<div class="flex items-center gap-3">
											{#if blog.featuredImage}
												<div class="avatar">
													<div class="mask mask-squircle w-12 h-12">
														<img src={blog.featuredImage} alt={blog.title} />
													</div>
												</div>
											{/if}
											<div>
												<div class="font-bold">{blog.title}</div>
												<div class="text-sm opacity-50 truncate max-w-xs">
													{blog.excerpt || blog.content?.substring(0, 100) + '...'}
												</div>
											</div>
										</div>
									</td>
									<td>
										{#if blog.tags && blog.tags.length > 0}
											<div class="flex flex-wrap gap-1">
												{#each blog.tags.slice(0, 3) as tag}
													<div class="badge badge-outline badge-sm">{tag}</div>
												{/each}
												{#if blog.tags.length > 3}
													<div class="badge badge-ghost badge-sm">+{blog.tags.length - 3}</div>
												{/if}
											</div>
										{:else}
											<span class="text-base-content/50">ไม่มีแท็ก</span>
										{/if}
									</td>
									<td>
										<div class="form-control">
											<label class="label cursor-pointer">
												<input 
													type="checkbox" 
													class="toggle toggle-success" 
													checked={blog.published}
													onchange={() => togglePublish(blog.id, blog.published)}
												/>
												<span class="label-text ml-2">
													{blog.published ? 'เผยแพร่' : 'แบบร่าง'}
												</span>
											</label>
										</div>
									</td>
									<td>{new Date(blog.createdAt).toLocaleDateString('th-TH')}</td>
									<td>
										<div class="flex gap-2">
											<a 
												href="/dashboard/{siteId}/contents/blogs/{blog.id}/edit"
												class="btn btn-ghost btn-sm"
											>
												<Icon icon="mdi:pencil" class="w-4 h-4" />
											</a>
											<button 
												class="btn btn-ghost btn-sm text-error"
												onclick={() => deleteBlog(blog.id)}
											>
												<Icon icon="mdi:delete" class="w-4 h-4" />
											</button>
										</div>
									</td>
								</tr>
							{/each}
						{:else}
							<tr>
								<td colspan="5" class="text-center py-8">
									<div class="flex flex-col items-center gap-4">
										<Icon icon="mdi:post-outline" class="w-16 h-16 text-base-content/30" />
										<div>
											<h3 class="text-lg font-semibold">ยังไม่มีบล็อก</h3>
											<p class="text-base-content/70">เริ่มเขียนบล็อกแรกของคุณ</p>
										</div>
										<a href="/dashboard/{siteId}/contents/blogs/create" class="btn btn-primary">
											<Icon icon="mdi:plus" class="w-4 h-4" />
											เขียนบล็อกใหม่
										</a>
									</div>
								</td>
							</tr>
						{/if}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
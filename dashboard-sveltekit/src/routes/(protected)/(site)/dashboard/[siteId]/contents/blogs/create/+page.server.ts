import { sanitizeBlogData, validateCreateBlogData } from '$lib/schemas/content.schema';
import { addonsService } from '$lib/services/addons';
import { contentService } from '$lib/services/content';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, fetch }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // ✅ ใช้ Addons Service พร้อม proper validation
    const blogAddonResult = await addonsService.getAddonWithFallback(siteId, 'blog', locals.token!);
    const blogAddon = blogAddonResult.data;

    // ✅ ตรวจสอบว่า addon ได้ซื้อและ active หรือไม่
    if (!blogAddon?.isPurchased || !blogAddon?.isActive) {
      throw redirect(302, `/dashboard/${siteId}/contents/addon-required?addon=blog&type=blogs`);
    }

    return {
      siteId,
    };
  }
  catch (err) {
    if (err instanceof Response) throw err;
    console.error('Error loading create blog page:', err);
    throw error(500, 'Internal server error');
  }
};

export const actions: Actions = {
  /**
   * ✅ Create Blog - Hybrid Approach
   * Route API + Service Pattern
   */
  createBlog: async ({ request, params, locals, getClientAddress }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'createBlog',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Extract form data
      const rawData = {
        title: formData.get('title')?.toString()?.trim() || '',
        content: formData.get('content')?.toString()?.trim() || '',
        excerpt: formData.get('excerpt')?.toString()?.trim() || '',
        tags: formData.get('tags')?.toString()?.trim().split(',').map(tag => tag.trim()).filter(tag => tag) || [],
        published: formData.get('published') === 'on',
        featuredImage: formData.get('featuredImage')?.toString()?.trim() || '',
      };

      // ✅ Schema validation
      const validation = validateCreateBlogData(rawData);
      if (!validation.success) {
        return fail(400, {
          message: validation.error || 'ข้อมูลไม่ถูกต้อง',
          type: 'createBlog',
          fields: validation.errors || {},
        });
      }

      // ✅ Sanitize data
      const sanitizedData = sanitizeBlogData(rawData);

      console.log('Creating blog:', {
        siteId,
        title: sanitizedData.title,
        published: sanitizedData.published,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.createBlog(sanitizedData, siteId, locals.token!);

      if (!result.success) {
        console.warn('Create blog failed:', {
          siteId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถสร้างบล็อกได้',
          type: 'createBlog',
        });
      }

      console.log('Create blog successful:', {
        siteId,
        blogId: result.data?.id,
        title: sanitizedData.title,
        userId: locals.user._id,
        clientIP,
      });

      // ✅ Redirect on success
      throw redirect(302, `/dashboard/${siteId}/contents/blogs`);
    }
    catch (error) {
      if (error instanceof Response) throw error;

      console.error('Create blog error:', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างบล็อก',
        type: 'createBlog',
      });
    }
  },
};

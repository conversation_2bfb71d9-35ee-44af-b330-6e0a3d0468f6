<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import { enhance } from '$app/forms';

	const { data, form } = $props();

	const siteId = page.params.siteId;

	let title = $state('');
	let content = $state('');
	let excerpt = $state('');
	let tags = $state('');
	let published = $state(false);
	let featuredImage = $state('');
	let isSubmitting = $state(false);

	// Auto-generate excerpt from content
	$effect(() => {
		if (content && !excerpt) {
			excerpt = content.replace(/<[^>]*>/g, '').substring(0, 200) + '...';
		}
	});
</script>

<svelte:head>
	<title>เขียนบล็อกใหม่ - Dashboard</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="flex items-center justify-between mb-6">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-3">
				<Icon icon="mdi:post-outline" class="text-primary" />
				เขียนบล็อกใหม่
			</h1>
			<p class="text-base-content/70 mt-2">สร้างและแชร์บทความใหม่</p>
		</div>
		<a href="/dashboard/{siteId}/contents/blogs" class="btn btn-ghost">
			<Icon icon="mdi:arrow-left" class="w-4 h-4" />
			ย้อนกลับ
		</a>
	</div>

	{#if form?.message}
		<div class="alert alert-error mb-6">
			<Icon icon="mdi:alert-circle" class="w-6 h-6" />
			<span>{form.message}</span>
		</div>
	{/if}

	<form 
		method="POST" 
		action="?/createBlog"
		use:enhance={() => {
			isSubmitting = true;
			return async ({ result, update }) => {
				isSubmitting = false;
				
				if (result.type === 'success') {
					// Success will redirect automatically
				} else if (result.type === 'failure') {
					// Handle error - update will show the error
					await update();
				}
			};
		}}
		class="grid grid-cols-1 lg:grid-cols-3 gap-6"
	>
		<!-- เนื้อหาหลัก -->
		<div class="lg:col-span-2 space-y-6">
			<!-- หัวข้อ -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">หัวข้อบล็อก</h2>
					<input 
						type="text" 
						name="title"
						bind:value={title}
						placeholder="กรอกหัวข้อบล็อก..." 
						class="input input-bordered w-full text-lg"
						required
					/>
				</div>
			</div>

			<!-- เนื้อหา -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">เนื้อหา</h2>
					<textarea 
						name="content"
						bind:value={content}
						placeholder="เขียนเนื้อหาบล็อก..."
						class="textarea textarea-bordered w-full h-96"
						required
					></textarea>
					<div class="text-sm text-base-content/70 mt-2">
						ใช้ HTML tags ได้ เช่น &lt;p&gt;, &lt;strong&gt;, &lt;em&gt;, &lt;ul&gt;, &lt;ol&gt;
					</div>
				</div>
			</div>

			<!-- สรุปเนื้อหา -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">สรุปเนื้อหา</h2>
					<textarea 
						name="excerpt"
						bind:value={excerpt}
						placeholder="สรุปเนื้อหาสั้นๆ..."
						class="textarea textarea-bordered w-full h-24"
					></textarea>
					<div class="text-sm text-base-content/70 mt-2">
						จะถูกใช้แสดงในหน้ารายการบล็อก (ถ้าไม่กรอกจะสร้างอัตโนมัติ)
					</div>
				</div>
			</div>
		</div>

		<!-- แถบด้านข้าง -->
		<div class="space-y-6">
			<!-- การเผยแพร่ -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">การเผยแพร่</h2>
					<div class="form-control">
						<label class="label cursor-pointer">
							<span class="label-text">เผยแพร่ทันที</span>
							<input 
								type="checkbox" 
								name="published"
								bind:checked={published}
								class="toggle toggle-primary" 
							/>
						</label>
					</div>
					<div class="text-sm text-base-content/70">
						{published ? 'บล็อกจะแสดงในเว็บไซต์ทันที' : 'บันทึกเป็นแบบร่าง'}
					</div>
				</div>
			</div>

			<!-- แท็ก -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">แท็ก</h2>
					<input 
						type="text" 
						name="tags"
						bind:value={tags}
						placeholder="แท็ก1, แท็ก2, แท็ก3..." 
						class="input input-bordered w-full"
					/>
					<div class="text-sm text-base-content/70 mt-2">
						แยกแท็กด้วยเครื่องหมายจุลภาค (,)
					</div>
					{#if tags}
						<div class="flex flex-wrap gap-2 mt-2">
							{#each tags.split(',').map(tag => tag.trim()).filter(tag => tag) as tag}
								<div class="badge badge-outline">{tag}</div>
							{/each}
						</div>
					{/if}
				</div>
			</div>

			<!-- รูปภาพประกอบ -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">รูปภาพประกอบ</h2>
					<input 
						type="url" 
						name="featuredImage"
						bind:value={featuredImage}
						placeholder="URL รูปภาพ..." 
						class="input input-bordered w-full"
					/>
					{#if featuredImage}
						<div class="mt-4">
							<img 
								src={featuredImage} 
								alt="Preview" 
								class="w-full h-32 object-cover rounded-lg"
								onerror={() => featuredImage = ''}
							/>
						</div>
					{/if}
				</div>
			</div>

			<!-- ปุ่มบันทึก -->
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<button 
						type="submit" 
						class="btn btn-primary w-full"
						class:loading={isSubmitting}
						disabled={isSubmitting}
					>
						{#if isSubmitting}
							<span class="loading loading-spinner"></span>
							กำลังบันทึก...
						{:else}
							<Icon icon="mdi:content-save" class="w-4 h-4" />
							{published ? 'เผยแพร่บล็อก' : 'บันทึกแบบร่าง'}
						{/if}
					</button>
				</div>
			</div>
		</div>
	</form>
</div>
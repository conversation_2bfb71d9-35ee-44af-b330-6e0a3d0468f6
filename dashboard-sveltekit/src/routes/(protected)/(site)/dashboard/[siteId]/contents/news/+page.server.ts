import { addonsService } from '$lib/services/addons';
import { contentService } from '$lib/services/content';
import { LogCategory, logger } from '$lib/utils/logger';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, fetch }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // ✅ ใช้ Addons Service พร้อม proper validation
    const newsAddonResult = await addonsService.getAddonWithFallback(siteId, 'news', locals.token!);
    const newsAddon = newsAddonResult.data;

    // ✅ ตรวจสอบว่า addon ได้ซื้อและ active หรือไม่
    if (!newsAddon.isPurchased || !newsAddon.isActive) {
      throw redirect(302, `/dashboard/${siteId}/contents/addon-required?addon=news&type=news`);
    }

    // ✅ ใช้ Content Service แทน direct fetch
    const newsResult = await contentService.getNews(siteId, locals.token!);
    const news = newsResult.success ? newsResult.data : [];

    const categoriesResult = await contentService.getNewsCategories(siteId, locals.token!);
    const categories = categoriesResult.success ? categoriesResult.data : [];

    return {
      news,
      categories,
      siteId,
      addon: newsAddon,
    };
  }
  catch (err) {
    if (err instanceof Response) throw err;
    console.error('Error loading news:', err);
    throw error(500, 'Internal server error');
  }
};

export const actions: Actions = {
  /**
   * ✅ Delete News - Hybrid Approach
   * Route API + Service Pattern
   */
  deleteNews: async ({ request, params, locals, getClientAddress, fetch }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'delete',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const deleteData = {
        newsId: formData.get('newsId')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!deleteData.newsId) {
        return fail(400, {
          message: 'กรุณาเลือกข่าวที่ต้องการลบ',
          type: 'delete',
          fields: {
            newsId: 'กรุณาเลือกข่าว',
          },
        });
      }

      logger.info(LogCategory.CONTENT, 'delete_news_attempt', 'News delete attempt', {
        siteId,
        newsId: deleteData.newsId,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.deleteNews(deleteData.newsId, locals.token!);

      if (!result.success) {
        logger.warn(LogCategory.CONTENT, 'delete_news_failed', 'News delete failed', {
          siteId,
          newsId: deleteData.newsId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถลบข่าวได้',
          type: 'delete',
        });
      }

      logger.info(LogCategory.CONTENT, 'delete_news_success', 'News delete successful', {
        siteId,
        newsId: deleteData.newsId,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: 'ลบข่าวเรียบร้อยแล้ว',
        type: 'delete',
      };
    }
    catch (error) {
      logger.error(LogCategory.CONTENT, 'delete_news_error', 'News delete error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบข่าว',
        type: 'delete',
      });
    }
  },

  /**
   * ✅ Toggle News Publish Status - Hybrid Approach
   * Route API + Service Pattern
   */
  togglePublish: async ({ request, params, locals, getClientAddress, fetch }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'toggle',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const toggleData = {
        newsId: formData.get('newsId')?.toString()?.trim(),
        published: formData.get('published') === 'true',
      };

      // ✅ Basic validation at route level
      if (!toggleData.newsId) {
        return fail(400, {
          message: 'กรุณาเลือกข่าวที่ต้องการเปลี่ยนสถานะ',
          type: 'toggle',
          fields: {
            newsId: 'กรุณาเลือกข่าว',
          },
        });
      }

      logger.info(LogCategory.CONTENT, 'toggle_news_publish_attempt', 'News publish toggle attempt', {
        siteId,
        newsId: toggleData.newsId,
        published: toggleData.published,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.toggleNewsPublish(
        toggleData.newsId,
        toggleData.published,
        locals.token,
      );

      if (!result.success) {
        logger.warn(LogCategory.CONTENT, 'toggle_news_publish_failed', 'News publish toggle failed', {
          siteId,
          newsId: toggleData.newsId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถเปลี่ยนสถานะได้',
          type: 'toggle',
        });
      }

      logger.info(LogCategory.CONTENT, 'toggle_news_publish_success', 'News publish toggle successful', {
        siteId,
        newsId: toggleData.newsId,
        published: toggleData.published,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: toggleData.published ? 'เผยแพร่ข่าวแล้ว' : 'ซ่อนข่าวแล้ว',
        type: 'toggle',
      };
    }
    catch (error) {
      logger.error(LogCategory.CONTENT, 'toggle_news_publish_error', 'News publish toggle error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ',
        type: 'toggle',
      });
    }
  },
};

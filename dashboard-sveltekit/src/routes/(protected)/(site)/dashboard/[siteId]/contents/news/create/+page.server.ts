import { sanitizeNewsData, validateCreateNewsData } from '$lib/schemas/content.schema';
import { addonsService } from '$lib/services/addons';
import { contentService } from '$lib/services/content';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // ✅ ใช้ Addons Service พร้อม proper validation
    const newsAddonResult = await addonsService.getAddonWithFallback(siteId, 'news', locals.token!);
    const newsAddon = newsAddonResult.data;

    // ✅ ตรวจสอบว่า addon ได้ซื้อและ active หรือไม่
    if (!newsAddon.isPurchased || !newsAddon.isActive) {
      throw redirect(302, `/dashboard/${siteId}/contents/addon-required?addon=news&type=news`);
    }

    // ✅ ใช้ Content Service แทน direct fetch
    const categoriesResult = await contentService.getNewsCategories(siteId, locals.token!);
    const categories = categoriesResult.success ? categoriesResult.data : [];

    return {
      categories,
      siteId,
    };
  }
  catch (err) {
    if (err instanceof Response) throw err;
    console.error('Error loading create news page:', err);
    throw error(500, 'Internal server error');
  }
};

export const actions: Actions = {
  /**
   * ✅ Create News - Hybrid Approach
   * Route API + Service Pattern
   */
  createNews: async ({ request, params, locals, getClientAddress }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'createNews',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Extract form data
      const rawData = {
        title: formData.get('title')?.toString()?.trim() || '',
        content: formData.get('content')?.toString()?.trim() || '',
        excerpt: formData.get('excerpt')?.toString()?.trim() || '',
        categoryId: formData.get('categoryId')?.toString()?.trim() || '',
        published: formData.get('published') === 'on',
        featuredImage: formData.get('featuredImage')?.toString()?.trim() || '',
      };

      // ✅ Sanitize data
      const sanitizedData = sanitizeNewsData(rawData);
      // ✅ Schema validation
      const validation = validateCreateNewsData(sanitizedData);
      if (!validation.success) {
        return fail(400, {
          message: validation.error || 'ข้อมูลไม่ถูกต้อง',
          type: 'createNews',
          fields: validation.errors || {},
        });
      }

      console.log('Creating news:', {
        siteId,
        title: sanitizedData.title,
        published: sanitizedData.published,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.createNews(sanitizedData, siteId, locals.token!);

      if (!result.success) {
        console.warn('Create news failed:', {
          siteId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถสร้างข่าวได้',
          type: 'createNews',
        });
      }

      console.log('Create news successful:', {
        siteId,
        newsId: result.data?.id,
        title: sanitizedData.title,
        userId: locals.user._id,
        clientIP,
      });

      // ✅ Redirect on success
      throw redirect(302, `/dashboard/${siteId}/contents/news`);
    }
    catch (error) {
      if (error instanceof Response) throw error;

      console.error('Create news error:', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างข่าว',
        type: 'createNews',
      });
    }
  },
};

import { addonsService } from '$lib/services/addons';
import { contentService } from '$lib/services/content';
import { LogCategory, logger } from '$lib/utils/logger';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, fetch }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // ✅ ใช้ Addons Service พร้อม proper validation
    const novelAddonResult = await addonsService.getAddonWithFallback(siteId, 'novel', locals.token!);
    const novelAddon = novelAddonResult.data;

    // ✅ ตรวจสอบว่า addon ได้ซื้อและ active หรือไม่
    if (!novelAddon?.isPurchased || !novelAddon?.isActive) {
      throw redirect(302, `/dashboard/${siteId}/contents/addon-required?addon=novel&type=novels`);
    }

    // ✅ ใช้ Content Service แทน direct fetch
    const novelsResult = await contentService.getNovels(siteId, locals.token!);
    const novels = novelsResult.success ? novelsResult.data : [];

    return {
      novels,
      siteId,
      addon: novelAddon,
    };
  }
  catch (err) {
    if (err instanceof Response) throw err;
    console.error('Error loading novels:', err);
    throw error(500, 'Internal server error');
  }
};

export const actions: Actions = {
  /**
   * ✅ Delete Novel - Hybrid Approach
   * Route API + Service Pattern
   */
  deleteNovel: async ({ request, params, locals, getClientAddress, fetch }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'delete',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const deleteData = {
        novelId: formData.get('novelId')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!deleteData.novelId) {
        return fail(400, {
          message: 'กรุณาเลือกนิยายที่ต้องการลบ',
          type: 'delete',
          fields: {
            novelId: 'กรุณาเลือกนิยาย',
          },
        });
      }

      logger.info(LogCategory.CONTENT, 'delete_novel_attempt', 'Novel delete attempt', {
        siteId,
        novelId: deleteData.novelId,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.deleteNovel(deleteData.novelId, locals.token!);

      if (!result.success) {
        logger.warn(LogCategory.CONTENT, 'delete_novel_failed', 'Novel delete failed', {
          siteId,
          novelId: deleteData.novelId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถลบนิยายได้',
          type: 'delete',
        });
      }

      logger.info(LogCategory.CONTENT, 'delete_novel_success', 'Novel delete successful', {
        siteId,
        novelId: deleteData.novelId,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: 'ลบนิยายเรียบร้อยแล้ว',
        type: 'delete',
      };
    }
    catch (error) {
      logger.error(LogCategory.CONTENT, 'delete_novel_error', 'Novel delete error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบนิยาย',
        type: 'delete',
      });
    }
  },

  /**
   * ✅ Toggle Novel Publish Status - Hybrid Approach
   * Route API + Service Pattern
   */
  togglePublish: async ({ request, params, locals, getClientAddress, fetch }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'toggle',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const toggleData = {
        novelId: formData.get('novelId')?.toString()?.trim(),
        published: formData.get('published') === 'true',
      };

      // ✅ Basic validation at route level
      if (!toggleData.novelId) {
        return fail(400, {
          message: 'กรุณาเลือกนิยายที่ต้องการเปลี่ยนสถานะ',
          type: 'toggle',
          fields: {
            novelId: 'กรุณาเลือกนิยาย',
          },
        });
      }

      logger.info(LogCategory.CONTENT, 'toggle_novel_publish_attempt', 'Novel publish toggle attempt', {
        siteId,
        novelId: toggleData.novelId,
        published: toggleData.published,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.toggleNovelPublish(
        toggleData.novelId,
        toggleData.published,
        locals.token,
      );

      if (!result.success) {
        logger.warn(LogCategory.CONTENT, 'toggle_novel_publish_failed', 'Novel publish toggle failed', {
          siteId,
          novelId: toggleData.novelId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถเปลี่ยนสถานะได้',
          type: 'toggle',
        });
      }

      logger.info(LogCategory.CONTENT, 'toggle_novel_publish_success', 'Novel publish toggle successful', {
        siteId,
        novelId: toggleData.novelId,
        published: toggleData.published,
        userId: locals.user._id,
        clientIP,
      });

      return {
        success: true,
        message: toggleData.published ? 'เผยแพร่นิยายแล้ว' : 'ซ่อนนิยายแล้ว',
        type: 'toggle',
      };
    }
    catch (error) {
      logger.error(LogCategory.CONTENT, 'toggle_novel_publish_error', 'Novel publish toggle error', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ',
        type: 'toggle',
      });
    }
  },
};

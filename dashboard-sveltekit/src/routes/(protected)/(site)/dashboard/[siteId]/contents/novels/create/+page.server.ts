import { sanitizeNovelData, validateCreateNovelData } from '$lib/schemas/content.schema';
import { addonsService } from '$lib/services/addons';
import { contentService } from '$lib/services/content';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, fetch }) => {
  const { siteId } = params;

  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // ✅ ใช้ Addons Service พร้อม proper validation
    const novelAddonResult = await addonsService.getAddonWithFallback(siteId, 'novel', locals.token!);
    const novelAddon = novelAddonResult.data;

    // ✅ ตรวจสอบว่า addon ได้ซื้อและ active หรือไม่
    if (!novelAddon?.isPurchased || !novelAddon?.isActive) {
      throw redirect(302, `/dashboard/${siteId}/contents/addon-required?addon=novel&type=novels`);
    }

    return {
      siteId,
    };
  }
  catch (err) {
    if (err instanceof Response) throw err;
    console.error('Error loading create novel page:', err);
    throw error(500, 'Internal server error');
  }
};

export const actions: Actions = {
  /**
   * ✅ Create Novel - Hybrid Approach
   * Route API + Service Pattern
   */
  createNovel: async ({ request, params, locals, getClientAddress, fetch }) => {
    const { siteId } = params;
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!locals.user || !locals.token) {
      return fail(401, {
        message: 'กรุณาเข้าสู่ระบบ',
        type: 'createNovel',
      });
    }

    try {
      const formData = await request.formData();

      // ✅ Extract form data
      const rawData = {
        title: formData.get('title')?.toString()?.trim() || '',
        description: formData.get('description')?.toString()?.trim() || '',
        genre: formData.get('genre')?.toString()?.trim() || '',
        tags: formData.get('tags')?.toString()?.trim().split(',').map(tag => tag.trim()).filter(tag => tag) || [],
        published: formData.get('published') === 'on',
        coverImage: formData.get('coverImage')?.toString()?.trim() || '',
      };

      // ✅ Schema validation
      const validation = validateCreateNovelData(rawData);
      if (!validation.success) {
        return fail(400, {
          message: validation.error || 'ข้อมูลไม่ถูกต้อง',
          type: 'createNovel',
          fields: validation.errors || {},
        });
      }

      // ✅ Sanitize data
      const sanitizedData = sanitizeNovelData(validation.data);

      console.log('Creating novel:', {
        siteId,
        title: sanitizedData.title,
        published: sanitizedData.published,
        userId: locals.user._id,
        clientIP,
        userAgent,
      });

      // ✅ ใช้ Content Service แทน direct fetch
      const result = await contentService.createNovel(sanitizedData, siteId, locals.token!);

      if (!result.success) {
        console.warn('Create novel failed:', {
          siteId,
          error: result.error || 'Unknown error',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถสร้างนิยายได้',
          type: 'createNovel',
        });
      }

      console.log('Create novel successful:', {
        siteId,
        novelId: result.data?.id,
        title: sanitizedData.title,
        userId: locals.user._id,
        clientIP,
      });

      // ✅ Redirect on success
      throw redirect(302, `/dashboard/${siteId}/contents/novels`);
    }
    catch (error) {
      if (error instanceof Response) throw error;

      console.error('Create novel error:', {
        siteId,
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างนิยาย',
        type: 'createNovel',
      });
    }
  },
};

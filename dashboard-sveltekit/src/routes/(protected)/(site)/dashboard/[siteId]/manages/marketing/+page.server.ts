import {
  sanitizeCampaignData,
  sanitizeDiscountData,
  validateCampaignFilterData,
  validateCreateCampaignData,
  validateCreateDiscountData,
  validateDiscountFilterData,
} from '$lib/schemas/marketing.schema';
import { marketingService } from '$lib/services/marketing';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, url }) => {
  const { siteId } = params;

  try {
    if (!locals.token || !locals.user) {
      return {
        siteId,
        campaigns: [],
        discounts: [],
        analytics: null,
        error: 'กรุณาเข้าสู่ระบบ',
      };
    }

    // Parse filter parameters
    const campaignFilters = {
      type: url.searchParams.get('campaignType') || undefined,
      status: url.searchParams.get('campaignStatus') || undefined,
      search: url.searchParams.get('search') || undefined,
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '20'),
    };

    const discountFilters = {
      type: url.searchParams.get('discountType') || undefined,
      status: url.searchParams.get('discountStatus') || undefined,
      search: url.searchParams.get('search') || undefined,
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '20'),
    };

    // Validate filters using schema
    const campaignFilterValidation = validateCampaignFilterData(campaignFilters);
    const discountFilterValidation = validateDiscountFilterData(discountFilters);

    const validCampaignFilters = campaignFilterValidation.success ? campaignFilterValidation.data!
      : { page: 1, limit: 20 };
    const validDiscountFilters = discountFilterValidation.success ? discountFilterValidation.data!
      : { page: 1, limit: 20 };

    // Call services for business logic
    const [campaignsResult, discountsResult, analyticsResult] = await Promise.all([
      marketingService.getCampaigns(siteId, validCampaignFilters, locals.token),
      marketingService.getDiscounts(siteId, validDiscountFilters, locals.token),
      marketingService.getMarketingAnalytics(siteId, locals.token),
    ]);

    if (!campaignsResult.success) {
      console.log('Marketing Page Server: Failed to fetch campaigns:', campaignsResult.error);
    }

    if (!discountsResult.success) {
      console.log('Marketing Page Server: Failed to fetch discounts:', discountsResult.error);
    }

    return {
      siteId,
      campaigns: campaignsResult.success ? campaignsResult.data?.campaigns || [] : [],
      discounts: discountsResult.success ? discountsResult.data?.discounts || [] : [],
      analytics: analyticsResult.success ? analyticsResult.data : null,
      error: null,
    };
  }
  catch (error) {
    console.error('Marketing Page Server: Error in load function:', error);
    return {
      siteId,
      campaigns: [],
      discounts: [],
      analytics: null,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  createCampaign: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'createCampaign',
        });
      }

      const formData = await request.formData();

      const rawData = {
        siteId,
        name: formData.get('name') as string,
        type: formData.get('type') as string,
        status: formData.get('status') as string,
        description: formData.get('description') as string,
        startDate: formData.get('startDate') as string,
        endDate: formData.get('endDate') as string,
        targetAudience: formData.get('targetAudience') as string,
        budget: formData.get('budget') as string,
      };

      // Sanitize data
      const sanitizedData = sanitizeCampaignData(rawData);

      // Validate using schema
      const validationResult = validateCreateCampaignData(sanitizedData);
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'createCampaign',
        });
      }

      // Call service for business logic
      const result = await marketingService.createCampaign(
        validationResult.data!,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'createCampaign',
        });
      }

      return {
        success: true,
        campaign: result.data,
        message: 'สร้างแคมเปญสำเร็จ',
        type: 'createCampaign',
      };
    }
    catch (error) {
      console.error('Create campaign error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างแคมเปญ',
        type: 'createCampaign',
      });
    }
  },

  createDiscount: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'createDiscount',
        });
      }

      const formData = await request.formData();

      const rawData = {
        siteId,
        code: formData.get('code') as string,
        name: formData.get('name') as string,
        type: formData.get('type') as string,
        value: formData.get('value') as string,
        minOrderAmount: formData.get('minOrderAmount') as string,
        maxDiscount: formData.get('maxDiscount') as string,
        usageLimit: formData.get('usageLimit') as string,
        startDate: formData.get('startDate') as string,
        endDate: formData.get('endDate') as string,
      };

      // Sanitize data
      const sanitizedData = sanitizeDiscountData(rawData);

      // Validate using schema
      const validationResult = validateCreateDiscountData(sanitizedData);
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'createDiscount',
        });
      }

      // Call service for business logic
      const result = await marketingService.createDiscount(
        validationResult.data!,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'createDiscount',
        });
      }

      return {
        success: true,
        discount: result.data,
        message: 'สร้างส่วนลดสำเร็จ',
        type: 'createDiscount',
      };
    }
    catch (error) {
      console.error('Create discount error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างส่วนลด',
        type: 'createDiscount',
      });
    }
  },

  deleteCampaign: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'deleteCampaign',
        });
      }

      const formData = await request.formData();
      const campaignId = formData.get('campaignId') as string;

      if (!campaignId) {
        return fail(400, {
          message: 'กรุณาระบุ Campaign ID',
          type: 'deleteCampaign',
        });
      }

      // Call service for business logic
      const result = await marketingService.deleteCampaign(campaignId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteCampaign',
        });
      }

      return {
        success: true,
        message: 'ลบแคมเปญสำเร็จ',
        type: 'deleteCampaign',
      };
    }
    catch (error) {
      console.error('Delete campaign error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบแคมเปญ',
        type: 'deleteCampaign',
      });
    }
  },

  deleteDiscount: async ({ request, params, locals }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'deleteDiscount',
        });
      }

      const formData = await request.formData();
      const discountId = formData.get('discountId') as string;

      if (!discountId) {
        return fail(400, {
          message: 'กรุณาระบุ Discount ID',
          type: 'deleteDiscount',
        });
      }

      // Call service for business logic
      const result = await marketingService.deleteDiscount(discountId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteDiscount',
        });
      }

      return {
        success: true,
        message: 'ลบส่วนลดสำเร็จ',
        type: 'deleteDiscount',
      };
    }
    catch (error) {
      console.error('Delete discount error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบส่วนลด',
        type: 'deleteDiscount',
      });
    }
  },
};

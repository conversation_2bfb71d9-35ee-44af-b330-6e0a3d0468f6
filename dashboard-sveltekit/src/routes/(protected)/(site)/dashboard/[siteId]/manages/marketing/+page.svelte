<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { page } from '$app/state';
	import { Badge, Button, Card, Input, Select, Table } from '$lib/components/ui';
	import { 
		getCampaignTypeDisplayName,
		getCampaignStatusDisplayName,
		getDiscountTypeDisplayName,
		getDiscountStatusDisplayName,
		getCampaignStatusColor,
		getDiscountStatusColor,
		formatDiscountValue,
		calculateDiscountUsagePercentage,
		formatMarketingDate,
		formatMarketingCurrency,
		CAMPAIGN_TYPES,
		CAMPAIGN_STATUSES,
		DISCOUNT_TYPES,
		DISCOUNT_STATUSES
	} from '$lib/schemas/marketing.schema';
	import type { 
		Campaign, 
		Discount, 
		MarketingAnalytics,
		CampaignType,
		CampaignStatus,
		DiscountType,
		DiscountStatus 
	} from '$lib/schemas/marketing.schema';
	import Dialog from '$lib/components/ui/Dialog.svelte'; 
	import DateTime from '$lib/components/ui/DateTime.svelte';  



	// รับข้อมูลจาก server
	const { data, form } = $props<{
		data: { 
			siteId: string;
			campaigns: Campaign[]; 
			discounts: Discount[];
			analytics: MarketingAnalytics | null;
			error?: string;
		};
		form?: any;
	}>();

	const campaigns = $derived(data.campaigns || []);
	const discounts = $derived(data.discounts || []);
	const analytics = $derived(data.analytics);
	const formResult = $derived(form);

	// State management
	let isLoading = $state(false);
	let activeTab = $state('campaigns');
	let searchQuery = $state('');
	let typeFilter = $state('all');
	let statusFilter = $state('all');
	let selectedCampaign: Campaign | null = $state(null);
	let createCampaignDialogOpen = $state(false);
	let createDiscountDialogOpen = $state(false);
	let viewDialogOpen = $state(false);

	// ฟอร์มสร้างแคมเปญใหม่
	let campaignForm = $state({
		name: '',
		type: 'discount' as CampaignType,
		status: 'draft' as CampaignStatus,
		description: '',
		startDate: '',
		endDate: '',
		targetAudience: '',
		budget: 0,
	});

	// ฟอร์มสร้างส่วนลดใหม่
	let discountForm = $state({
		code: '',
		name: '',
		type: 'percentage' as DiscountType,
		value: 0,
		minOrderAmount: 0,
		maxDiscount: 0,
		usageLimit: 0,
		startDate: '',
		endDate: '',
	});

	const siteId = $derived(page.params.siteId);
	const filteredCampaigns = $derived(
		campaigns.filter(campaign => {
			const matchesSearch =
				campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				(campaign.description &&
					campaign.description.toLowerCase().includes(searchQuery.toLowerCase()));

			const matchesType = typeFilter === 'all' || campaign.type === typeFilter;

			const matchesStatus = statusFilter === 'all' || campaign.status === statusFilter;

			return matchesSearch && matchesType && matchesStatus;
		})
	);

	const filteredDiscounts = $derived(
		discounts.filter(discount => {
			const matchesSearch =
				discount.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
				discount.name.toLowerCase().includes(searchQuery.toLowerCase());

			const matchesStatus = statusFilter === 'all' || discount.status === statusFilter;

			return matchesSearch && matchesStatus;
		})
	);

	// ฟังก์ชันสำหรับแสดงไอคอนตามประเภท
	function getTypeIcon(type: string) {
		switch (type) {
			case 'discount':
				return 'lucide:percent';
			case 'coupon':
				return 'lucide:gift';
			case 'affiliate':
				return 'lucide:users';
			case 'notification':
				return 'lucide:bell';
			case 'email':
				return 'lucide:target';
			default:
				return 'lucide:megaphone';
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตามสถานะ
	function getStatusBadgeVariant(status: string) {
		switch (status) {
			case 'active':
				return 'solid';
			case 'inactive':
				return 'ghost';
			case 'scheduled':
				return 'ghost';
			case 'completed':
				return 'ghost';
			case 'expired':
				return 'ghost';
			default:
				return 'ghost';
		}
	}

	// ฟังก์ชันสำหรับแปลงประเภทเป็นภาษาไทย
	function getTypeText(type: string) {
		switch (type) {
			case 'discount':
				return 'ส่วนลด';
			case 'coupon':
				return 'คูปอง';
			case 'affiliate':
				return 'พันธมิตร';
			case 'notification':
				return 'การแจ้งเตือน';
			case 'email':
				return 'อีเมล';
			default:
				return type;
		}
	}

	// ฟังก์ชันสำหรับแปลงสถานะเป็นภาษาไทย
	function getStatusText(status: string) {
		switch (status) {
			case 'active':
				return 'ใช้งานอยู่';
			case 'inactive':
				return 'ไม่ใช้งาน';
			case 'scheduled':
				return 'กำหนดการ';
			case 'completed':
				return 'เสร็จสิ้น';
			case 'expired':
				return 'หมดอายุ';
			default:
				return status;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}

	// ฟังก์ชันสำหรับแปลงเงิน
	function formatCurrency(amount: number) {
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB',
		}).format(amount);
	}

	// คำนวณสถิติการตลาด
	const marketingStats = $derived({
		totalCampaigns: campaigns.length,
		activeCampaigns: campaigns.filter((c: Campaign) => c.status === 'active').length,
		totalDiscounts: discounts.length,
		activeDiscounts: discounts.filter((d: Discount) => d.status === 'active').length,
		totalBudget: campaigns.reduce((sum: number, c: Campaign) => sum + (c.budget || 0), 0),
		totalSpent: campaigns.reduce((sum: number, c: Campaign) => sum + (c.spent || 0), 0),
	});

	// Handle form results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			if (result.data?.type === 'createCampaign') {
				showSuccessMessage(result.data?.message || 'สร้างแคมเปญสำเร็จ');
				resetCampaignForm();
				createCampaignDialogOpen = false;
			} else if (result.data?.type === 'createDiscount') {
				showSuccessMessage(result.data?.message || 'สร้างส่วนลดสำเร็จ');
				resetDiscountForm();
				createDiscountDialogOpen = false;
			} else if (result.data?.type === 'deleteCampaign') {
				showSuccessMessage(result.data?.message || 'ลบแคมเปญสำเร็จ');
			} else if (result.data?.type === 'deleteDiscount') {
				showSuccessMessage(result.data?.message || 'ลบส่วนลดสำเร็จ');
			}
			// Refresh data
			invalidateAll();
		} else if (result.type === 'failure') {
			// Handle error
			showErrorMessage(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// Helper functions
	function showSuccessMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-success">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 3 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 3000);
	}

	function showErrorMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-error">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 5 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 5000);
	}

	// เปิดดูรายละเอียด
	function viewCampaign(campaign: Campaign) {
		selectedCampaign = campaign;
		viewDialogOpen = true;
	}

	// รีเซ็ตฟอร์ม
	function resetCampaignForm() {
		campaignForm = {
			name: '',
			type: 'discount',
			status: 'draft',
			description: '',
			startDate: '',
			endDate: '',
			targetAudience: '',
			budget: 0,
		};
	}

	function resetDiscountForm() {
		discountForm = {
			code: '',
			name: '',
			type: 'percentage',
			value: 0,
			minOrderAmount: 0,
			maxDiscount: 0,
			usageLimit: 0,
			startDate: '',
			endDate: '',
		};
	}
</script>

<svelte:head>
	<title>จัดการการตลาด - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-4 sm:space-y-4">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการการตลาด</h1>
			<p class="text-muted-foreground">จัดการแคมเปญ ส่วนลด และกิจกรรมทางการตลาด</p>
		</div>
		<div class="flex gap-2">
			<Button onclick={() => (createCampaignDialogOpen = true)}>
				<Icon icon="lucide:plus" class="h-4 w-4 mr-2" />
				สร้างแคมเปญ
			</Button>
			<Button variant="outline" onclick={() => (createDiscountDialogOpen = true)}>
				<Icon icon="lucide:percent" class="h-4 w-4 mr-2" />
				สร้างส่วนลด
			</Button>
		</div>
	</div>

	<!-- สถิติการตลาด -->
	<div class="grid gap-4 md:grid-cols-6">
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">แคมเปญทั้งหมด</div>
				<Icon icon="lucide:megaphone" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold">{marketingStats.totalCampaigns}</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">แคมเปญที่ใช้งาน</div>
				<Icon icon="lucide:megaphone" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-green-600">{marketingStats.activeCampaigns}</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ส่วนลดทั้งหมด</div>
				<Icon icon="lucide:percent" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold">{marketingStats.totalDiscounts}</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ส่วนลดที่ใช้งาน</div>
				<Icon icon="lucide:gift" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-green-600">{marketingStats.activeDiscounts}</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">งบประมาณรวม</div>
				<Icon icon="lucide:percent" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold">{formatCurrency(marketingStats.totalBudget)}</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ใช้จ่ายแล้ว</div>
				<Icon icon="lucide:percent" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold">{formatCurrency(marketingStats.totalSpent)}</div>
			</div>
		</Card>
	</div>

	<!-- แท็บ -->
	<div class="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
		<Button
			variant={activeTab === 'campaigns' ? 'solid' : 'ghost'}
			size="sm"
			onclick={() => (activeTab = 'campaigns')}
		>
			แคมเปญ
		</Button>
		<Button
			variant={activeTab === 'discounts' ? 'solid' : 'ghost'}
			size="sm"
			onclick={() => (activeTab = 'discounts')}
		>
			ส่วนลด
		</Button>
	</div>

	<!-- ตัวกรองและค้นหา -->
	<Card>
		<div class="p-4">
			<div class="flex flex-col md:flex-row gap-4">
				<div class="relative flex-1">
					<Icon
						icon="lucide:search"
						class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
					/>
					<Input
						placeholder={activeTab === 'campaigns' ? 'ค้นหาแคมเปญ...' : 'ค้นหาส่วนลด...'}
						value={searchQuery}
						class="pl-10"
					/>
				</div>
				{#if activeTab === 'campaigns'}
					<Select
						value={typeFilter}
						placeholder="ประเภท"
						class="w-full md:w-48"
						options={[
							{ value: 'all', label: 'ทั้งหมด' },
							{ value: 'discount', label: 'ส่วนลด' },
							{ value: 'coupon', label: 'คูปอง' },
							{ value: 'affiliate', label: 'พันธมิตร' },
							{ value: 'notification', label: 'การแจ้งเตือน' },
							{ value: 'email', label: 'อีเมล' },
						]}
					/>
				{/if}
				<Select
					value={statusFilter}
					placeholder="สถานะ"
					class="w-full md:w-48"
					options={activeTab === 'campaigns'
						? [
								{ value: 'all', label: 'ทั้งหมด' },
								{ value: 'active', label: 'ใช้งานอยู่' },
								{ value: 'inactive', label: 'ไม่ใช้งาน' },
								{ value: 'scheduled', label: 'กำหนดการ' },
								{ value: 'completed', label: 'เสร็จสิ้น' },
							]
						: [
								{ value: 'all', label: 'ทั้งหมด' },
								{ value: 'active', label: 'ใช้งานอยู่' },
								{ value: 'inactive', label: 'ไม่ใช้งาน' },
								{ value: 'expired', label: 'หมดอายุ' },
							]}
				/>
			</div>
		</div>
	</Card>

	{#if data.error}
		<div class="alert alert-error">
			<Icon icon="heroicons:exclamation-triangle" class="w-5 h-5" />
			<span>{data.error}</span>
		</div>
	{/if}

	{#if isLoading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else if activeTab === 'campaigns'}
		<Card>
			<div>
				<div class="flex items-center gap-2">
					<Icon icon="lucide:megaphone" class="h-5 w-5" />
					แคมเปญทั้งหมด ({filteredCampaigns.length})
				</div>
			</div>
			<div>
				{#if filteredCampaigns.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || typeFilter !== 'all' || statusFilter !== 'all'
							? 'ไม่พบแคมเปญที่ตรงกับเงื่อนไข'
							: 'ยังไม่มีแคมเปญ'}
					</div>
				{:else}
					<Table>
						<table class="table table-zebra w-full">
							<thead>
								<tr>
									<th>แคมเปญ</th>
									<th>ประเภท</th>
									<th>สถานะ</th>
									<th>วันที่เริ่ม</th>
									<th>วันที่สิ้นสุด</th>
									<th>งบประมาณ</th>
									<th>ผลลัพธ์</th>
									<th class="text-right">การจัดการ</th>
								</tr>
							</thead>
							<tbody>
								{#each filteredCampaigns as campaign (campaign._id)}
									<tr>
										<td>
											<div class="flex items-center gap-3">
												<Icon
													icon={getTypeIcon(campaign.type)}
													class="h-4 w-4 text-muted-foreground"
												/>
												<div>
													<p class="font-medium">{campaign.name}</p>
													{#if campaign.description}
														<p class="text-xs text-muted-foreground">{campaign.description}</p>
													{/if}
												</div>
											</div>
										</td>
										<td>
											<Badge variant="outline">
												{getTypeText(campaign.type)}
											</Badge>
										</td>
										<td>
											<Badge variant={getStatusBadgeVariant(campaign.status)}>
												{getStatusText(campaign.status)}
											</Badge>
										</td>
										<td class="text-sm">
											{formatDate(campaign.startDate)}
										</td>
										<td class="text-sm">
											{campaign.endDate ? formatDate(campaign.endDate) : '-'}
										</td>
										<td>
											{campaign.budget ? formatCurrency(campaign.budget) : '-'}
										</td>
										<td>
											<div class="text-xs space-y-1">
												{#if campaign.impressions}
													<p>แสดง: {campaign.impressions.toLocaleString()}</p>
												{/if}
												{#if campaign.clicks}
													<p>คลิก: {campaign.clicks.toLocaleString()}</p>
												{/if}
												{#if campaign.conversions}
													<p>แปลง: {campaign.conversions.toLocaleString()}</p>
												{/if}
											</div>
										</td>
										<td class="text-right">
											<div class="flex items-center justify-end gap-2">
												<Button variant="ghost" size="sm" onclick={() => viewCampaign(campaign)}>
													<Icon icon="lucide:eye" class="h-4 w-4" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onclick={() => {
														/* Navigate to edit */
													}}
												>
													<Icon icon="lucide:edit" class="h-4 w-4" />
												</Button>
												<form 
													method="POST" 
													action="?/deleteCampaign" 
													use:enhance={() => {
														if (!confirm('คุณแน่ใจหรือไม่ที่จะลบแคมเปญนี้?')) {
															return;
														}
														isLoading = true;
														return async ({ result }) => {
															handleFormResult(result);
														};
													}}
												>
													<input type="hidden" name="campaignId" value={campaign._id} />
													<Button
														variant="ghost"
														size="sm"
														type="submit"
														disabled={isLoading}
													>
														<Icon icon="lucide:trash-2" class="h-4 w-4" />
													</Button>
												</form>
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</Table>
				{/if}
			</div>
		</Card>
	{:else}
		<Card>
			<div>
				<div class="flex items-center gap-2">
					<Icon icon="lucide:percent" class="h-5 w-5" />
					ส่วนลดทั้งหมด ({filteredDiscounts.length})
				</div>
			</div>
			<div>
				{#if filteredDiscounts.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || statusFilter !== 'all'
							? 'ไม่พบส่วนลดที่ตรงกับเงื่อนไข'
							: 'ยังไม่มีส่วนลด'}
					</div>
				{:else}
					<Table>
						<table class="table table-zebra w-full">
							<thead>
								<tr>
									<th>รหัส</th>
									<th>ชื่อ</th>
									<th>ประเภท</th>
									<th>ค่าส่วนลด</th>
									<th>การใช้งาน</th>
									<th>สถานะ</th>
									<th>วันหมดอายุ</th>
									<th class="text-right">การจัดการ</th>
								</tr>
							</thead>
							<tbody>
								{#each filteredDiscounts as discount (discount._id)}
									<tr>
										<td>
											<span class="font-mono font-medium">{discount.code}</span>
										</td>
										<td>{discount.name}</td>
										<td>
											<Badge variant="outline">
												{discount.type === 'percentage'
													? 'เปอร์เซ็นต์'
													: discount.type === 'fixed'
														? 'จำนวนคงที่'
														: 'ค่าจัดส่ง'}
											</Badge>
										</td>
										<td>
											{discount.type === 'percentage'
												? `${discount.value}%`
												: formatCurrency(discount.value)}
										</td>
										<td>
											<div class="text-sm">
												<p>{discount.usedCount} / {discount.usageLimit || '∞'}</p>
												{#if discount.usageLimit}
													<div class="w-full bg-gray-200 rounded-full h-1 mt-1">
														<div
															class="bg-primary h-1 rounded-full"
															style="width: {Math.min(
																(discount.usedCount / discount.usageLimit) * 100,
																100
															)}%"
														></div>
													</div>
												{/if}
											</div>
										</td>
										<td>
											<Badge variant={getStatusBadgeVariant(discount.status)}>
												{getStatusText(discount.status)}
											</Badge>
										</td>
										<td class="text-sm">
											{discount.endDate ? formatDate(discount.endDate) : '-'}
										</td>
										<td class="text-right">
											<div class="flex items-center justify-end gap-2">
												<Button
													variant="ghost"
													size="sm"
													onclick={() => {
														/* Navigate to edit */
													}}
												>
													<Icon icon="lucide:edit" class="h-4 w-4" />
												</Button>
												<form 
													method="POST" 
													action="?/deleteDiscount" 
													use:enhance={() => {
														if (!confirm('คุณแน่ใจหรือไม่ที่จะลบส่วนลดนี้?')) {
															return;
														}
														isLoading = true;
														return async ({ result }) => {
															handleFormResult(result);
														};
													}}
												>
													<input type="hidden" name="discountId" value={discount._id} />
													<Button
														variant="ghost"
														size="sm"
														type="submit"
														disabled={isLoading}
													>
														<Icon icon="lucide:trash-2" class="h-4 w-4" />
													</Button>
												</form>
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</Table>
				{/if}
			</div>
		</Card>
	{/if}
</div>

<!-- Dialog สร้างแคมเปญใหม่ -->
<Dialog open={createCampaignDialogOpen} onClose={() => (createCampaignDialogOpen = false)}>
	<div class="p-6">
		<div class="mb-4">
			<h2 class="text-lg font-bold">สร้างแคมเปญใหม่</h2>
			<div class="text-sm text-muted-foreground">สร้างแคมเปญการตลาดใหม่</div>
		</div>

		<form 
			method="POST" 
			action="?/createCampaign" 
			use:enhance={() => {
				isLoading = true;
				return async ({ result }) => {
					handleFormResult(result);
				};
			}}
		>
			<div class="space-y-4">
				<div class="space-y-2">
					<label for="name" class="text-sm font-medium">ชื่อแคมเปญ</label>
					<Input name="name" placeholder="ชื่อแคมเปญ..." value={campaignForm.name} required />
				</div>

				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<label for="type" class="text-sm font-medium">ประเภท</label>
						<Select
							name="type"
							value={campaignForm.type}
							options={[
								{ value: 'discount', label: 'ส่วนลด' },
								{ value: 'coupon', label: 'คูปอง' },
								{ value: 'affiliate', label: 'พันธมิตร' },
								{ value: 'notification', label: 'การแจ้งเตือน' },
								{ value: 'email', label: 'อีเมล' },
							]}
						/>
					</div>

					<div class="space-y-2">
						<label for="status" class="text-sm font-medium">สถานะ</label>
						<Select
							name="status"
							value={campaignForm.status}
							options={[
								{ value: 'active', label: 'ใช้งานอยู่' },
								{ value: 'inactive', label: 'ไม่ใช้งาน' },
								{ value: 'scheduled', label: 'กำหนดการ' },
							]}
						/>
					</div>
				</div>

				<div class="space-y-2">
					<label for="description" class="text-sm font-medium">คำอธิบาย</label>
					<textarea
						name="description"
						class="w-full p-2 border rounded-md"
						rows="2"
						placeholder="คำอธิบายแคมเปญ..."
						value={campaignForm.description}
					></textarea>
				</div>

				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<label for="startDate" class="text-sm font-medium">วันที่เริ่ม</label>
						<DateTime name="startDate" type="date" value={campaignForm.startDate} />
					</div>

					<div class="space-y-2">
						<label for="endDate" class="text-sm font-medium">วันที่สิ้นสุด</label>
						<DateTime name="endDate" type="date" value={campaignForm.endDate} />
					</div>
				</div>

				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<label for="targetAudience" class="text-sm font-medium">กลุ่มเป้าหมาย</label>
						<Input
							name="targetAudience"
							type="text"
							placeholder="กลุ่มเป้าหมาย..."
							value={campaignForm.targetAudience}
						/>
					</div>

					<div class="space-y-2">
						<label for="budget" class="text-sm font-medium">งบประมาณ (บาท)</label>
						<Input name="budget" type="number" placeholder="0" value={campaignForm.budget} />
					</div>
				</div>

				<div class="flex gap-2">
					<Button type="submit" disabled={isLoading} class="flex-1">
						{#if isLoading}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
						{:else}
							<Icon icon="lucide:plus" class="h-4 w-4 mr-2" />
						{/if}
						สร้างแคมเปญ
					</Button>
					<Button variant="outline" onclick={() => (createCampaignDialogOpen = false)}>
						ยกเลิก
					</Button>
				</div>
			</div>
		</form>
	</div>
</Dialog>

<!-- Dialog สร้างส่วนลดใหม่ -->
<Dialog open={createDiscountDialogOpen} onClose={() => (createDiscountDialogOpen = false)}>
	<div class="p-6">
		<div class="mb-4">
			<h2 class="text-lg font-bold">สร้างส่วนลดใหม่</h2>
			<div class="text-sm text-muted-foreground">สร้างรหัสส่วนลดใหม่</div>
		</div>

		<form 
			method="POST" 
			action="?/createDiscount" 
			use:enhance={() => {
				isLoading = true;
				return async ({ result }) => {
					handleFormResult(result);
				};
			}}
		>
			<div class="space-y-4">
				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<label for="code" class="text-sm font-medium">รหัสส่วนลด</label>
						<Input name="code" placeholder="DISCOUNT10" value={discountForm.code} required />
					</div>

					<div class="space-y-2">
						<label for="name" class="text-sm font-medium">ชื่อส่วนลด</label>
						<Input name="name" placeholder="ส่วนลด 10%" value={discountForm.name} required />
					</div>
				</div>

				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<label for="type" class="text-sm font-medium">ประเภท</label>
						<Select
							name="type"
							value={discountForm.type}
							options={[
								{ value: 'percentage', label: 'เปอร์เซ็นต์' },
								{ value: 'fixed', label: 'จำนวนคงที่' },
								{ value: 'shipping', label: 'ค่าจัดส่ง' },
							]}
						/>
					</div>

					<div class="space-y-2">
						<label for="value" class="text-sm font-medium">
							ค่าส่วนลด {discountForm.type === 'percentage' ? '(%)' : '(บาท)'}
						</label>
						<Input name="value" type="number" placeholder="0" value={discountForm.value} />
					</div>
				</div>

				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<label for="minOrderAmount" class="text-sm font-medium">ยอดขั้นต่ำ (บาท)</label>
						<Input
							name="minOrderAmount"
							type="number"
							placeholder="0"
							value={discountForm.minOrderAmount}
						/>
					</div>

					<div class="space-y-2">
						<label for="maxDiscount" class="text-sm font-medium">ส่วนลดสูงสุด (บาท)</label>
						<Input
							name="maxDiscount"
							type="number"
							placeholder="0"
							value={discountForm.maxDiscount}
						/>
					</div>
				</div>

				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<label for="usageLimit" class="text-sm font-medium">จำกัดการใช้งาน</label>
						<Input
							name="usageLimit"
							type="number"
							placeholder="0 = ไม่จำกัด"
							value={discountForm.usageLimit}
						/>
					</div>

					<div class="space-y-2">
						<label for="endDate" class="text-sm font-medium">วันหมดอายุ</label>
						<Input name="endDate" type="text" value={discountForm.endDate} />
					</div>
				</div>

				<div class="flex gap-2">
					<Button type="submit" disabled={isLoading} class="flex-1">
						{#if isLoading}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
						{:else}
							<Icon icon="lucide:percent" class="h-4 w-4 mr-2" />
						{/if}
						สร้างส่วนลด
					</Button>
					<Button variant="outline" onclick={() => (createDiscountDialogOpen = false)}>
						ยกเลิก
					</Button>
				</div>
			</div>
		</form>
	</div>
</Dialog>

<!-- Dialog ดูรายละเอียดแคมเปญ -->
<Dialog open={viewDialogOpen} onClose={() => (viewDialogOpen = false)}>
	<div class="p-6 max-w-2xl">
		{#if selectedCampaign}
			<div class="mb-4">
				<h2 class="text-lg font-bold">{selectedCampaign.name}</h2>
				<div class="text-sm text-muted-foreground">
					{getTypeText(selectedCampaign.type)} • {getStatusText(selectedCampaign.status)}
				</div>
			</div>

			<div class="space-y-4">
				{#if selectedCampaign.description}
					<div>
						<h3 class="font-semibold mb-2">คำอธิบาย</h3>
						<p class="text-sm text-muted-foreground">{selectedCampaign.description}</p>
					</div>
				{/if}

				<div class="grid grid-cols-2 gap-4 text-sm">
					<div>
						<p class="font-medium">วันที่เริ่ม</p>
						<p>{formatDate(selectedCampaign.startDate)}</p>
					</div>
					{#if selectedCampaign.endDate}
						<div>
							<p class="font-medium">วันที่สิ้นสุด</p>
							<p>{formatDate(selectedCampaign.endDate)}</p>
						</div>
					{/if}
					{#if selectedCampaign.targetAudience}
						<div>
							<p class="font-medium">กลุ่มเป้าหมาย</p>
							<p>{selectedCampaign.targetAudience}</p>
						</div>
					{/if}
					{#if selectedCampaign.budget}
						<div>
							<p class="font-medium">งบประมาณ</p>
							<p>{formatCurrency(selectedCampaign.budget)}</p>
						</div>
					{/if}
				</div>

				{#if selectedCampaign.impressions || selectedCampaign.clicks || selectedCampaign.conversions}
					<div>
						<h3 class="font-semibold mb-2">ผลลัพธ์</h3>
						<div class="grid grid-cols-3 gap-4">
							{#if selectedCampaign.impressions}
								<div class="text-center p-3 bg-muted rounded-lg">
									<p class="text-2xl font-bold">{selectedCampaign.impressions.toLocaleString()}</p>
									<p class="text-sm text-muted-foreground">การแสดงผล</p>
								</div>
							{/if}
							{#if selectedCampaign.clicks}
								<div class="text-center p-3 bg-muted rounded-lg">
									<p class="text-2xl font-bold">{selectedCampaign.clicks.toLocaleString()}</p>
									<p class="text-sm text-muted-foreground">การคลิก</p>
								</div>
							{/if}
							{#if selectedCampaign.conversions}
								<div class="text-center p-3 bg-muted rounded-lg">
									<p class="text-2xl font-bold">{selectedCampaign.conversions.toLocaleString()}</p>
									<p class="text-sm text-muted-foreground">การแปลง</p>
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</Dialog>

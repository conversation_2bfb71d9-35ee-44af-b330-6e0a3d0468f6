import {
  sanitizeNotificationData,
  validateMarkAsReadData,
  validateNotificationFilterData,
} from '$lib/schemas/notification.schema';
import { notificationService } from '$lib/services/notification';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;

  try {
    if (!locals.token || !locals.user) {
      return {
        siteId,
        notifications: [],
        pagination: null,
        stats: null,
        error: 'กรุณาเข้าสู่ระบบ',
      };
    }

    // Parse filter parameters
    const rawFilters = {
      type: url.searchParams.get('type'),
      priority: url.searchParams.get('priority'),
      status: url.searchParams.get('status'),
      isRead: url.searchParams.get('isRead') === 'true' ? true
        : url.searchParams.get('isRead') === 'false' ? false : undefined,
      search: url.searchParams.get('search'),
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '20'),
    };

    // Validate filters using schema
    const filterValidation = validateNotificationFilterData(rawFilters);
    const filters = filterValidation.success ? filterValidation.data! : { page: 1, limit: 20 };

    // Call service for business logic
    const [notificationsResult, statsResult] = await Promise.all([
      notificationService.getNotifications(siteId, filters, locals.token),
      notificationService.getNotificationStats(siteId, locals.token),
    ]);

    if (!notificationsResult.success) {
      console.log('Notifications Page Server: Failed to fetch notifications:', notificationsResult.error);
      return {
        siteId,
        notifications: [],
        pagination: null,
        stats: null,
        error: notificationsResult.error,
      };
    }

    return {
      siteId,
      notifications: notificationsResult.data?.notifications || [],
      pagination: notificationsResult.data?.pagination || null,
      stats: statsResult.success ? statsResult.data : null,
      error: null,
    };
  }
  catch (error) {
    console.error('Notifications Page Server: Error in load function:', error);
    return {
      siteId,
      notifications: [],
      pagination: null,
      stats: null,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  markAsRead: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'markAsRead',
        });
      }

      const formData = await request.formData();
      const notificationIds = formData.getAll('notificationIds') as string[];

      // Validate using schema
      const validationResult = validateMarkAsReadData({ notificationIds });
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'markAsRead',
        });
      }

      // Call service for business logic
      const result = await notificationService.markAsRead(
        validationResult.data!,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'markAsRead',
        });
      }

      return {
        success: true,
        message: 'ทำเครื่องหมายว่าอ่านแล้วสำเร็จ',
        type: 'markAsRead',
      };
    }
    catch (error) {
      console.error('Mark as read error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการทำเครื่องหมายว่าอ่านแล้ว',
        type: 'markAsRead',
      });
    }
  },

  markAllAsRead: async ({ locals, params }) => {
    const { siteId } = params;

    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'markAllAsRead',
        });
      }

      // Call service for business logic
      const result = await notificationService.markAllAsRead(siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'markAllAsRead',
        });
      }

      return {
        success: true,
        message: 'ทำเครื่องหมายทั้งหมดว่าอ่านแล้วสำเร็จ',
        type: 'markAllAsRead',
      };
    }
    catch (error) {
      console.error('Mark all as read error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการทำเครื่องหมายทั้งหมดว่าอ่านแล้ว',
        type: 'markAllAsRead',
      });
    }
  },

  deleteNotification: async ({ request, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'deleteNotification',
        });
      }

      const formData = await request.formData();
      const notificationId = formData.get('notificationId') as string;

      if (!notificationId) {
        return fail(400, {
          message: 'กรุณาระบุ Notification ID',
          type: 'deleteNotification',
        });
      }

      // Call service for business logic
      const result = await notificationService.deleteNotification(notificationId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteNotification',
        });
      }

      return {
        success: true,
        message: 'ลบการแจ้งเตือนสำเร็จ',
        type: 'deleteNotification',
      };
    }
    catch (error) {
      console.error('Delete notification error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบการแจ้งเตือน',
        type: 'deleteNotification',
      });
    }
  },

  deleteMultiple: async ({ request, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'deleteMultiple',
        });
      }

      const formData = await request.formData();
      const notificationIds = formData.getAll('notificationIds') as string[];

      if (notificationIds.length === 0) {
        return fail(400, {
          message: 'กรุณาเลือกการแจ้งเตือนที่ต้องการลบ',
          type: 'deleteMultiple',
        });
      }

      // Call service for business logic
      const result = await notificationService.deleteMultipleNotifications(notificationIds, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteMultiple',
        });
      }

      return {
        success: true,
        message: `ลบการแจ้งเตือน ${notificationIds.length} รายการสำเร็จ`,
        type: 'deleteMultiple',
      };
    }
    catch (error) {
      console.error('Delete multiple notifications error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบการแจ้งเตือน',
        type: 'deleteMultiple',
      });
    }
  },

  archiveNotifications: async ({ request, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'archiveNotifications',
        });
      }

      const formData = await request.formData();
      const notificationIds = formData.getAll('notificationIds') as string[];

      if (notificationIds.length === 0) {
        return fail(400, {
          message: 'กรุณาเลือกการแจ้งเตือนที่ต้องการเก็บถาวร',
          type: 'archiveNotifications',
        });
      }

      // Call service for business logic
      const result = await notificationService.archiveNotifications(notificationIds, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'archiveNotifications',
        });
      }

      return {
        success: true,
        message: `เก็บถาวรการแจ้งเตือน ${notificationIds.length} รายการสำเร็จ`,
        type: 'archiveNotifications',
      };
    }
    catch (error) {
      console.error('Archive notifications error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเก็บถาวรการแจ้งเตือน',
        type: 'archiveNotifications',
      });
    }
  },
};

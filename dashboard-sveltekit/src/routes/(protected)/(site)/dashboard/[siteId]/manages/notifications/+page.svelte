<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import { invalidateAll } from '$app/navigation';
	import { page } from '$app/state';
	import { <PERSON><PERSON>, Badge, Button, Card, Checkbox, Pagination } from '$lib/components/ui';
	import { 
		getNotificationTypeDisplayName,
		getNotificationTypeIcon,
		getNotificationTypeColor,
		getNotificationPriorityColor,
		formatNotificationDate,
		NOTIFICATION_TYPES,
		NOTIFICATION_PRIORITIES,
		NOTIFICATION_STATUSES
	} from '$lib/schemas/notification.schema';
	import type { 
		Notification, 
		NotificationStatsData,
		NotificationType,
		NotificationPriority,
		NotificationStatus 
	} from '$lib/schemas/notification.schema';

	const { data, form } = $props<{
		data: {
			siteId: string;
			notifications: Notification[];
			pagination: any;
			stats: NotificationStatsData | null;
			error?: string;
		};
		form?: any;
	}>();

	// Reactive data
	const { siteId, notifications = [], pagination, stats, error } = $derived(data);
	const formResult = $derived(form);

	// State management
	let isLoading = $state(false);
	let selectedNotifications = $state<string[]>([]);
	
	// Filter states
	let filterType = $state<NotificationType | ''>('');
	let filterPriority = $state<NotificationPriority | ''>('');
	let filterStatus = $state<NotificationStatus | ''>('');
	let searchQuery = $state('');

	// Computed values
	const unreadNotifications = $derived(notifications.filter(n => !n.isRead));
	const readNotifications = $derived(notifications.filter(n => n.isRead));
	const allSelected = $derived(selectedNotifications.length === notifications.length && notifications.length > 0);
	const someSelected = $derived(selectedNotifications.length > 0 && selectedNotifications.length < notifications.length);

	// Handle form results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			showSuccessMessage(result.data?.message || 'ดำเนินการสำเร็จ');
			selectedNotifications = []; // Clear selection
			// Refresh data
			invalidateAll();
		} else if (result.type === 'failure') {
			// Handle error
			showErrorMessage(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// Helper functions
	function showSuccessMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-success">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 3 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 3000);
	}

	function showErrorMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-error">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 5 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 5000);
	}

	// Selection functions
	function toggleSelectAll() {
		if (allSelected) {
			selectedNotifications = [];
		} else {
			selectedNotifications = notifications.map(n => n._id!);
		}
	}

	function toggleSelectNotification(notificationId: string) {
		if (selectedNotifications.includes(notificationId)) {
			selectedNotifications = selectedNotifications.filter(id => id !== notificationId);
		} else {
			selectedNotifications = [...selectedNotifications, notificationId];
		}
	}

	// Filter functions
	function applyFilters() {
		const params = new URLSearchParams();
		
		if (filterType) params.append('type', filterType);
		if (filterPriority) params.append('priority', filterPriority);
		if (filterStatus) params.append('status', filterStatus);
		if (searchQuery) params.append('search', searchQuery);
		
		goto(`?${params.toString()}`);
	}

	function clearFilters() {
		filterType = '';
		filterPriority = '';
		filterStatus = '';
		searchQuery = '';
		goto('?');
	}


</script>

<svelte:head>
	<title>การแจ้งเตือนทั้งหมด - Dashboard</title>
</svelte:head>

<div class="space-y-4">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">การแจ้งเตือนทั้งหมด</h1>
			<p class="text-muted-foreground">ศูนย์รวมการแจ้งเตือนทุกประเภทของเว็บไซต์</p>
		</div>
		<div class="flex items-center gap-2">
			{#if selectedNotifications.length > 0}
				<form
					method="POST"
					action="?/markAsRead"
					use:enhance={() => {
						isLoading = true;
						return async ({ result }) => {
							handleFormResult(result);
						};
					}}
				>
					{#each selectedNotifications as notificationId}
						<input type="hidden" name="notificationIds" value={notificationId} />
					{/each}
					<Button type="submit" variant="outline" size="sm" disabled={isLoading}>
						<Icon icon="heroicons:eye" class="h-4 w-4 mr-2" />
						อ่านที่เลือก ({selectedNotifications.length})
					</Button>
				</form>
			{/if}
			
			{#if unreadNotifications.length > 0}
				<form
					method="POST"
					action="?/markAllAsRead"
					use:enhance={() => {
						isLoading = true;
						return async ({ result }) => {
							handleFormResult(result);
						};
					}}
				>
					<Button type="submit" variant="solid" size="sm" disabled={isLoading}>
						{#if isLoading}
							<span class="loading loading-spinner loading-sm mr-2"></span>
						{:else}
							<Icon icon="heroicons:check-circle" class="h-4 w-4 mr-2" />
						{/if}
						อ่านทั้งหมด
					</Button>
				</form>
			{/if}
		</div>
	</div>

	<!-- Error Alert -->
	{#if error}
		<Alert color="error">
			<Icon icon="heroicons:exclamation-triangle" class="w-5 h-5" />
			<span>{error}</span>
		</Alert>
	{/if}

	<!-- Filters -->
	<Card>
		<div class="p-4">
			<div class="flex flex-wrap items-center gap-4">
				<div class="flex-1 min-w-64">
					<input
						type="text"
						placeholder="ค้นหาการแจ้งเตือน..."
						bind:value={searchQuery}
						class="input input-bordered w-full"
						onkeydown={(e) => e.key === 'Enter' && applyFilters()}
					/>
				</div>
				
				<select bind:value={filterType} class="select select-bordered">
					<option value="">ทุกประเภท</option>
					{#each NOTIFICATION_TYPES as type}
						<option value={type}>{getNotificationTypeDisplayName(type)}</option>
					{/each}
				</select>
				
				<select bind:value={filterPriority} class="select select-bordered">
					<option value="">ทุกระดับ</option>
					{#each NOTIFICATION_PRIORITIES as priority}
						<option value={priority}>{priority === 'low' ? 'ต่ำ' : priority === 'medium' ? 'ปานกลาง' : priority === 'high' ? 'สูง' : 'เร่งด่วน'}</option>
					{/each}
				</select>
				
				<select bind:value={filterStatus} class="select select-bordered">
					<option value="">ทุกสถานะ</option>
					{#each NOTIFICATION_STATUSES as status}
						<option value={status}>{status === 'unread' ? 'ยังไม่อ่าน' : status === 'read' ? 'อ่านแล้ว' : 'เก็บถาวร'}</option>
					{/each}
				</select>
				
				<Button onclick={applyFilters} variant="solid" size="sm">
					<Icon icon="heroicons:magnifying-glass" class="h-4 w-4 mr-2" />
					ค้นหา
				</Button>
				
				<Button onclick={clearFilters} variant="outline" size="sm">
					<Icon icon="heroicons:x-mark" class="h-4 w-4 mr-2" />
					ล้าง
				</Button>
			</div>
		</div>
	</Card>

	<!-- Stats -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
		<Card>
			<div class="p-4">
				<div class="flex items-center">
					<div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
						<Icon icon="mdi:bell" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600 dark:text-gray-400">การแจ้งเตือนทั้งหมด</p>
						<p class="text-2xl font-bold text-gray-900 dark:text-white">
							{notifications.length}
						</p>
					</div>
				</div>
			</div>
		</Card>

		<Card>
			<div class="p-4">
				<div class="flex items-center">
					<div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
						<Icon icon="mdi:eye" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600 dark:text-gray-400">ยังไม่อ่าน</p>
						<p class="text-2xl font-bold text-gray-900 dark:text-white">
							{unreadNotifications.length}
						</p>
					</div>
				</div>
			</div>
		</Card>

		<Card>
			<div class="p-4">
				<div class="flex items-center">
					<div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
						<Icon icon="mdi:check-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600 dark:text-gray-400">อ่านแล้ว</p>
						<p class="text-2xl font-bold text-gray-900 dark:text-white">
							{readNotifications.length}
						</p>
					</div>
				</div>
			</div>
		</Card>
	</div>

	<!-- Bulk Actions -->
	{#if selectedNotifications.length > 0}
		<Card>
			<div class="p-4">
				<div class="flex items-center justify-between">
					<div class="flex items-center gap-2">
						<span class="text-sm font-medium">เลือกแล้ว {selectedNotifications.length} รายการ</span>
						<Button onclick={() => selectedNotifications = []} variant="ghost" size="sm">
							<Icon icon="heroicons:x-mark" class="h-4 w-4 mr-1" />
							ยกเลิก
						</Button>
					</div>
					
					<div class="flex items-center gap-2">
						<form
							method="POST"
							action="?/markAsRead"
							use:enhance={() => {
								isLoading = true;
								return async ({ result }) => {
									handleFormResult(result);
								};
							}}
						>
							{#each selectedNotifications as notificationId}
								<input type="hidden" name="notificationIds" value={notificationId} />
							{/each}
							<Button type="submit" variant="outline" size="sm" disabled={isLoading}>
								<Icon icon="heroicons:eye" class="h-4 w-4 mr-2" />
								อ่านที่เลือก
							</Button>
						</form>
						
						<form
							method="POST"
							action="?/archiveNotifications"
							use:enhance={() => {
								if (!confirm(`คุณแน่ใจหรือไม่ที่จะเก็บถาวร ${selectedNotifications.length} รายการ?`)) {
									return;
								}
								isLoading = true;
								return async ({ result }) => {
									handleFormResult(result);
								};
							}}
						>
							{#each selectedNotifications as notificationId}
								<input type="hidden" name="notificationIds" value={notificationId} />
							{/each}
							<Button type="submit" variant="outline" size="sm" disabled={isLoading}>
								<Icon icon="heroicons:archive-box" class="h-4 w-4 mr-2" />
								เก็บถาวร
							</Button>
						</form>
						
						<form
							method="POST"
							action="?/deleteMultiple"
							use:enhance={() => {
								if (!confirm(`คุณแน่ใจหรือไม่ที่จะลบ ${selectedNotifications.length} รายการ?`)) {
									return;
								}
								isLoading = true;
								return async ({ result }) => {
									handleFormResult(result);
								};
							}}
						>
							{#each selectedNotifications as notificationId}
								<input type="hidden" name="notificationIds" value={notificationId} />
							{/each}
							<Button type="submit" variant="outline" size="sm" disabled={isLoading}>
								<Icon icon="heroicons:trash" class="h-4 w-4 mr-2" />
								ลบที่เลือก
							</Button>
						</form>
					</div>
				</div>
			</div>
		</Card>
	{/if}

	<!-- Notifications List -->
	<div class="space-y-4">
		{#if notifications.length > 0}
			<!-- Select All Header -->
			<Card>
				<div class="p-4 border-b">
					<div class="flex items-center gap-4">
						<label class="flex items-center gap-2 cursor-pointer">
							<input
								type="checkbox"
								class="checkbox"
								checked={allSelected}
								indeterminate={someSelected}
								onchange={toggleSelectAll}
							/>
							<span class="text-sm font-medium">
								{#if allSelected}
									เลือกทั้งหมด ({notifications.length})
								{:else if someSelected}
									เลือกแล้ว {selectedNotifications.length} จาก {notifications.length}
								{:else}
									เลือกทั้งหมด
								{/if}
							</span>
						</label>
					</div>
				</div>
			</Card>
		{/if}
		
		{#if notifications.length === 0}
			<Card>
				<div class="p-8 text-center">
					<Icon icon="mdi:bell" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
					<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">ไม่มีการแจ้งเตือน</h3>
					<p class="text-gray-600 dark:text-gray-400">
						ไม่มีการแจ้งเตือนใดๆ
					</p>
				</div>
			</Card>
		{:else}
			{#each notifications as notification (notification._id)}
				<Card
					class="transition-all duration-200 hover:shadow-lg {!notification.isRead
						? 'border-l-4 border-l-blue-500 bg-blue-50 dark:bg-blue-900/20'
						: ''}"
				>
					<div class="p-6">
						<div class="flex items-start justify-between">
							<div class="flex items-start space-x-4 flex-1">
								<div class="flex-shrink-0">
									<input
										type="checkbox"
										class="checkbox"
										checked={selectedNotifications.includes(notification._id!)}
										onchange={() => toggleSelectNotification(notification._id!)}
									/>
								</div>
								<div class="flex-shrink-0">
									<div class="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
										<Icon
											icon={getNotificationTypeIcon(notification.type)}
											class="w-6 h-6 text-gray-600 dark:text-gray-400"
										/>
									</div>
								</div>

								<div class="flex-1 min-w-0">
									<div class="flex items-center space-x-2 mb-2">
										<Badge class={getNotificationTypeColor(notification.type)}>
											{getNotificationTypeDisplayName(notification.type)}
										</Badge>
										{#if notification.priority !== 'medium'}
											<Badge class={getNotificationPriorityColor(notification.priority)}>
												{notification.priority === 'low' ? 'ต่ำ' : notification.priority === 'high' ? 'สูง' : 'เร่งด่วน'}
											</Badge>
										{/if}
										{#if !notification.isRead}
											<Badge class="badge-info">ใหม่</Badge>
										{/if}
									</div>

									<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">
										{notification.title}
									</h3>

									<p class="text-gray-600 dark:text-gray-400 mb-3">
										{notification.message}
									</p>

									<div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
										<div class="flex items-center">
											<Icon icon="heroicons:clock" class="w-4 h-4 mr-1" />
											{formatNotificationDate(notification.createdAt!)}
										</div>

										{#if notification.relatedId}
											<div class="flex items-center">
												<Icon icon="heroicons:link" class="w-4 h-4 mr-1" />
												{notification.relatedType}: {notification.relatedId}
											</div>
										{/if}
									</div>
								</div>
							</div>

							<div class="flex items-center space-x-2 ml-4">
								{#if !notification.isRead}
									<form
										method="POST"
										action="?/markAsRead"
										use:enhance={() => {
											isLoading = true;
											return async ({ result }) => {
												handleFormResult(result);
											};
										}}
									>
										<input type="hidden" name="notificationIds" value={notification._id} />
										<Button type="submit" size="sm" variant="outline" disabled={isLoading}>
											<Icon icon="heroicons:eye" class="w-4 h-4 mr-1" />
											อ่าน
										</Button>
									</form>
								{/if}
								
								<form
									method="POST"
									action="?/deleteNotification"
									use:enhance={() => {
										if (!confirm('คุณแน่ใจหรือไม่ที่จะลบการแจ้งเตือนนี้?')) {
											return;
										}
										isLoading = true;
										return async ({ result }) => {
											handleFormResult(result);
										};
									}}
								>
									<input type="hidden" name="notificationId" value={notification._id} />
									<Button type="submit" size="sm" variant="ghost" disabled={isLoading}>
										<Icon icon="heroicons:trash" class="w-4 h-4" />
									</Button>
								</form>
							</div>
						</div>

						{#if notification.data}
							<div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
								<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									ข้อมูลเพิ่มเติม:
								</h4>
								<pre class="text-xs text-gray-600 dark:text-gray-400 overflow-x-auto">
                  {JSON.stringify(notification.data, null, 2)}
                </pre>
							</div>
						{/if}
					</div>
				</Card>
			{/each}
		{/if}
	</div>

	<!-- Pagination -->
	{#if pagination && pagination.totalPages > 1}
		<div class="flex justify-center">
			<Pagination
				page={pagination.page}
				totalPages={pagination.totalPages}
				onPageChange={page => goto(`?page=${page}`)}
			/>
		</div>
	{/if}
</div>

import { roleService } from '$lib/services/role';
import { getAuthToken } from '$lib/utils/auth';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, url }) => {
  // Auth check already done in layout
  const { siteId } = params;

  try {
    // ดึงข้อมูลสมาชิกทีม
    const [teamResponse, invitationsResponse] = await Promise.all([
      roleService.getRoles(siteId, locals.token!),
      roleService.getSentInvitations(siteId, locals.token!),
    ]);

    // สร้าง join URL สำหรับแชร์
    const joinUrl = `${url.origin}/dashboard/join`;

    return {
      teamMembers: teamResponse.success ? teamResponse.data?.roles : [],
      sentInvitations: invitationsResponse.success ? invitationsResponse.data?.invitations : [],
      siteId,
      joinUrl,
    };
  }
  catch (err) {
    console.error('Error loading team data:', err);
    return {
      teamMembers: [],
      sentInvitations: [],
      siteId,
      joinUrl: '',
    };
  }
};

export const actions: Actions = {
  // ส่งคำเชิญ
  invite: async ({ request, params, locals }) => {
    const token = getAuthToken(locals);
    if (!token) {
      return fail(401, { error: 'Unauthorized' });
    }

    const { siteId } = params;
    const formData = await request.formData();

    const email = formData.get('email')?.toString()?.trim();
    const role = formData.get('role')?.toString() as 'owner' | 'admin' | 'editor' | 'viewer';
    const message = formData.get('message')?.toString()?.trim();

    // Validation
    if (!email) {
      return fail(400, { error: 'กรุณากรอกอีเมล' });
    }

    if (!role) {
      return fail(400, { error: 'กรุณาเลือกบทบาท' });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return fail(400, { error: 'รูปแบบอีเมลไม่ถูกต้อง' });
    }

    try {
      const response = await roleService.createInvitation(
        siteId,
        {
          toEmail: email,
          role,
          message: message || '',
        },
        token,
      );

      if (response.success) {
        return { success: true, message: 'ส่งคำเชิญเรียบร้อยแล้ว' };
      }
      else {
        return fail(400, { error: response.error || 'ไม่สามารถส่งคำเชิญได้' });
      }
    }
    catch (err) {
      console.error('Error sending invitation:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการส่งคำเชิญ' });
    }
  },

  // สร้างลิงก์คำเชิญ
  generateLink: async ({ request, params, locals }) => {
    const token = getAuthToken(locals);
    if (!token) {
      return fail(401, { error: 'Unauthorized' });
    }

    const { siteId } = params;
    const formData = await request.formData();
    const invitationId = formData.get('invitationId')?.toString();

    if (!invitationId) {
      return fail(400, { error: 'ไม่พบ ID คำเชิญ' });
    }

    try {
      const response = await roleService.generateInvitationLink(invitationId, token);

      if (response.success) {
        return {
          success: true,
          message: 'สร้างลิงก์เรียบร้อยแล้ว',
          data: response.data,
        };
      }
      else {
        return fail(400, { error: response.error || 'ไม่สามารถสร้างลิงก์ได้' });
      }
    }
    catch (err) {
      console.error('Error generating invitation link:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการสร้างลิงก์' });
    }
  },

  // อัปเดตบทบาท
  updateRole: async ({ request, params, locals }) => {
    const token = getAuthToken(locals);
    if (!token) {
      return fail(401, { error: 'Unauthorized' });
    }

    const { siteId } = params;
    const formData = await request.formData();

    const memberId = formData.get('memberId')?.toString();
    const newRole = formData.get('role')?.toString() as 'owner' | 'admin' | 'editor' | 'viewer';

    if (!memberId || !newRole) {
      return fail(400, { error: 'กรุณาระบุข้อมูลให้ครบถ้วน' });
    }

    try {
      const response = await roleService.updateRole(siteId, memberId, { role: newRole }, token);

      if (response.success) {
        return { success: true, message: 'อัปเดตบทบาทเรียบร้อยแล้ว' };
      }
      else {
        return fail(400, { error: response.message || 'ไม่สามารถอัปเดตบทบาทได้' });
      }
    }
    catch (err) {
      console.error('Error updating role:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการอัปเดตบทบาท' });
    }
  },

  // ลบสมาชิก
  removeMember: async ({ request, params, locals }) => {
    const token = getAuthToken(locals);
    if (!token) {
      return fail(401, { error: 'Unauthorized' });
    }

    const { siteId } = params;
    const formData = await request.formData();

    const memberId = formData.get('memberId')?.toString();

    if (!memberId) {
      return fail(400, { error: 'กรุณาระบุสมาชิกที่ต้องการลบ' });
    }

    try {
      const response = await roleService.deleteRole(siteId, memberId, token);

      if (response.success) {
        return { success: true, message: 'ลบสมาชิกเรียบร้อยแล้ว' };
      }
      else {
        return fail(400, { error: response.message || 'ไม่สามารถลบสมาชิกได้' });
      }
    }
    catch (err) {
      console.error('Error removing member:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการลบสมาชิก' });
    }
  },

  // ยกเลิกคำเชิญ
  cancelInvitation: async ({ request, params, locals }) => {
    const token = getAuthToken(locals);
    if (!token) {
      return fail(401, { message: 'Unauthorized' });
    }

    const { siteId } = params;
    const formData = await request.formData();

    const invitationId = formData.get('invitationId')?.toString();

    if (!invitationId) {
      return fail(400, { message: 'กรุณาระบุคำเชิญที่ต้องการยกเลิก' });
    }

    try {
      const response = await roleService.cancelInvitation(siteId, invitationId, token);

      console.log('response', response);

      if (response.success) {
        return { success: true, message: 'ยกเลิกคำเชิญเรียบร้อยแล้ว' };
      }
      else {
        return fail(400, {
          message: response.message || 'ไม่สามารถยกเลิกคำเชิญได้',
        });
      }
    }
    catch (err) {
      console.error('Error cancelling invitation:', err);
      return fail(500, { message: 'เกิดข้อผิดพลาดในการยกเลิกคำเชิญ' });
    }
  },

  // ส่งคำเชิญใหม่
  resendInvitation: async ({ request, params, locals }) => {
    const token = getAuthToken(locals);
    if (!token) {
      return fail(401, { message: 'Unauthorized' });
    }

    const { siteId } = params;
    const formData = await request.formData();

    const invitationId = formData.get('invitationId')?.toString();

    if (!invitationId) {
      return fail(400, { message: 'กรุณาระบุคำเชิญที่ต้องการส่งใหม่' });
    }

    try {
      const response = await roleService.resendInvitation(siteId, invitationId, token);

      if (response.success) {
        return { success: true, message: 'ส่งคำเชิญใหม่เรียบร้อยแล้ว' };
      }
      else {
        return fail(400, {
          message: response.message || 'ไม่สามารถส่งคำเชิญใหม่ได้',
        });
      }
    }
    catch (err) {
      console.error('Error resending invitation:', err);
      return fail(500, { message: 'เกิดข้อผิดพลาดในการส่งคำเชิญใหม่' });
    }
  },
};

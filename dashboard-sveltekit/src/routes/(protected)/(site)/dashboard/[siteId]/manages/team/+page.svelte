<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { goto, invalidateAll } from '$app/navigation';
	import {  Badge, Input, Select, Textarea, Button, Card } from '$lib/components/ui';
	import Image from '$lib/components/ui/Image.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { LogCategory, logger } from '$lib/utils/logger';
	import { showConfirm, showErrorToast, showSuccessToast } from '$lib/utils/sweetalert';

	const { data, form } = $props<{ data: any; form: any }>();

	interface TeamMember {
		_id: string;
		userId: string;
		userName: string;
		userEmail: string;
		role: 'owner' | 'admin' | 'editor' | 'viewer';
		updatedAt: string;
		userInfo: {
			avatar: string;
			role: 'owner' | 'admin' | 'editor' | 'viewer';
			firstName: string;
			lastName: string;
			email: string;
		};
	}

	interface Invitation {
		_id: string;
		toEmail?: string;
		toUserId?: string;
		toUserName?: string;
		role: 'owner' | 'admin' | 'editor' | 'viewer';
		status: 'pending' | 'accepted' | 'rejected' | 'expired';
		message?: string;
		createdAt: string;
		expiresAt: string;
		inviteLink?: string;
	}

	const teamMembers: TeamMember[] = $state(data.teamMembers || []);
	const sentInvitations: Invitation[] = $state(data.sentInvitations || []);
	let inviteDialogOpen = $state(false);
	let isSubmitting = $state(false);
	let selectedInvitation = $state<Invitation | null>(null);
	let inviteLinkDialogOpen = $state(false);
	let inviteLink = $state('');
	let inviteCode = $state('');
	let isGeneratingLink = $state(false);

	// ฟอร์มเชิญทีมงานดูแลเว็บไซต์
	let inviteForm = $state({
		email: '',
		role: 'viewer' as 'owner' | 'admin' | 'editor' | 'viewer',
		message: '',
	});

	const siteId = $derived(data.siteId);
	const joinUrl = $derived(data.joinUrl);

	// Handle form result
	$effect(() => {
		if (form?.success) {
			showSuccessToast(form.message || 'ดำเนินการสำเร็จ');
			inviteDialogOpen = false;
			inviteForm = { email: '', role: 'viewer', message: '' };
			
			// ถ้าเป็นการสร้างลิงก์
			if (form.data?.inviteLink) {
				inviteLink = form.data.inviteLink;
				inviteCode = form.data.inviteCode;
				inviteLinkDialogOpen = true;
			}
			
			invalidateAll();
		} else if (form?.error) {
			showErrorToast(form.error);
		}
	});

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม role
	function getRoleBadgeColor(role: string) {
		switch (role) {
			case 'owner':
				return 'error';
			case 'admin':
				return 'warning';
			case 'editor':
				return 'info';
			case 'viewer':
				return 'success';
			default:
				return 'neutral';
		}
	}

	// ฟังก์ชันสำหรับแสดงไอคอนตาม role
	function getRoleIcon(role: string) {
		switch (role) {
			case 'owner':
				return 'mdi:crown';
			case 'admin':
				return 'mdi:shield-account';
			case 'editor':
				return 'mdi:pencil';
			case 'viewer':
				return 'mdi:eye';
			default:
				return 'mdi:account';
		}
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตาม status
	function getStatusBadgeColor(status: string) {
		switch (status) {
			case 'pending':
				return 'warning';
			case 'accepted':
				return 'success';
			case 'rejected':
				return 'error';
			case 'expired':
				return 'neutral';
			default:
				return 'neutral';
		}
	}

	// ฟังก์ชันสำหรับแสดงข้อความ status
	function getStatusText(status: string) {
		switch (status) {
			case 'pending':
				return 'รอการตอบกลับ';
			case 'accepted':
				return 'ยอมรับแล้ว';
			case 'rejected':
				return 'ปฏิเสธแล้ว';
			case 'expired':
				return 'หมดอายุ';
			default:
				return status;
		}
	}

	// ฟังก์ชันสำหรับแสดงไอคอนตาม status
	function getStatusIcon(status: string) {
		switch (status) {
			case 'pending':
				return 'mdi:clock-outline';
			case 'accepted':
				return 'mdi:check-circle';
			case 'rejected':
				return 'mdi:close-circle';
			case 'expired':
				return 'mdi:timer-off';
			default:
				return 'mdi:clock-outline';
		}
	}

	// สร้างลิงก์คำเชิญ
	function generateInviteLink(invitation: Invitation) {
		selectedInvitation = invitation;
		isGeneratingLink = true;
	}

	// คัดลอกลิงก์
	async function copyInviteLink() {
		try {
			await navigator.clipboard.writeText(inviteLink);
			showSuccessToast('คัดลอกลิงก์แล้ว');
		} catch (error) {
			console.error('Error copying link:', error);
			showErrorToast('ไม่สามารถคัดลอกลิงก์ได้');
		}
	}

	// คัดลอกโค้ดคำเชิญ
	async function copyInviteCodeText() {
		try {
			await navigator.clipboard.writeText(inviteCode);
			showSuccessToast('คัดลอกโค้ดแล้ว');
		} catch (error) {
			console.error('Error copying code:', error);
			showErrorToast('ไม่สามารถคัดลอกโค้ดได้');
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}

	// ฟังก์ชันสำหรับยืนยันการลบ
	async function confirmRemoveMember(memberId: string, memberName: string) {
		const result = await showConfirm(
			'ยืนยันการลบสมาชิก',
			`คุณแน่ใจหรือไม่ที่จะลบ "${memberName}" ออกจากทีม?`
		);

		if (result?.isConfirmed) {
			// Find the form and submit it
			const form = document
				.querySelector(`form[action="?/removeMember"] input[name="memberId"][value="${memberId}"]`)
				?.closest('form') as HTMLFormElement;
			if (form) {
				form.submit();
			}
		}
	}

	// ฟังก์ชันสำหรับคัดลอกโค้ดเชิญ
	async function copyInviteCode(inviteCode: string) {
		try {
			await navigator.clipboard.writeText(inviteCode);
			showSuccessToast('คัดลอกโค้ดเชิญแล้ว');
		} catch (err) {
			showErrorToast('ไม่สามารถคัดลอกได้');
		}
	}

	// ฟังก์ชันสำหรับคัดลอกลิงก์ join
	async function copyJoinLink() {
		try {
			await navigator.clipboard.writeText(joinUrl);
			showSuccessToast('คัดลอกลิงก์เข้าร่วมแล้ว');
		} catch (err) {
			showErrorToast('ไม่สามารถคัดลอกได้');
		}
	}
</script>

<svelte:head>
	<title>จัดการทีมงาน - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-4 sm:space-y-4">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">จัดการทีมงาน</h1>
			<p class="text-base-content/60">เชิญและจัดการสมาชิกในทีมของคุณ</p>
		</div>

		<button class="btn btn-primary" onclick={() => (inviteDialogOpen = true)}>
			<Icon icon="solar:add-circle-line-duotone" class="size-6" />
			สร้างคำเชิญ
		</button>
	</div>

	<!-- Stats -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
		<div class="stat bg-base-200 rounded-lg">
			<div class="stat-figure text-primary">
				<Icon icon="solar:users-group-line-duotone" class="size-8" />
			</div>
			<div class="stat-title">สมาชิกทั้งหมด</div>
			<div class="stat-value text-primary">{teamMembers.length}</div>
		</div>

		<div class="stat bg-base-200 rounded-lg">
			<div class="stat-figure text-warning">
				<Icon icon="solar:email-line-duotone" class="size-8" />
			</div>
			<div class="stat-title">คำเชิญที่ส่งไป</div>
			<div class="stat-value text-warning">{sentInvitations.length}</div>
		</div>

		<div class="stat bg-base-200 rounded-lg">
			<div class="stat-figure text-info">
				<Icon icon="solar:clock-line-duotone" class="size-8" />
			</div>
			<div class="stat-title">รอการตอบรับ</div>
			<div class="stat-value text-info">
				{sentInvitations.filter(inv => inv.status === 'pending').length}
			</div>
		</div>
	</div>

	<!-- Join Link Section -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title">
				<Icon icon="solar:link-line-duotone" class="size-5" />
				ลิงก์เข้าร่วมทีม
			</h2>
			<p class="text-base-content/60 mb-4">
				แชร์ลิงก์นี้เพื่อให้สมาชิกใหม่สามารถเข้าร่วมทีมได้โดยตรง
			</p>
			
			<div class="flex gap-2">
				<input
					type="text"
					class="input input-bordered flex-1"
					value={joinUrl}
					readonly
				/>
				<button
					class="btn btn-primary"
					onclick={copyJoinLink}
				>
					<Icon icon="mdi:content-copy" class="w-4 h-4" />
					คัดลอก
				</button>
			</div>
		</div>
	</div>

	<!-- Team Members -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title">
				<Icon icon="solar:users-group-line-duotone" class="size-5" />
				สมาชิกทีม ({teamMembers.length})
			</h2>

			{#if teamMembers.length === 0}
				<div class="text-center py-8">
					<Icon
						icon="solar:users-group-line-duotone"
						class="size-16 mx-auto text-base-content/30 mb-4"
					/>
					<p class="text-base-content/60">ยังไม่มีสมาชิกในทีม</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>สมาชิก</th>
								<th>บทบาท</th>
								<th>เข้าร่วมเมื่อ</th>
								<th>การดำเนินการ</th>
							</tr>
						</thead>
						<tbody>
							{#each teamMembers as member}
								<tr>
									<td>
										<div class="flex items-center gap-3">
											<Image
												width={50}
												height={50}
												publicId={member.userInfo?.avatar}
												alt={member.userInfo?.email || 'User Avatar'}
											/>

											<div>
												<div class="font-bold">
													{member.userInfo?.email || 'Unknown User'}
												</div>
												<div class="text-sm text-base-content/60">
													ID: {member.userId}
												</div>
											</div>
										</div>
									</td>
									<td>
										<Badge
											color={getRoleBadgeColor(member.role)}
											icon={getRoleIcon(member.role)}
											iconPosition="left"
										>
											{member.role}
										</Badge>
									</td>
									<td>{formatDate(member.updatedAt)}</td>
									<td>
										<div class="flex gap-2">
											{#if member.role !== 'owner'}
												<form
													method="POST"
													action="?/removeMember"
													use:enhance
													style="display: inline;"
												>
													<input type="hidden" name="memberId" value={member._id} />
													<button
														type="submit"
														class="btn btn-sm btn-error btn-outline"
														onclick={e => {
															e.preventDefault();
															confirmRemoveMember(
																member._id,
																member.userInfo?.email || 'Unknown User'
															);
														}}
													>
														<Icon icon="solar:trash-bin-minimalistic-bold" class="w-4 h-4" />
													</button>
												</form>
											{:else}
												-
											{/if}
										</div>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>
	</div>

	<!-- Sent Invitations -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title">
				<Icon icon="solar:email-line-duotone" class="size-5" />
				คำเชิญที่ส่งไป ({sentInvitations.length})
			</h2>

			{#if sentInvitations.length === 0}
				<div class="text-center py-8">
					<Icon icon="solar:email-line-duotone" class="size-16 mx-auto text-base-content/30 mb-4" />
					<p class="text-base-content/60">ยังไม่มีคำเชิญที่ส่งไป</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>โค้ดเชิญ</th>
								<th>อีเมล</th>
								<th>บทบาท</th>
								<th>สถานะ</th>
								<th>ส่งเมื่อ</th>
								<th>การดำเนินการ</th>
							</tr>
						</thead>
						<tbody>
							{#each sentInvitations as invitation}
								<tr>
									<td>
										<div class="flex items-center gap-2">
											<code class="text-xs bg-base-200 px-2 py-1 rounded">
												{invitation._id?.slice(-8) || '-'}
											</code>
											<button
												class="btn btn-xs btn-ghost"
												onclick={() => copyInviteCode(invitation._id)}
												title="คัดลอกโค้ดเชิญ"
											>
												<Icon icon="solar:copy-line-duotone" class="size-3" />
											</button>
										</div>
									</td>
									<td>{invitation.toEmail || '-'}</td>
									<td>
										<Badge
											color={getRoleBadgeColor(invitation.role)}
											icon={getRoleIcon(invitation.role)}
											iconPosition="left"
										>
											{invitation.role}
										</Badge>
									</td>
									<td>
										<Badge
											color={getStatusBadgeColor(invitation.status)}
											icon={getStatusIcon(invitation.status)}
											iconPosition="left"
										>
											{invitation.status}
										</Badge>
									</td>
									<td>{formatDate(invitation.createdAt)}</td>
									<td>
										<div class="flex gap-2">
											{#if invitation.status === 'pending'}
												<form
													method="POST"
													action="?/cancelInvitation"
													use:enhance={() => {
														return async ({ result }: any) => {
															console.log('result', result);
															isSubmitting = true;

															if (result.type === 'success' && result.data?.success) {
																logger.info(
																	LogCategory.AUTH,
																	'client_signin_success',
																	'Client-side signin success'
																);

																showSuccessToast('ยกเลิกคำเชิญสำเร็จ!');
															} else if (
																result.type === 'failure' ||
																result.data?.success === false
															) {
															}
															isSubmitting = false;
															showErrorToast(
																result.data?.message ||
																	result.data?.error ||
																	'เกิดข้อผิดพลาดในการยกเลิกคำเชิญ'
															);
															// Update form with result
															// await update();
														};
													}}
													style="display: inline;"
												>
													<input type="hidden" name="invitationId" value={invitation._id} />
													<button
														type="submit"
														class="btn btn-sm btn-warning btn-outline"
														title="ยกเลิกคำเชิญ"
													>
														<Icon icon="solar:close-circle-line-duotone" class="size-4" />
													</button>
												</form>
											{:else}
												<form method="POST" action="?/generateLink" use:enhance>
													<input type="hidden" name="invitationId" value={invitation._id} />
													<Button
														type="submit"
														icon="solar:link-line-duotone"
														class="btn-sm btn-info"
														disabled={isGeneratingLink}
													>
														{isGeneratingLink ? 'กำลังสร้าง...' : 'สร้างลิงก์'}
													</Button>
												</form>
												{#if inviteLinkDialogOpen && inviteLink}
													<div class="mt-2 p-3 bg-base-200 rounded-lg">
														<div class="flex items-center gap-2 mb-2">
															<input
																type="text"
																class="input input-bordered flex-1"
																value={inviteLink}
																readonly
															/>
															<Button
																icon="mdi:content-copy"
																size="sm"
																onclick={copyInviteLink}
															/>
														</div>
														<div class="text-sm text-base-content/60">
															โค้ด: <code class="text-xs bg-base-200 px-1 py-0.5 rounded">{inviteCode}</code>
															<Button
																icon="mdi:content-copy"
																size="xs"
																class="ml-2"
																onclick={copyInviteCodeText}
															/>
														</div>
													</div>
												{/if}
											{/if}
										</div>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>
	</div>
</div>

<!-- Invite Modal -->
{#if inviteDialogOpen}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">
				สร้างคำเชิญใหม่
			</h3>

			<form method="POST" action="?/invite" use:enhance>
				<div class="space-y-4"> 
						<Input icon="solar:letter-line-duotone"
							type="email"
							id="email"
							name="email"
							placeholder="<EMAIL>"
							class="input input-bordered w-full"
							bind:value={inviteForm.email}
						/> 
						
						<Select
							icon="solar:user-line-duotone"
							id="role"
							name="role"
							class="w-full"
							bind:value={inviteForm.role}
						>
							<option value="viewer">Viewer - ดูข้อมูลได้อย่างเดียว</option>
							<option value="editor">Editor - แก้ไขเนื้อหาได้</option>
							<option value="admin">Admin - จัดการเว็บไซต์ได้</option>
							<option value="owner">Owner - สิทธิ์เต็ม</option>
						</Select>
						

					
						<Textarea
							icon="solar:chat-line-line-duotone"
							id="message"
							name="message"
							placeholder="ข้อความเชิญ..."
							class="w-full"
							bind:value={inviteForm.message}
						/>
					
				</div>

				<div class="modal-action">
					<button type="button" class="btn btn-ghost" onclick={() => (inviteDialogOpen = false)}>
						ยกเลิก
					</button>
					<button type="submit" class="btn btn-primary">
						<Icon icon="mdi:send" class="w-4 h-4 mr-2" />
						ส่งคำเชิญ
					</button>
				</div>
			</form>
		</div>
		<button
			aria-label="modal-backdrop"
			class="modal-backdrop"
			onclick={() => (inviteDialogOpen = false)}
		></button>
	</div>
{/if}

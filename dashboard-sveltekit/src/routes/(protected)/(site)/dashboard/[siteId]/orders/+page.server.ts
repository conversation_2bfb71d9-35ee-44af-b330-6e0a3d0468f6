import { orderService } from '$lib/services/order';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;

  try {
    // ตรวจสอบ token
    if (!locals.token) {
      console.log('Orders Page Server: No token found');
      return {
        siteId,
        orders: [],
        orderStats: null,
        error: 'ไม่พบ token สำหรับการเข้าถึง',
      };
    }

    // ดึง query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status') || undefined;
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

    // ดึงข้อมูล orders และ statistics พร้อมกัน
    const [ordersResult, statsResult] = await Promise.all([
      orderService.getOrders(siteId, locals.token, {
        page,
        limit,
        status: status as any,
        sortBy,
        sortOrder,
      }),
      orderService.getOrderStats(siteId, locals.token),
    ]);

    // ตรวจสอบผลลัพธ์
    if (!ordersResult.success) {
      console.log('Orders Page Server: Failed to fetch orders:', ordersResult.error);
      return {
        siteId,
        orders: [],
        orderStats: null,
        error: ordersResult.error,
      };
    }

    if (!statsResult.success) {
      console.log('Orders Page Server: Failed to fetch stats:', statsResult.error);
      // ยังคงส่งข้อมูล orders แม้ stats จะล้มเหลว
      return {
        siteId,
        orders: ordersResult.data?.orders || [],
        pagination: ordersResult.data?.pagination,
        orderStats: null,
        error: `ไม่สามารถดึงสถิติได้: ${statsResult.error}`,
      };
    }

    console.log('Orders Page Server: Successfully loaded data');
    return {
      siteId,
      orders: ordersResult.data?.orders || [],
      pagination: ordersResult.data?.pagination,
      orderStats: statsResult.data,
      error: null,
    };
  }
  catch (error) {
    console.error('Orders Page Server: Error in load function:', error);
    return {
      siteId,
      orders: [],
      orderStats: null,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  updateOrderStatus: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const orderId = data.get('orderId')?.toString()?.trim();
      const status = data.get('status')?.toString()?.trim();

      // Route-level validation
      if (!orderId) {
        return fail(400, {
          message: 'กรุณาระบุ Order ID',
          type: 'updateOrderStatus',
        });
      }

      if (!status) {
        return fail(400, {
          message: 'กรุณาระบุสถานะ',
          type: 'updateOrderStatus',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'updateOrderStatus',
        });
      }

      // Call service for business logic
      const result = await orderService.updateOrder(
        orderId,
        { status: status as any },
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'updateOrderStatus',
        });
      }

      return {
        success: true,
        order: result.data,
        message: 'อัปเดตสถานะคำสั่งซื้อสำเร็จ',
        type: 'updateOrderStatus',
      };
    }
    catch (error) {
      console.error('Update order status error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตสถานะคำสั่งซื้อ',
        type: 'updateOrderStatus',
      });
    }
  },

  deleteOrder: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const orderId = data.get('orderId')?.toString()?.trim();

      // Route-level validation
      if (!orderId) {
        return fail(400, {
          message: 'กรุณาระบุ Order ID',
          type: 'deleteOrder',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'deleteOrder',
        });
      }

      // Call service for business logic
      const result = await orderService.deleteOrder(orderId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteOrder',
        });
      }

      return {
        success: true,
        message: 'ลบคำสั่งซื้อสำเร็จ',
        type: 'deleteOrder',
      };
    }
    catch (error) {
      console.error('Delete order error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบคำสั่งซื้อ',
        type: 'deleteOrder',
      });
    }
  },

  createOrder: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();

      // Extract form data
      const customerName = data.get('customerName')?.toString()?.trim();
      const customerEmail = data.get('customerEmail')?.toString()?.trim();
      const customerPhone = data.get('customerPhone')?.toString()?.trim();
      const paymentMethod = data.get('paymentMethod')?.toString()?.trim();
      const shippingAddress = data.get('shippingAddress')?.toString()?.trim();

      // Route-level validation
      if (!customerName) {
        return fail(400, {
          message: 'กรุณาระบุชื่อลูกค้า',
          type: 'createOrder',
        });
      }

      if (!customerEmail) {
        return fail(400, {
          message: 'กรุณาระบุอีเมลลูกค้า',
          type: 'createOrder',
        });
      }

      if (!paymentMethod) {
        return fail(400, {
          message: 'กรุณาเลือกวิธีการชำระเงิน',
          type: 'createOrder',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'createOrder',
        });
      }

      // Prepare order data
      // const orderData = {
      //   customerId: customerEmail, // Use customerEmail as customerId or replace with actual customerId
      //   items: [], // จะต้องเพิ่มการจัดการ items ในอนาคต
      //   shippingAddress: {
      //     street: shippingAddress || '',
      //     city: '',
      //     zipCode: '',
      //     country: '',
      //     // state: '', // Add if needed
      //   },
      //   paymentMethod,
      //   // note: '', // Add if needed
      // };

      // Call service for business logic
      const result = await orderService.createOrder(data, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'createOrder',
        });
      }

      return {
        success: true,
        order: result.data,
        message: 'สร้างคำสั่งซื้อสำเร็จ',
        type: 'createOrder',
      };
    }
    catch (error) {
      console.error('Create order error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างคำสั่งซื้อ',
        type: 'createOrder',
      });
    }
  },
};

<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Chart from '$lib/components/ui/Chart.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';

	const { data, form } = $props<{
		data: {
			user?: any;
			site?: any;
			error?: string;
			siteId: string;
			orders: any[];
			orderStats: any;
			pagination?: any;
		};
		form?: any;
	}>();

	const user = $derived(data?.user || authStore.user);
	const error = $derived(data?.error);
	const site = $derived(data?.site);
	const orders = $derived(data?.orders || []);
	const orderStats = $derived(data?.orderStats);

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Form states
	let isLoading = $state(false);
	let showCreateDialog = $state(false);
	let showUpdateDialog = $state(false);
	let selectedOrder: any = $state(null);

	// Form data
	let createForm = $state({
		customerName: '',
		customerEmail: '',
		customerPhone: '',
		paymentMethod: '',
		shippingAddress: '',
	});

	let updateForm = $state({
		orderId: '',
		status: '',
	});

	// Handle form results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			if (result.data?.type === 'createOrder') {
				showCreateDialog = false;
				resetCreateForm();
				showSuccessMessage(result.data?.message || 'สร้างคำสั่งซื้อสำเร็จ');
			} else if (result.data?.type === 'updateOrderStatus') {
				showUpdateDialog = false;
				showSuccessMessage(result.data?.message || 'อัปเดตสถานะสำเร็จ');
			} else if (result.data?.type === 'deleteOrder') {
				showSuccessMessage(result.data?.message || 'ลบคำสั่งซื้อสำเร็จ');
			}
			// Refresh data
			invalidateAll();
		} else if (result.type === 'failure') {
			// Handle error
			showErrorMessage(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// Helper functions
	function resetCreateForm() {
		createForm = {
			customerName: '',
			customerEmail: '',
			customerPhone: '',
			paymentMethod: '',
			shippingAddress: '',
		};
	}

	function openUpdateDialog(order: any) {
		selectedOrder = order;
		updateForm = {
			orderId: order._id,
			status: order.status,
		};
		showUpdateDialog = true;
	}

	function showSuccessMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-success">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 3 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 3000);
	}

	function showErrorMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-error">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 5 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 5000);
	}

	// ข้อมูลกราฟจาก server หรือใช้ค่าเริ่มต้น
	const orderData = $derived({
		revenue: orderStats?.chartData?.revenue || {
			labels: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
			datasets: [
				{
					label: 'รายได้ (บาท)',
					data: [0, 0, 0, 0, 0, 0],
					borderColor: 'rgb(59, 130, 246)',
					backgroundColor: 'rgba(59, 130, 246, 0.1)',
					tension: 0.4,
				},
			],
		},
		status: orderStats?.chartData?.status || {
			labels: ['รอดำเนินการ', 'กำลังจัดส่ง', 'จัดส่งแล้ว', 'ยกเลิก'],
			datasets: [
				{
					data: [0, 0, 0, 0],
					backgroundColor: [
						'rgb(59, 130, 246)',
						'rgb(245, 158, 11)',
						'rgb(34, 197, 94)',
						'rgb(239, 68, 68)',
					],
				},
			],
		},
	});

	const chartOptions = {
		plugins: {
			legend: {
				display: true,
				position: 'top' as const,
			},
		},
		scales: {
			y: {
				beginAtZero: true,
			},
		},
	};

	const pieOptions = {
		plugins: {
			legend: {
				display: true,
				position: 'bottom' as const,
			},
		},
	};

	// สถิติคำสั่งซื้อจาก server หรือใช้ค่าเริ่มต้น
	const statsCards = $derived([
		{
			title: 'คำสั่งซื้อทั้งหมด',
			value: orderStats?.totalOrders?.toLocaleString() || '0',
			change: orderStats?.todayChange || '+0%',
			changeType: 'positive',
			icon: 'mdi:shopping-cart',
			color: 'bg-blue-500',
		},
		{
			title: 'รายได้รวม',
			value: `฿${orderStats?.totalRevenue?.toLocaleString() || '0'}`,
			change: orderStats?.monthChange || '+0%',
			changeType: 'positive',
			icon: 'mdi:currency-usd',
			color: 'bg-green-500',
		},
		{
			title: 'รอดำเนินการ',
			value: orderStats?.pendingOrders?.toLocaleString() || '0',
			change: '+0',
			changeType: 'neutral',
			icon: 'mdi:clock-outline',
			color: 'bg-yellow-500',
		},
		{
			title: 'จัดส่งแล้ว',
			value: orderStats?.paidOrders?.toLocaleString() || '0',
			change: '+0',
			changeType: 'positive',
			icon: 'mdi:check-circle',
			color: 'bg-purple-500',
		},
	]);

	// คำสั่งซื้อล่าสุดจาก server หรือใช้ข้อมูลจาก orders
	const recentOrders = $derived(orderStats?.recentOrders || orders.slice(0, 10) || []);

	function getStatusBadge(status: string) {
		const badges = {
			pending: 'badge-warning',
			shipping: 'badge-info',
			delivered: 'badge-success',
			cancelled: 'badge-error',
		};
		return badges[status as keyof typeof badges] || 'badge-neutral';
	}

	function getStatusText(status: string) {
		const texts = {
			pending: 'รอดำเนินการ',
			shipping: 'กำลังจัดส่ง',
			delivered: 'จัดส่งแล้ว',
			cancelled: 'ยกเลิก',
		};
		return texts[status as keyof typeof texts] || status;
	}

	function getPaymentIcon(payment: string) {
		const icons = {
			credit_card: 'mdi:credit-card',
			bank_transfer: 'mdi:bank',
			cash_on_delivery: 'mdi:cash',
			paypal: 'mdi:paypal',
		};
		return icons[payment as keyof typeof icons] || 'mdi:credit-card';
	}

	function getPaymentText(payment: string) {
		const texts = {
			credit_card: 'บัตรเครดิต',
			bank_transfer: 'โอนเงิน',
			cash_on_delivery: 'เก็บเงินปลายทาง',
			paypal: 'PayPal',
		};
		return texts[payment as keyof typeof texts] || payment;
	}
</script>

<SEO
	title="Orders - จัดการคำสั่งซื้อ"
	description="จัดการคำสั่งซื้อ ดูสถิติการขาย และติดตามสถานะ"
	keywords="orders, คำสั่งซื้อ, การขาย, สถิติ, สถานะ"
	url="/dashboard/orders"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	<div class="space-y-4 sm:space-y-4">
		<!-- Error Alert -->
		{#if error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{error}</span>
			</div>
		{/if}
		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:shopping-cart" class="w-8 h-8 inline mr-2" />
					จัดการคำสั่งซื้อ
				</h1>
				<p class="text-base-content/60 mt-1">จัดการคำสั่งซื้อ ดูสถิติการขาย และติดตามสถานะ</p>
			</div>
			<div class="flex gap-2">
				<button class="btn btn-primary" onclick={() => (showCreateDialog = true)}>
					<Icon icon="mdi:plus" class="w-5 h-5" />
					สร้างคำสั่งซื้อ
				</button>
				<button class="btn btn-outline">
					<Icon icon="mdi:download" class="w-5 h-5" />
					ส่งออกข้อมูล
				</button>
			</div>
		</div>

		<!-- Stats Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{#each statsCards as stat}
				<div class="card bg-base-100 shadow-lg">
					<div class="card-body">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm text-base-content/60">{stat.title}</p>
								<p class="text-2xl font-bold text-base-content">{stat.value}</p>
								<p
									class="text-sm {stat.changeType === 'positive'
										? 'text-green-500'
										: stat.changeType === 'negative'
											? 'text-red-500'
											: 'text-gray-500'}"
								>
									{stat.change} จากเดือนที่แล้ว
								</p>
							</div>
							<div class="w-12 h-12 rounded-full {stat.color} flex items-center justify-center">
								<Icon icon={stat.icon} class="w-6 h-6 text-white" />
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Charts Row -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Revenue Chart -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:chart-line" class="w-5 h-5" />
						รายได้รายเดือน
					</h3>
					<Chart data={orderData.revenue} options={chartOptions} height="300px" />
				</div>
			</div>

			<!-- Order Status -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:pie-chart" class="w-5 h-5" />
						สถานะคำสั่งซื้อ
					</h3>
					<Chart type="doughnut" data={orderData.status} options={pieOptions} height="300px" />
				</div>
			</div>
		</div>

		<!-- Orders Table -->
		<div class="card bg-base-100 shadow-lg">
			<div class="card-body">
				<div class="flex justify-between items-center mb-4">
					<h3 class="card-title">
						<Icon icon="mdi:shopping-cart" class="w-5 h-5" />
						คำสั่งซื้อล่าสุด
					</h3>
					<button class="btn btn-sm btn-outline"> ดูทั้งหมด </button>
				</div>

				<div class="overflow-x-auto">
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>คำสั่งซื้อ</th>
								<th>ลูกค้า</th>
								<th>อีเมล</th>
								<th>จำนวนสินค้า</th>
								<th>ยอดรวม</th>
								<th>การชำระเงิน</th>
								<th>สถานะ</th>
								<th>วันที่</th>
								<th>การดำเนินการ</th>
							</tr>
						</thead>
						<tbody>
							{#if recentOrders.length > 0}
								{#each recentOrders as order}
									<tr>
										<td>
											<div class="font-bold">{order.orderNumber || order._id}</div>
										</td>
										<td>{order.customerName || 'ไม่ระบุ'}</td>
										<td>{order.customerEmail || 'ไม่ระบุ'}</td>
										<td>
											<span class="badge badge-outline">
												{order.items?.length || 0} รายการ
											</span>
										</td>
										<td>฿{order.total?.toLocaleString() || '0'}</td>
										<td>
											<div class="flex items-center gap-2">
												<Icon icon={getPaymentIcon(order.paymentMethod)} class="w-4 h-4" />
												<span class="text-sm">{getPaymentText(order.paymentMethod)}</span>
											</div>
										</td>
										<td>
											<span class="badge {getStatusBadge(order.status)}">
												{getStatusText(order.status)}
											</span>
										</td>
										<td>{new Date(order.createdAt).toLocaleDateString('th-TH')}</td>
										<td>
											<div class="flex gap-1">
												<button class="btn btn-xs btn-outline" title="ดูรายละเอียด">
													<Icon icon="mdi:eye" class="w-3 h-3" />
												</button>
												<button 
													class="btn btn-xs btn-outline" 
													title="แก้ไขสถานะ"
													onclick={() => openUpdateDialog(order)}
												>
													<Icon icon="mdi:pencil" class="w-3 h-3" />
												</button>
												<form
													method="POST"
													action="?/deleteOrder"
													use:enhance={() => {
														isLoading = true;
														return async ({ result }) => {
															handleFormResult(result);
														};
													}}
													style="display: inline;"
												>
													<input type="hidden" name="orderId" value={order._id} />
													<button 
														type="submit" 
														class="btn btn-xs btn-error btn-outline" 
														title="ลบคำสั่งซื้อ"
														disabled={isLoading}
														onclick={(e) => {
															if (!confirm('คุณแน่ใจหรือไม่ที่จะลบคำสั่งซื้อนี้?')) {
																e.preventDefault();
															}
														}}
													>
														<Icon icon="mdi:delete" class="w-3 h-3" />
													</button>
												</form>
											</div>
										</td>
									</tr>
								{/each}
							{:else}
								<tr>
									<td colspan="9" class="text-center py-8">
										<div class="flex flex-col items-center gap-2">
											<Icon icon="mdi:shopping-cart-off" class="w-12 h-12 text-base-content/30" />
											<p class="text-base-content/60">ยังไม่มีคำสั่งซื้อ</p>
										</div>
									</td>
								</tr>
							{/if}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- Create Order Dialog -->
{#if showCreateDialog}
	<div class="modal modal-open">
		<div class="modal-box w-11/12 max-w-2xl">
			<h3 class="font-bold text-lg mb-4">
				<Icon icon="mdi:plus" class="w-5 h-5 inline mr-2" />
				สร้างคำสั่งซื้อใหม่
			</h3>
			
			<form
				method="POST"
				action="?/createOrder"
				use:enhance={() => {
					isLoading = true;
					return async ({ result }) => {
						handleFormResult(result);
					};
				}}
			>
				<div class="space-y-4">
					<!-- Customer Information -->
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div class="form-control">
							<label class="label" for="order-customer-name">
								<span class="label-text">ชื่อลูกค้า *</span>
							</label>
							<input
								id="order-customer-name"
								type="text"
								name="customerName"
								bind:value={createForm.customerName}
								class="input input-bordered"
								placeholder="กรอกชื่อลูกค้า"
								required
							/>
						</div>
						
						<div class="form-control">
							<label class="label" for="order-customer-email">
								<span class="label-text">อีเมล *</span>
							</label>
							<input
								id="order-customer-email"
								type="email"
								name="customerEmail"
								bind:value={createForm.customerEmail}
								class="input input-bordered"
								placeholder="กรอกอีเมลลูกค้า"
								required
							/>
						</div>
					</div>

					<div class="form-control">
						<label class="label" for="order-customer-phone">
							<span class="label-text">เบอร์โทรศัพท์</span>
						</label>
						<input
							id="order-customer-phone"
							type="tel"
							name="customerPhone"
							bind:value={createForm.customerPhone}
							class="input input-bordered"
							placeholder="กรอกเบอร์โทรศัพท์"
						/>
					</div>

					<div class="form-control">
						<label class="label" for="order-payment-method">
							<span class="label-text">วิธีการชำระเงิน *</span>
						</label>
						<select
							id="order-payment-method"
							name="paymentMethod"
							bind:value={createForm.paymentMethod}
							class="select select-bordered"
							required
						>
							<option value="">เลือกวิธีการชำระเงิน</option>
							<option value="credit_card">บัตรเครดิต</option>
							<option value="bank_transfer">โอนเงิน</option>
							<option value="cash_on_delivery">เก็บเงินปลายทาง</option>
							<option value="paypal">PayPal</option>
						</select>
					</div>

					<div class="form-control">
						<label class="label" for="order-shipping-address">
							<span class="label-text">ที่อยู่จัดส่ง</span>
						</label>
						<textarea
							id="order-shipping-address"
							name="shippingAddress"
							bind:value={createForm.shippingAddress}
							class="textarea textarea-bordered"
							placeholder="กรอกที่อยู่จัดส่ง"
							rows="3"
						></textarea>
					</div>

					<!-- Form Error Display -->
					{#if form?.type === 'createOrder' && form?.message}
						<div class="alert alert-error">
							<Icon icon="mdi:alert-circle" class="w-5 h-5" />
							<span>{form.message}</span>
						</div>
					{/if}
				</div>

				<div class="modal-action">
					<button
						type="submit"
						class="btn btn-primary"
						disabled={isLoading}
					>
						{#if isLoading}
							<span class="loading loading-spinner loading-sm"></span>
						{:else}
							<Icon icon="mdi:check" class="w-4 h-4" />
						{/if}
						สร้างคำสั่งซื้อ
					</button>
					<button
						type="button"
						class="btn btn-ghost"
						onclick={() => {
							showCreateDialog = false;
							resetCreateForm();
						}}
						disabled={isLoading}
					>
						ยกเลิก
					</button>
				</div>
			</form>
		</div>
	</div>
{/if}

<!-- Update Order Status Dialog -->
{#if showUpdateDialog && selectedOrder}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">
				<Icon icon="mdi:pencil" class="w-5 h-5 inline mr-2" />
				อัปเดตสถานะคำสั่งซื้อ
			</h3>
			
			<div class="mb-4 p-4 bg-base-200 rounded-lg">
				<p class="text-sm text-base-content/60">คำสั่งซื้อ</p>
				<p class="font-bold">{selectedOrder.orderNumber || selectedOrder._id}</p>
				<p class="text-sm">ลูกค้า: {selectedOrder.customerName}</p>
			</div>

			<form
				method="POST"
				action="?/updateOrderStatus"
				use:enhance={() => {
					isLoading = true;
					return async ({ result }) => {
						handleFormResult(result);
					};
				}}
			>
				<input type="hidden" name="orderId" value={updateForm.orderId} />
				
				<div class="form-control mb-4">
					<label class="label" for="order-status-update">
						<span class="label-text">สถานะใหม่ *</span>
					</label>
					<select
						id="order-status-update"
						name="status"
						bind:value={updateForm.status}
						class="select select-bordered"
						required
					>
						<option value="pending">รอดำเนินการ</option>
						<option value="processing">กำลังดำเนินการ</option>
						<option value="shipping">กำลังจัดส่ง</option>
						<option value="delivered">จัดส่งแล้ว</option>
						<option value="cancelled">ยกเลิก</option>
					</select>
				</div>

				<!-- Form Error Display -->
				{#if form?.type === 'updateOrderStatus' && form?.message}
					<div class="alert alert-error mb-4">
						<Icon icon="mdi:alert-circle" class="w-5 h-5" />
						<span>{form.message}</span>
					</div>
				{/if}

				<div class="modal-action">
					<button
						type="submit"
						class="btn btn-primary"
						disabled={isLoading}
					>
						{#if isLoading}
							<span class="loading loading-spinner loading-sm"></span>
						{:else}
							<Icon icon="mdi:check" class="w-4 h-4" />
						{/if}
						อัปเดตสถานะ
					</button>
					<button
						type="button"
						class="btn btn-ghost"
						onclick={() => {
							showUpdateDialog = false;
							selectedOrder = null;
						}}
						disabled={isLoading}
					>
						ยกเลิก
					</button>
				</div>
			</form>
		</div>
	</div>
{/if}

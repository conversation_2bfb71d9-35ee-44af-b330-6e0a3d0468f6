import {
  sanitizePreorderData,
  validatePreorderActionData,
  validateUpdatePreorderData,
} from '$lib/schemas/preorder.schema';
import { preorderService } from '$lib/services/preorder';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ params, locals, url }) => {
  const { siteId } = params;

  try {
    // ตรวจสอบ token
    if (!locals.token) {
      console.log('Preorders Page Server: No token found');
      return {
        siteId,
        preOrders: [],
        preorderStats: null,
        error: 'ไม่พบ token สำหรับการเข้าถึง',
      };
    }

    // ดึง query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || undefined;
    const status = url.searchParams.get('status') || undefined;
    const productId = url.searchParams.get('productId') || undefined;
    const customerId = url.searchParams.get('customerId') || undefined;
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

    // ดึงข้อมูล preorders และ statistics พร้อมกัน
    const [preordersResult, statsResult] = await Promise.all([
      preorderService.getPreorders(siteId, locals.token, {
        page,
        limit,
        search,
        status: status as any,
        productId,
        customerId,
        sortBy,
        sortOrder,
      }),
      preorderService.getPreorderStats(siteId, locals.token),
    ]);

    // ตรวจสอบผลลัพธ์
    if (!preordersResult.success) {
      console.log('Preorders Page Server: Failed to fetch preorders:', preordersResult.error);
      return {
        siteId,
        preOrders: [],
        preorderStats: null,
        error: preordersResult.error,
      };
    }

    if (!statsResult.success) {
      console.log('Preorders Page Server: Failed to fetch stats:', statsResult.error);
      // ยังคงส่งข้อมูล preorders แม้ stats จะล้มเหลว
      return {
        siteId,
        preOrders: preordersResult.data?.preOrders || [],
        pagination: preordersResult.data?.pagination,
        preorderStats: null,
        error: `ไม่สามารถดึงสถิติได้: ${statsResult.error}`,
      };
    }

    console.log('Preorders Page Server: Successfully loaded data');
    return {
      siteId,
      preOrders: preordersResult.data?.preOrders || [],
      pagination: preordersResult.data?.pagination,
      preorderStats: statsResult.data,
      error: null,
    };
  }
  catch (error) {
    console.error('Preorders Page Server: Error in load function:', error);
    return {
      siteId,
      preOrders: [],
      preorderStats: null,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  updatePreorderStatus: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const preorderId = data.get('preorderId')?.toString()?.trim();
      const status = data.get('status')?.toString()?.trim();
      const estimatedDeliveryDate = data.get('estimatedDeliveryDate')?.toString()?.trim();
      const notes = data.get('notes')?.toString()?.trim();

      // Validate preorderId first
      const preorderIdValidation = validatePreorderActionData({ preorderId });
      if (!preorderIdValidation.success) {
        return fail(400, {
          message: preorderIdValidation.error,
          type: 'updatePreorderStatus',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'updatePreorderStatus',
        });
      }

      // Prepare update data
      const updateData: any = {};
      if (status) updateData.status = status;
      if (estimatedDeliveryDate) updateData.estimatedDeliveryDate = estimatedDeliveryDate;
      if (notes) updateData.notes = notes;

      // Validate using schema
      const sanitizedData = sanitizePreorderData(updateData);
      const validationResult = validateUpdatePreorderData(sanitizedData);

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'updatePreorderStatus',
        });
      }

      // Call service for business logic
      const result = await preorderService.updatePreorder(
        preorderId!,
        validationResult.data!,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'updatePreorderStatus',
        });
      }

      return {
        success: true,
        preorder: result.data,
        message: 'อัปเดตสถานะ Pre-order สำเร็จ',
        type: 'updatePreorderStatus',
      };
    }
    catch (error) {
      console.error('Update preorder status error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตสถานะ Pre-order',
        type: 'updatePreorderStatus',
      });
    }
  },

  cancelPreorder: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const preorderId = data.get('preorderId')?.toString()?.trim();

      // Validate using schema
      const validationResult = validatePreorderActionData({ preorderId });

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'cancelPreorder',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'cancelPreorder',
        });
      }

      // Call service for business logic
      const result = await preorderService.cancelPreorder(preorderId!, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'cancelPreorder',
        });
      }

      return {
        success: true,
        preorder: result.data,
        message: 'ยกเลิก Pre-order สำเร็จ',
        type: 'cancelPreorder',
      };
    }
    catch (error) {
      console.error('Cancel preorder error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการยกเลิก Pre-order',
        type: 'cancelPreorder',
      });
    }
  },

  deletePreorder: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const preorderId = data.get('preorderId')?.toString()?.trim();

      // Validate using schema
      const validationResult = validatePreorderActionData({ preorderId });

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'deletePreorder',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'deletePreorder',
        });
      }

      // Call service for business logic
      const result = await preorderService.deletePreorder(preorderId!, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deletePreorder',
        });
      }

      return {
        success: true,
        message: 'ลบ Pre-order สำเร็จ',
        type: 'deletePreorder',
      };
    }
    catch (error) {
      console.error('Delete preorder error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบ Pre-order',
        type: 'deletePreorder',
      });
    }
  },
};

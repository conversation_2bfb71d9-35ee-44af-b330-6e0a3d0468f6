<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import SEO from '$lib/components/layout/SEO.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';

	const { data, form } = $props<{
		data: {
			products?: {
				products: any[];
				total: number;
				page: number;
				limit: number;
				totalPages: number;
			};
			categories?: any[];
			filters?: any;
			errors?: {
				products?: string | null;
				categories?: string | null;
			};
		};
		form?: any;
	}>();

	const products = $derived(
		data?.products || {
			products: [],
			total: 0,
			page: 1,
			limit: 10,
			totalPages: 0,
		}
	);
	const categories = $derived(data?.categories || []);
	const filters = $derived(data?.filters || {});
	const errors = $derived(data?.errors || {});

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Form action result
	const formResult = $derived(form);

	// สินค้าจาก API
	const recentProducts = $derived(products?.products || []);

	function getStatusBadge(status: string) {
		const badges = {
			active: 'badge-success',
			inactive: 'badge-error',
			out_of_stock: 'badge-warning',
			draft: 'badge-neutral',
		};
		return badges[status as keyof typeof badges] || 'badge-neutral';
	}

	function getStatusText(status: string) {
		const texts = {
			active: 'เปิดขาย',
			inactive: 'ปิดขาย',
			out_of_stock: 'หมดสต็อก',
			draft: 'ร่าง',
		};
		return texts[status as keyof typeof texts] || status;
	}

	function formatPrice(price: number) {
		if (!price || isNaN(price)) {
			return new Intl.NumberFormat('th-TH', {
				style: 'currency',
				currency: 'THB',
			}).format(0);
		}
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB',
		}).format(price);
	}

	function getProductImage(product: any) {
		if (product.images && product.images.length > 0) {
			const primaryImage = product.images.find((img: any) => img.isPrimary);
			return primaryImage?.url || product.images[0].url;
		}
		return null;
	}

	function getProductInitials(name: string) {
		if (!name || typeof name !== 'string') {
			return 'UN';
		}
		return name
			.split(' ')
			.map(word => word.charAt(0))
			.join('')
			.toUpperCase()
			.substring(0, 2);
	}
</script>

<SEO
	title="Products - จัดการสินค้า"
	description="จัดการสินค้า ดูสถิติการขาย และจัดการสต็อก"
	keywords="products, สินค้า, จัดการสินค้า, สต็อก, การขาย"
	url="/dashboard/products"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	<div class="space-y-4 sm:space-y-4">
		<!-- Form Action Messages -->
		{#if formResult?.success}
			<div class="alert alert-success">
				<Icon icon="mdi:check-circle" class="w-5 h-5" />
				<span>{formResult.message}</span>
			</div>
		{:else if formResult?.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{formResult.error}</span>
			</div>
		{/if}

		<!-- API Error Messages -->
		{#if errors.products}
			<div class="alert alert-warning">
				<Icon icon="mdi:alert" class="w-5 h-5" />
				<span>ไม่สามารถโหลดข้อมูลสินค้าได้: {errors.products}</span>
			</div>
		{/if}
		{#if errors.categories}
			<div class="alert alert-warning">
				<Icon icon="mdi:alert" class="w-5 h-5" />
				<span>ไม่สามารถโหลดข้อมูลหมวดหมู่ได้: {errors.categories}</span>
			</div>
		{/if}

		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:package" class="w-8 h-8 inline mr-2" />
					จัดการสินค้า
				</h1>
				<p class="text-base-content/60 mt-1">จัดการสินค้า ดูสถิติการขาย และจัดการสต็อก</p>
			</div>
			<div class="flex gap-2">
				<a href="../analytics" class="btn btn-outline">
					<Icon icon="mdi:chart-line" class="w-5 h-5" />
					ดูสถิติ
				</a>
				<a href="./products/create" class="btn btn-primary">
					<Icon icon="mdi:plus" class="w-5 h-5" />
					เพิ่มสินค้า
				</a>
				<button class="btn btn-outline">
					<Icon icon="mdi:download" class="w-5 h-5" />
					ส่งออกข้อมูล
				</button>
			</div>
		</div>

		<!-- Filters and Search -->
		<div class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<div class="flex flex-col md:flex-row gap-4">
					<!-- Search -->
					<div class="flex-1">
						<div class="form-control">
							<div class="input-group">
								<input
									type="text"
									placeholder="ค้นหาสินค้า..."
									class="input input-bordered flex-1"
									value={filters?.search || ''}
								/>
								<button class="btn btn-square">
									<Icon icon="mdi:magnify" class="w-5 h-5" />
								</button>
							</div>
						</div>
					</div>

					<!-- Category Filter -->
					<div class="form-control">
						<select class="select select-bordered" value={filters?.categoryId || ''}>
							<option value="">ทุกหมวดหมู่</option>
							{#each categories as category}
								<option value={category._id}>{category.name}</option>
							{/each}
						</select>
					</div>

					<!-- Status Filter -->
					<div class="form-control">
						<select class="select select-bordered" value={filters?.status || ''}>
							<option value="">ทุกสถานะ</option>
							<option value="active">เปิดขาย</option>
							<option value="inactive">ปิดขาย</option>
						</select>
					</div>

					<!-- Sort -->
					<div class="form-control">
						<select class="select select-bordered" value={filters?.sortBy || 'createdAt'}>
							<option value="createdAt">วันที่สร้าง</option>
							<option value="name">ชื่อสินค้า</option>
							<option value="price">ราคา</option>
							<option value="stock">สต็อก</option>
						</select>
					</div>
				</div>
			</div>
		</div>

		<!-- Products Table -->
		<div class="card bg-base-100 shadow-lg">
			<div class="card-body">
				<div class="flex justify-between items-center mb-4">
					<h3 class="card-title">
						<Icon icon="mdi:package" class="w-5 h-5" />
						สินค้าทั้งหมด ({products?.total || 0} รายการ)
					</h3>
					<div class="flex gap-2">
						<span class="text-sm text-base-content/60">
							หน้า {products?.page || 1} จาก {products?.totalPages || 1}
						</span>
						<div class="dropdown dropdown-end">
							<button class="btn btn-sm btn-outline">
								<Icon icon="mdi:dots-vertical" class="w-4 h-4" />
							</button>
							<ul class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52">
								<li><a href="./products/create">
									<Icon icon="mdi:plus" class="w-4 h-4" />
									เพิ่มสินค้า
								</a></li>
								<li><button type="button" class="w-full text-left">
									<Icon icon="mdi:download" class="w-4 h-4" />
									ส่งออกข้อมูล
								</button></li>
								<li><button type="button" class="w-full text-left">
									<Icon icon="mdi:upload" class="w-4 h-4" />
									นำเข้าข้อมูล
								</button></li>
							</ul>
						</div>
					</div>
				</div>

				<div class="overflow-x-auto">
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>สินค้า</th>
								<th>หมวดหมู่</th>
								<th>ราคา</th>
								<th>สต็อก</th>
								<th>สถานะ</th>
								<th>ยอดขาย</th>
								<th>คะแนน</th>
								<th>การดำเนินการ</th>
							</tr>
						</thead>
						<tbody>
							{#if recentProducts && recentProducts.length > 0}
								{#each recentProducts as product}
									<tr>
										<td>
											<div class="flex items-center gap-3">
												{#if getProductImage(product)}
													<div class="avatar">
														<div class="w-10 h-10 rounded-full">
															<img src={getProductImage(product)} alt={product.name} />
														</div>
													</div>
												{:else}
													<div class="avatar placeholder">
														<div class="bg-neutral text-neutral-content rounded-full w-10">
															<span class="text-sm"
																>{getProductInitials(product.name || 'Unknown')}</span
															>
														</div>
													</div>
												{/if}
												<div>
													<div class="font-bold">
														{product.name || 'ไม่มีชื่อ'}
													</div>
													{#if product.sku}
														<div class="text-sm text-base-content/60">
															SKU: {product.sku}
														</div>
													{/if}
												</div>
											</div>
										</td>
										<td>{product.categoryId || 'ไม่มีหมวดหมู่'}</td>
										<td>{formatPrice(product.price || 0)}</td>
										<td>
											<span
												class="badge {(product.stock || 0) > 10
													? 'badge-success'
													: (product.stock || 0) > 0
														? 'badge-warning'
														: 'badge-error'}"
											>
												{product.stock || 0}
											</span>
										</td>
										<td>
											<span
												class="badge {getStatusBadge(product.isActive ? 'active' : 'inactive')}"
											>
												{getStatusText(product.isActive ? 'active' : 'inactive')}
											</span>
										</td>
										<td>-</td>
										<td>-</td>
										<td>
											<div class="dropdown dropdown-end">
												<button class="btn btn-xs btn-outline">
													<Icon icon="mdi:dots-vertical" class="w-3 h-3" />
												</button>
												<ul class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-48">
													<li><a href="./products/{product._id}">
														<Icon icon="mdi:eye" class="w-4 h-4" />
														ดูรายละเอียด
													</a></li>
													<li><a href="./products/{product._id}">
														<Icon icon="mdi:pencil" class="w-4 h-4" />
														แก้ไข
													</a></li>
													{#if product.hasVariants}
														<li><a href="./products/{product._id}/variants">
															<Icon icon="mdi:tune-variant" class="w-4 h-4" />
															จัดการตัวเลือก
														</a></li>
													{/if}
													<li><hr /></li>
													<li>
														<form method="POST" action="?/updateStock" class="w-full">
															<input type="hidden" name="productId" value={product._id} />
															<input type="hidden" name="stock" value="0" />
															<button type="submit" class="w-full text-left">
																<Icon icon="mdi:package-down" class="w-4 h-4" />
																ตั้งสต็อกเป็น 0
															</button>
														</form>
													</li>
													<li><hr /></li>
													<li>
														<form method="POST" action="?/deleteProduct" class="w-full">
															<input type="hidden" name="productId" value={product._id} />
															<button
																type="submit"
																class="w-full text-left text-error"
																onclick={e => {
																	if (!confirm('คุณแน่ใจหรือไม่ที่จะลบสินค้านี้?')) {
																		e.preventDefault();
																	}
																}}
															>
																<Icon icon="mdi:delete" class="w-4 h-4" />
																ลบสินค้า
															</button>
														</form>
													</li>
												</ul>
											</div>
										</td>
									</tr>
								{/each}
							{:else}
								<tr>
									<td colspan="8" class="text-center py-8">
										<div class="flex flex-col items-center gap-2">
											<Icon
												icon="mdi:package-variant-closed"
												class="w-12 h-12 text-base-content/40"
											/>
											<p class="text-base-content/60">ยังไม่มีสินค้า</p>
											<a href="./products/create" class="btn btn-sm btn-primary">
												<Icon icon="mdi:plus" class="w-4 h-4" />
												เพิ่มสินค้าแรก
											</a>
										</div>
									</td>
								</tr>
							{/if}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				{#if products && products.totalPages > 1}
					<div class="flex justify-center mt-6">
						<div class="join">
							{#if products.page > 1}
								<a href="?page={products.page - 1}" class="join-item btn btn-outline">
									<Icon icon="mdi:chevron-left" class="w-4 h-4" />
								</a>
							{/if}

							{#each Array.from({ length: products.totalPages || 1 }, (_, i) => i + 1) as pageNum}
								{#if pageNum >= (products.page || 1) - 2 && pageNum <= (products.page || 1) + 2}
									<a
										href="?page={pageNum}"
										class="join-item btn {pageNum === (products.page || 1)
											? 'btn-primary'
											: 'btn-outline'}"
									>
										{pageNum}
									</a>
								{/if}
							{/each}

							{#if (products.page || 1) < (products.totalPages || 1)}
								<a href="?page={(products.page || 1) + 1}" class="join-item btn btn-outline">
									<Icon icon="mdi:chevron-right" class="w-4 h-4" />
								</a>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}

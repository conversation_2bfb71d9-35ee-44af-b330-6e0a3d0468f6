import { categoryService } from '$lib/services/category';
import { productService } from '$lib/services/product';
import { error, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  try {
    if (!locals.token || !locals.user) {
      throw redirect(302, '/signin');
    }

    const siteId = params.siteId;
    if (!siteId) {
      throw error(400, 'Site ID is required');
    }

    // Fetch categories for dropdown
    const categoriesResponse = await categoryService.getCategories(siteId, locals.token!);

    if (!categoriesResponse.success) {
      throw error(500, categoriesResponse.error || 'Failed to fetch categories');
    }

    // Mock option sets data - in real app, fetch from API
    const mockOptionSets = [
      {
        id: '1',
        name: 'ขนาดเสื้อผ้า',
        type: 'single',
        options: [
          { value: 'xs', label: 'XS' },
          { value: 's', label: 'S' },
          { value: 'm', label: 'M' },
          { value: 'l', label: 'L' },
          { value: 'xl', label: 'XL' },
          { value: 'xxl', label: 'XXL' },
        ],
      },
      {
        id: '2',
        name: 'สีสินค้า',
        type: 'color',
        options: [
          { value: 'red', label: 'แดง', color: '#FF0000' },
          { value: 'blue', label: 'น้ำเงิน', color: '#0000FF' },
          { value: 'green', label: 'เขียว', color: '#00FF00' },
          { value: 'black', label: 'ดำ', color: '#000000' },
          { value: 'white', label: 'ขาว', color: '#FFFFFF' },
        ],
      },
      {
        id: '3',
        name: 'วัสดุ',
        type: 'single',
        options: [
          { value: 'cotton', label: 'ผ้าฝ้าย' },
          { value: 'polyester', label: 'โพลีเอสเตอร์' },
          { value: 'silk', label: 'ผ้าไหม' },
          { value: 'wool', label: 'ผ้าขนสัตว์' },
        ],
      },
    ];

    return {
      categories: categoriesResponse.data || [],
      optionSets: mockOptionSets,
    };
  }
  catch (err) {
    console.error('Error loading create product page:', err);
    if (err instanceof Response) {
      throw err;
    }
    throw error(500, 'เกิดข้อผิดพลาดในการโหลดข้อมูล');
  }
};

export const actions: Actions = {
  default: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return { success: false, error: 'กรุณาเข้าสู่ระบบ' };
      }

      const siteId = params.siteId;
      if (!siteId) {
        return { success: false, error: 'Site ID is required' };
      }

      const formData = await request.formData();

      // Parse form data
      const productData = {
        name: formData.get('name') as string,
        type: (formData.get('type') as 'physical' | 'digital' | 'service' | 'subscription') || 'physical',
        saleChannel: (formData.get('saleChannel') as 'online' | 'offline' | 'both') || 'online',
        description: formData.get('description') as string,
        shortDescription: formData.get('shortDescription') as string,
        price: parseFloat(formData.get('price') as string) || 0,
        compareAtPrice: parseFloat(formData.get('compareAtPrice') as string) || undefined,
        costPrice: parseFloat(formData.get('costPrice') as string) || undefined,
        stock: parseInt(formData.get('stock') as string) || 0,
        trackStock: formData.get('trackStock') === 'on',
        allowBackorder: formData.get('allowBackorder') === 'on',
        categoryId: formData.get('categoryId') as string,
        tags: (formData.get('tags') as string)?.split(',').filter(Boolean) || [],
        featured: formData.get('featured') === 'on',
        isActive: formData.get('isActive') === 'on',
        allowPreOrder: formData.get('allowPreOrder') === 'on',
        seoTitle: formData.get('seoTitle') as string,
        seoDescription: formData.get('seoDescription') as string,

        // Parse JSON fields
        images: JSON.parse(formData.get('images') as string || '[]'),
        hasVariants: formData.get('hasVariants') === 'on',
        selectedOptionSets: JSON.parse(formData.get('selectedOptionSets') as string || '[]'),
        variants: JSON.parse(formData.get('variants') as string || '[]'),
        digitalAssets: JSON.parse(formData.get('digitalAssets') as string || '[]'),

        shipping: {
          weight: parseFloat(formData.get('shipping.weight') as string) || undefined,
          dimensions: {
            length: parseFloat(formData.get('shipping.dimensions.length') as string) || 0,
            width: parseFloat(formData.get('shipping.dimensions.width') as string) || 0,
            height: parseFloat(formData.get('shipping.dimensions.height') as string) || 0,
          },
          shippingClass: formData.get('shipping.shippingClass') as string,
          requiresShipping: formData.get('shipping.requiresShipping') !== 'off',
        },
      };

      console.log('Product data:', productData);

      // Validation
      if (!productData.name) {
        return { success: false, error: 'กรุณากรอกชื่อสินค้า' };
      }

      if (productData.price <= 0) {
        return { success: false, error: 'กรุณากรอกราคาสินค้า' };
      }

      const response = await productService.createProduct(productData, siteId, locals.token!);

      if (response.success) {
        return { success: true, message: 'สร้างสินค้าเรียบร้อย' };
      }
      else {
        return {
          success: false,
          error: response.error || 'เกิดข้อผิดพลาดในการสร้างสินค้า',
        };
      }
    }
    catch (err) {
      console.error('Error creating product:', err);
      if (err instanceof Response) {
        throw err;
      }
      return { success: false, error: 'เกิดข้อผิดพลาดในการสร้างสินค้า' };
    }
  },
};

import { productService } from '$lib/services/product';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, setHeaders }) => {
  try {
    if (!locals.token || !locals.user) {
      throw error(401, 'กรุณาเข้าสู่ระบบ');
    }

    const siteId = params.siteId;
    if (!siteId) {
      throw error(400, 'Site ID is required');
    }

    // Fetch product statistics
    const statsResponse = await productService.getProductStats(siteId, locals.token!);

    // Set cache headers
    setHeaders({
      'Cache-Control': 'public, max-age=300', // 5 minutes
    });

    return {
      stats: statsResponse.success ? statsResponse.data : null,
      error: !statsResponse.success ? statsResponse.error : null,
    };
  }
  catch (err) {
    console.error('Error loading product stats page:', err);
    if (err instanceof Response) {
      throw err;
    }
    throw error(500, 'เกิดข้อผิดพลาดในการโหลดสถิติสินค้า');
  }
};

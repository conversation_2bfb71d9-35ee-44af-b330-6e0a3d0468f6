import {
  sanitizeReviewData,
  validateReviewActionData,
  validateReviewVisibilityData,
  validateUpdateReviewData,
} from '$lib/schemas/review.schema';
import { reviewService } from '$lib/services/review';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;

  try {
    // ตรวจสอบ token
    if (!locals.token) {
      console.log('Reviews Page Server: No token found');
      return {
        siteId,
        reviews: [],
        reviewStats: null,
        error: 'ไม่พบ token สำหรับการเข้าถึง',
      };
    }

    // ดึง query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || undefined;
    const productId = url.searchParams.get('productId') || undefined;
    const customerId = url.searchParams.get('customerId') || undefined;
    const rating = url.searchParams.get('rating') ? parseInt(url.searchParams.get('rating')!) : undefined;
    const isApproved = url.searchParams.get('isApproved') === 'true' ? true
      : url.searchParams.get('isApproved') === 'false' ? false : undefined;
    const isVisible = url.searchParams.get('isVisible') === 'true' ? true
      : url.searchParams.get('isVisible') === 'false' ? false : undefined;
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

    // ดึงข้อมูล reviews และ statistics พร้อมกัน
    const [reviewsResult, statsResult] = await Promise.all([
      reviewService.getReviews(siteId, locals.token, {
        page,
        limit,
        search,
        productId,
        customerId,
        rating,
        isApproved,
        isVisible,
        sortBy,
        sortOrder,
      }),
      reviewService.getReviewStats(siteId, locals.token),
    ]);

    // ตรวจสอบผลลัพธ์
    if (!reviewsResult.success) {
      console.log('Reviews Page Server: Failed to fetch reviews:', reviewsResult.error);
      return {
        siteId,
        reviews: [],
        reviewStats: null,
        error: reviewsResult.error,
      };
    }

    if (!statsResult.success) {
      console.log('Reviews Page Server: Failed to fetch stats:', statsResult.error);
      // ยังคงส่งข้อมูล reviews แม้ stats จะล้มเหลว
      return {
        siteId,
        reviews: reviewsResult.data?.reviews || [],
        pagination: reviewsResult.data?.pagination,
        reviewStats: null,
        error: `ไม่สามารถดึงสถิติได้: ${statsResult.error}`,
      };
    }

    console.log('Reviews Page Server: Successfully loaded data');
    return {
      siteId,
      reviews: reviewsResult.data?.reviews || [],
      pagination: reviewsResult.data?.pagination,
      reviewStats: statsResult.data,
      error: null,
    };
  }
  catch (error) {
    console.error('Reviews Page Server: Error in load function:', error);
    return {
      siteId,
      reviews: [],
      reviewStats: null,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  approveReview: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const reviewId = data.get('reviewId')?.toString()?.trim();

      // Validate using schema
      const validationResult = validateReviewActionData({ reviewId });

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'approveReview',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'approveReview',
        });
      }

      // Call service for business logic
      const result = await reviewService.approveReview(reviewId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'approveReview',
        });
      }

      return {
        success: true,
        review: result.data,
        message: 'อนุมัติรีวิวสำเร็จ',
        type: 'approveReview',
      };
    }
    catch (error) {
      console.error('Approve review error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอนุมัติรีวิว',
        type: 'approveReview',
      });
    }
  },

  rejectReview: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const reviewId = data.get('reviewId')?.toString()?.trim();

      // Validate using schema
      const validationResult = validateReviewActionData({ reviewId });

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'rejectReview',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'rejectReview',
        });
      }

      // Call service for business logic
      const result = await reviewService.rejectReview(reviewId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'rejectReview',
        });
      }

      return {
        success: true,
        review: result.data,
        message: 'ปฏิเสธรีวิวสำเร็จ',
        type: 'rejectReview',
      };
    }
    catch (error) {
      console.error('Reject review error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการปฏิเสธรีวิว',
        type: 'rejectReview',
      });
    }
  },

  toggleReviewVisibility: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const reviewId = data.get('reviewId')?.toString()?.trim();
      const isVisible = data.get('isVisible') === 'true';

      // Validate using schema
      const validationResult = validateReviewVisibilityData({ reviewId, isVisible });

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'toggleReviewVisibility',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'toggleReviewVisibility',
        });
      }

      // Call service for business logic
      const result = await reviewService.toggleReviewVisibility(reviewId, isVisible, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'toggleReviewVisibility',
        });
      }

      return {
        success: true,
        review: result.data,
        message: `${isVisible ? 'แสดง' : 'ซ่อน'}รีวิวสำเร็จ`,
        type: 'toggleReviewVisibility',
      };
    }
    catch (error) {
      console.error('Toggle review visibility error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปลี่ยนการแสดงรีวิว',
        type: 'toggleReviewVisibility',
      });
    }
  },

  deleteReview: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const reviewId = data.get('reviewId')?.toString()?.trim();

      // Validate using schema
      const validationResult = validateReviewActionData({ reviewId });

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'deleteReview',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'deleteReview',
        });
      }

      // Call service for business logic
      const result = await reviewService.deleteReview(reviewId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteReview',
        });
      }

      return {
        success: true,
        message: 'ลบรีวิวสำเร็จ',
        type: 'deleteReview',
      };
    }
    catch (error) {
      console.error('Delete review error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบรีวิว',
        type: 'deleteReview',
      });
    }
  },

  updateReview: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const reviewId = data.get('reviewId')?.toString()?.trim();
      const rating = data.get('rating') ? parseInt(data.get('rating')!.toString()) : undefined;
      const title = data.get('title')?.toString()?.trim();
      const comment = data.get('comment')?.toString()?.trim();
      const isApproved = data.get('isApproved') === 'true';
      const isVisible = data.get('isVisible') === 'true';

      // Validate reviewId first
      const reviewIdValidation = validateReviewActionData({ reviewId });
      if (!reviewIdValidation.success) {
        return fail(400, {
          message: reviewIdValidation.error,
          type: 'updateReview',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'updateReview',
        });
      }

      // Prepare update data
      const updateData: any = {};
      if (rating) updateData.rating = rating;
      if (title) updateData.title = title;
      if (comment) updateData.comment = comment;
      updateData.isApproved = isApproved;
      updateData.isVisible = isVisible;

      // Validate using schema
      const sanitizedData = sanitizeReviewData(updateData);
      const validationResult = validateUpdateReviewData(sanitizedData);

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'updateReview',
        });
      }

      // Call service for business logic
      const result = await reviewService.updateReview(reviewId, validationResult.data!, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'updateReview',
        });
      }

      return {
        success: true,
        review: result.data,
        message: 'อัปเดตรีวิวสำเร็จ',
        type: 'updateReview',
      };
    }
    catch (error) {
      console.error('Update review error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตรีวิว',
        type: 'updateReview',
      });
    }
  },
};

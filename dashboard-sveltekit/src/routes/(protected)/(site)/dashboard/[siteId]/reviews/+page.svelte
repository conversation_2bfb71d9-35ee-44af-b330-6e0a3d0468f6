<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { Badge, Button, Card, Dialog, Input, Select, Table, Textarea } from '$lib/components/ui';

	interface Review {
		_id: string;
		productId: string;
		productName: string;
		customerId: string;
		customerName: string;
		customerEmail: string;
		rating: number;
		title?: string;
		comment: string;
		isApproved: boolean;
		isVisible: boolean;
		images?: string[];
		createdAt: string;
		updatedAt: string;
	}

	const { data, form } = $props<{
		data: {
			siteId: string;
			reviews: Review[];
			reviewStats?: any;
			pagination?: any;
			error?: string;
		};
		form?: any;
	}>();

	const reviews = $derived(data?.reviews || []);
	const reviewStats = $derived(data?.reviewStats);
	const error = $derived(data?.error);

	// Form states
	let isLoading = $state(false);
	let searchQuery = $state('');
	let statusFilter = $state('all');
	let ratingFilter = $state('all');
	let selectedReview: Review | null = $state(null);
	let viewDialogOpen = $state(false);

	const filteredReviews = $derived(
		reviews.filter(review => {
			const matchesSearch =
				review.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
				review.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
				review.comment.toLowerCase().includes(searchQuery.toLowerCase()) ||
				(review.title && review.title.toLowerCase().includes(searchQuery.toLowerCase()));

			const matchesStatus =
				statusFilter === 'all' ||
				(statusFilter === 'approved' && review.isApproved) ||
				(statusFilter === 'pending' && !review.isApproved) ||
				(statusFilter === 'visible' && review.isVisible) ||
				(statusFilter === 'hidden' && !review.isVisible);

			const matchesRating = ratingFilter === 'all' || review.rating.toString() === ratingFilter;

			return matchesSearch && matchesStatus && matchesRating;
		})
	);

	// Handle form results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			if (result.data?.type === 'approveReview') {
				showSuccessMessage(result.data?.message || 'อนุมัติรีวิวสำเร็จ');
			} else if (result.data?.type === 'rejectReview') {
				showSuccessMessage(result.data?.message || 'ปฏิเสธรีวิวสำเร็จ');
			} else if (result.data?.type === 'toggleReviewVisibility') {
				showSuccessMessage(result.data?.message || 'เปลี่ยนการแสดงรีวิวสำเร็จ');
			} else if (result.data?.type === 'deleteReview') {
				showSuccessMessage(result.data?.message || 'ลบรีวิวสำเร็จ');
			}
			// Refresh data
			invalidateAll();
		} else if (result.type === 'failure') {
			// Handle error
			showErrorMessage(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// Helper functions
	function showSuccessMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-success">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 3 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 3000);
	}

	function showErrorMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-error">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 5 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 5000);
	}



	// เปิดดูรายละเอียดรีวิว
	function viewReview(review: Review) {
		selectedReview = review;
		viewDialogOpen = true;
	}

	// สร้างดาวสำหรับแสดงคะแนน
	function renderStars(rating: number) {
		return Array.from({ length: 5 }, (_, i) => i < rating);
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	}

	// คำนวณสถิติรีวิว (ใช้ข้อมูลจาก server หรือคำนวณจาก reviews)
	const computedReviewStats = $derived({
		total: reviews.length,
		approved: reviews.filter(r => r.isApproved).length,
		pending: reviews.filter(r => !r.isApproved).length,
		averageRating:
			reviews.length > 0
				? (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1)
				: '0.0',
	});

	// ใช้ stats จาก server หรือคำนวณเอง
	const finalReviewStats = $derived(reviewStats || computedReviewStats);
</script>

<svelte:head>
	<title>จัดการรีวิวสินค้า - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-4 sm:space-y-4">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการรีวิวสินค้า</h1>
			<p class="text-muted-foreground">จัดการและตรวจสอบรีวิวจากลูกค้า</p>
		</div>
	</div>

	<!-- Error Alert -->
	{#if error}
		<div class="alert alert-error">
			<Icon icon="mdi:alert-circle" class="w-5 h-5" />
			<span>{error}</span>
		</div>
	{/if}

	<!-- สถิติรีวิว -->
	<div class="grid gap-4 md:grid-cols-4">
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">รีวิวทั้งหมด</div>
				<Icon icon="heroicons:chat-bubble-left-right" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">{finalReviewStats.total}</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">อนุมัติแล้ว</div>
				<Icon icon="heroicons:check-circle" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold text-green-600">{finalReviewStats.approved}</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">รอการอนุมัติ</div>
				<Icon icon="heroicons:clock" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold text-orange-600">{finalReviewStats.pending}</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">คะแนนเฉลี่ย</div>
				<Icon icon="heroicons:star" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div class="text-2xl font-bold">{finalReviewStats.averageRating}</div>
		</Card>
	</div>

	<!-- ตัวกรองและค้นหา -->
	<Card>
		<div class="p-4">
			<div class="flex flex-col md:flex-row gap-4">
				<div class="relative flex-1">
					<Icon icon="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
					<input 
						type="text" 
						placeholder="ค้นหารีวิว..." 
						bind:value={searchQuery} 
						class="input input-bordered w-full pl-10" 
					/>
				</div>
				<Select
					bind:value={statusFilter}
					placeholder="สถานะ"
					className="w-full md:w-48"
					options={[
						{ value: 'all', label: 'ทั้งหมด' },
						{ value: 'approved', label: 'อนุมัติแล้ว' },
						{ value: 'pending', label: 'รอการอนุมัติ' },
						{ value: 'visible', label: 'แสดงอยู่' },
						{ value: 'hidden', label: 'ซ่อนอยู่' },
					]}
				/>
				<Select
					bind:value={ratingFilter}
					placeholder="คะแนน"
					className="w-full md:w-32"
					options={[
						{ value: 'all', label: 'ทั้งหมด' },
						{ value: '5', label: '5 ดาว' },
						{ value: '4', label: '4 ดาว' },
						{ value: '3', label: '3 ดาว' },
						{ value: '2', label: '2 ดาว' },
						{ value: '1', label: '1 ดาว' },
					]}
				/>
			</div>
		</div>
	</Card>


		<Card>
			<div class="p-4">
				{#if filteredReviews.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || statusFilter !== 'all' || ratingFilter !== 'all'
							? 'ไม่พบรีวิวที่ตรงกับเงื่อนไข'
							: 'ยังไม่มีรีวิว'}
					</div>
				{:else}
					<Table>
						<table class="table table-zebra w-full">
							<thead>
								<tr>
									<th>สินค้า</th>
									<th>ลูกค้า</th>
									<th>คะแนน</th>
									<th>รีวิว</th>
									<th>สถานะ</th>
									<th>วันที่</th>
									<th class="text-right">การจัดการ</th>
								</tr>
							</thead>
							<tbody>
								{#each filteredReviews as review (review._id)}
									<tr>
										<td>
											<div class="flex items-center gap-2">
												<Icon icon="heroicons:cube" class="h-4 w-4 text-muted-foreground" />
												<span class="font-medium">{review.productName}</span>
											</div>
										</td>
										<td>
											<div class="flex items-center gap-2">
												<Icon icon="heroicons:user" class="h-4 w-4 text-muted-foreground" />
												<div>
													<p class="font-medium">{review.customerName}</p>
													<p class="text-xs text-muted-foreground">{review.customerEmail}</p>
												</div>
											</div>
										</td>
										<td>
											<div class="flex items-center gap-1">
												{#each renderStars(review.rating) as filled}
													<Icon
														icon="heroicons:star"
														class="h-4 w-4 {filled
															? 'text-yellow-400'
															: 'text-gray-300'}"
													/>
												{/each}
												<span class="ml-1 text-sm text-muted-foreground">
													({review.rating})
												</span>
											</div>
										</td>
										<td>
											<div class="max-w-xs">
												{#if review.title}
													<p class="font-medium text-sm truncate">{review.title}</p>
												{/if}
												<p class="text-sm text-muted-foreground truncate">
													{review.comment}
												</p>
											</div>
										</td>
										<td>
											<div class="flex flex-col gap-1">
												<Badge variant={review.isApproved ? 'default' : 'secondary'}>
													{review.isApproved ? 'อนุมัติแล้ว' : 'รอการอนุมัติ'}
												</Badge>
												<Badge variant={review.isVisible ? 'outline' : 'destructive'}>
													{review.isVisible ? 'แสดงอยู่' : 'ซ่อนอยู่'}
												</Badge>
											</div>
										</td>
										<td class="text-sm text-muted-foreground">
											{formatDate(review.createdAt)}
										</td>
										<td class="text-right">
											<div class="flex items-center justify-end gap-2">
												<button 
													class="btn btn-xs btn-outline" 
													title="ดูรายละเอียด"
													onclick={() => viewReview(review)}
												>
													<Icon icon="heroicons:eye" class="w-3 h-3" />
												</button>
												
												{#if !review.isApproved}
													<form
														method="POST"
														action="?/approveReview"
														use:enhance={() => {
															isLoading = true;
															return async ({ result }) => {
																handleFormResult(result);
															};
														}}
														style="display: inline;"
													>
														<input type="hidden" name="reviewId" value={review._id} />
														<button 
															type="submit" 
															class="btn btn-xs btn-success btn-outline" 
															title="อนุมัติรีวิว"
															disabled={isLoading}
														>
															<Icon icon="heroicons:check" class="w-3 h-3" />
														</button>
													</form>
												{:else}
													<form
														method="POST"
														action="?/rejectReview"
														use:enhance={() => {
															isLoading = true;
															return async ({ result }) => {
																handleFormResult(result);
															};
														}}
														style="display: inline;"
													>
														<input type="hidden" name="reviewId" value={review._id} />
														<button 
															type="submit" 
															class="btn btn-xs btn-warning btn-outline" 
															title="ปฏิเสธรีวิว"
															disabled={isLoading}
														>
															<Icon icon="heroicons:x-mark" class="w-3 h-3" />
														</button>
													</form>
												{/if}

												<form
													method="POST"
													action="?/toggleReviewVisibility"
													use:enhance={() => {
														isLoading = true;
														return async ({ result }) => {
															handleFormResult(result);
														};
													}}
													style="display: inline;"
												>
													<input type="hidden" name="reviewId" value={review._id} />
													<input type="hidden" name="isVisible" value={(!review.isVisible).toString()} />
													<button 
														type="submit" 
														class="btn btn-xs btn-outline" 
														title={review.isVisible ? 'ซ่อนรีวิว' : 'แสดงรีวิว'}
														disabled={isLoading}
													>
														{#if review.isVisible}
															<Icon icon="heroicons:eye-slash" class="w-3 h-3" />
														{:else}
															<Icon icon="heroicons:eye" class="w-3 h-3" />
														{/if}
													</button>
												</form>

												<form
													method="POST"
													action="?/deleteReview"
													use:enhance={() => {
														isLoading = true;
														return async ({ result }) => {
															handleFormResult(result);
														};
													}}
													style="display: inline;"
												>
													<input type="hidden" name="reviewId" value={review._id} />
													<button 
														type="submit" 
														class="btn btn-xs btn-error btn-outline" 
														title="ลบรีวิว"
														disabled={isLoading}
														onclick={(e) => {
															if (!confirm('คุณแน่ใจหรือไม่ที่จะลบรีวิวนี้?')) {
																e.preventDefault();
															}
														}}
													>
														<Icon icon="heroicons:trash" class="w-3 h-3" />
													</button>
												</form>
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</Table>
				{/if}
			</div>
		</Card>
</div>

<!-- Dialog ดูรายละเอียดรีวิว -->
<Dialog open={viewDialogOpen} on:close={() => (viewDialogOpen = false)} class="max-w-2xl">
	<div class="p-6">
		<div class="mb-4">
			<h2 class="text-lg font-bold">รายละเอียดรีวิว</h2>
			<div class="text-sm text-muted-foreground">{selectedReview?.title}</div>
		</div>
		{#if selectedReview}
			<div class="space-y-4">
				<div class="grid grid-cols-2 gap-4">
					<div>
						<p class="text-sm font-medium">ลูกค้า</p>
						<p class="text-sm text-muted-foreground">{selectedReview.customerName}</p>
						<p class="text-xs text-muted-foreground">{selectedReview.customerEmail}</p>
					</div>
					<div>
						<p class="text-sm font-medium">คะแนน</p>
						<div class="flex items-center gap-1">
							{#each renderStars(selectedReview.rating) as filled}
								<Icon
									icon="heroicons:star"
									class="h-4 w-4 {filled ? 'text-yellow-400' : 'text-gray-300'}"
								/>
							{/each}
							<span class="ml-1 text-sm">({selectedReview.rating}/5)</span>
						</div>
					</div>
				</div>

				{#if selectedReview.title}
					<div>
						<p class="text-sm font-medium">หัวข้อ</p>
						<p class="text-sm">{selectedReview.title}</p>
					</div>
				{/if}

				<div>
					<p class="text-sm font-medium">ความคิดเห็น</p>
					<p class="text-sm whitespace-pre-wrap">{selectedReview.comment}</p>
				</div>

				{#if selectedReview.images && selectedReview.images.length > 0}
					<div>
						<p class="text-sm font-medium mb-2">รูปภาพ</p>
						<div class="grid grid-cols-3 gap-2">
							{#each selectedReview.images as image}
								<img
									src={image}
									alt="รีวิวรูปภาพ"
									class="w-full h-20 object-cover rounded border"
								/>
							{/each}
						</div>
					</div>
				{/if}

				<div class="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
					<div>
						<p>วันที่สร้าง: {formatDate(selectedReview.createdAt)}</p>
					</div>
					<div>
						<p>อัปเดตล่าสุด: {formatDate(selectedReview.updatedAt)}</p>
					</div>
				</div>

				<div class="flex gap-2">
					{#if !selectedReview.isApproved}
						<form
							method="POST"
							action="?/approveReview"
							use:enhance={() => {
								isLoading = true;
								viewDialogOpen = false;
								return async ({ result }) => {
									handleFormResult(result);
								};
							}}
							class="flex-1"
						>
							<input type="hidden" name="reviewId" value={selectedReview._id} />
							<button
								type="submit"
								class="btn btn-primary w-full"
								disabled={isLoading}
							>
								{#if isLoading}
									<span class="loading loading-spinner loading-sm"></span>
								{:else}
									<Icon icon="heroicons:check" class="w-4 h-4" />
								{/if}
								อนุมัติรีวิว
							</button>
						</form>
					{:else}
						<form
							method="POST"
							action="?/rejectReview"
							use:enhance={() => {
								isLoading = true;
								viewDialogOpen = false;
								return async ({ result }) => {
									handleFormResult(result);
								};
							}}
							class="flex-1"
						>
							<input type="hidden" name="reviewId" value={selectedReview._id} />
							<button
								type="submit"
								class="btn btn-outline w-full"
								disabled={isLoading}
							>
								{#if isLoading}
									<span class="loading loading-spinner loading-sm"></span>
								{:else}
									<Icon icon="heroicons:x-mark" class="w-4 h-4" />
								{/if}
								ยกเลิกการอนุมัติ
							</button>
						</form>
					{/if}
					
					<form
						method="POST"
						action="?/toggleReviewVisibility"
						use:enhance={() => {
							isLoading = true;
							viewDialogOpen = false;
							return async ({ result }) => {
								handleFormResult(result);
							};
						}}
						class="flex-1"
					>
						<input type="hidden" name="reviewId" value={selectedReview._id} />
						<input type="hidden" name="isVisible" value={(!selectedReview.isVisible).toString()} />
						<button
							type="submit"
							class="btn btn-outline w-full"
							disabled={isLoading}
						>
							{#if isLoading}
								<span class="loading loading-spinner loading-sm"></span>
							{:else if selectedReview.isVisible}
								<Icon icon="heroicons:eye-slash" class="w-4 h-4" />
							{:else}
								<Icon icon="heroicons:eye" class="w-4 h-4" />
							{/if}
							{selectedReview.isVisible ? 'ซ่อนรีวิว' : 'แสดงรีวิว'}
						</button>
					</form>
				</div>
			</div>
		{/if}
	</div>
</Dialog>

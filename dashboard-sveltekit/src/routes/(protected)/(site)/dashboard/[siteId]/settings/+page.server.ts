import {
  parseKeywordsFromString,
  sanitizePaymentGatewayData,
  sanitizeSiteSettingsData,
  validateCreatePaymentGatewayData,
  validateUpdateSiteSettingsData,
} from '$lib/schemas/settings.schema';
import { settingsService } from '$lib/services/settings';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, params }) => {
  const { siteId } = params;

  try {
    // ตรวจสอบ token
    if (!locals.token) {
      console.log('Settings Page Server: No token found');
      return {
        siteId,
        settings: null,
        paymentGateways: [],
        error: 'ไม่พบ token สำหรับการเข้าถึง',
      };
    }

    // ดึงข้อมูล settings และ payment gateways พร้อมกัน
    const [settingsResult, gatewaysResult] = await Promise.all([
      settingsService.getSiteSettings(siteId, locals.token),
      settingsService.getPaymentGateways(siteId, locals.token),
    ]);

    // ตรวจสอบผลลัพธ์
    if (!settingsResult.success) {
      console.log('Settings Page Server: Failed to fetch settings:', settingsResult.error);
      return {
        siteId,
        settings: null,
        paymentGateways: [],
        error: settingsResult.error,
      };
    }

    if (!gatewaysResult.success) {
      console.log('Settings Page Server: Failed to fetch gateways:', gatewaysResult.error);
      // ยังคงส่งข้อมูล settings แม้ gateways จะล้มเหลว
      return {
        siteId,
        settings: settingsResult.data,
        paymentGateways: [],
        error: `ไม่สามารถดึง Payment Gateways ได้: ${gatewaysResult.error}`,
      };
    }

    console.log('Settings Page Server: Successfully loaded data');
    return {
      siteId,
      settings: settingsResult.data,
      paymentGateways: gatewaysResult.data?.gateways || [],
      error: null,
    };
  }
  catch (error) {
    console.error('Settings Page Server: Error in load function:', error);
    return {
      siteId,
      settings: null,
      paymentGateways: [],
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  updateSettings: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();

      // Extract form data
      const siteName = data.get('siteName')?.toString()?.trim();
      const siteDescription = data.get('siteDescription')?.toString()?.trim();
      const siteUrl = data.get('siteUrl')?.toString()?.trim();
      const primaryColor = data.get('primaryColor')?.toString()?.trim();
      const secondaryColor = data.get('secondaryColor')?.toString()?.trim();
      const fontFamily = data.get('fontFamily')?.toString()?.trim();
      const metaTitle = data.get('metaTitle')?.toString()?.trim();
      const metaDescription = data.get('metaDescription')?.toString()?.trim();
      const keywords = data.get('keywords')?.toString()?.trim();
      const email = data.get('email')?.toString()?.trim();
      const phone = data.get('phone')?.toString()?.trim();
      const address = data.get('address')?.toString()?.trim();
      const facebook = data.get('facebook')?.toString()?.trim();
      const instagram = data.get('instagram')?.toString()?.trim();
      const twitter = data.get('twitter')?.toString()?.trim();
      const line = data.get('line')?.toString()?.trim();
      const enableCart = data.get('enableCart') === 'true';
      const enableReviews = data.get('enableReviews') === 'true';
      const enableWishlist = data.get('enableWishlist') === 'true';
      const enableChat = data.get('enableChat') === 'true';
      const emailNotifications = data.get('emailNotifications') === 'true';
      const smsNotifications = data.get('smsNotifications') === 'true';
      const pushNotifications = data.get('pushNotifications') === 'true';
      const twoFactorAuth = data.get('twoFactorAuth') === 'true';
      const passwordPolicy = data.get('passwordPolicy')?.toString()?.trim();
      const sessionTimeout = parseInt(data.get('sessionTimeout')?.toString() || '30');

      // Route-level validation
      if (!siteName) {
        return fail(400, {
          message: 'กรุณากรอกชื่อเว็บไซต์',
          type: 'updateSettings',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'updateSettings',
        });
      }

      // Prepare settings data
      const settingsData = {
        siteName,
        siteDescription: siteDescription || '',
        siteUrl: siteUrl || '',
        theme: {
          primaryColor: primaryColor || '#3b82f6',
          secondaryColor: secondaryColor || '#64748b',
          fontFamily: fontFamily || 'Inter',
        },
        seo: {
          metaTitle: metaTitle || '',
          metaDescription: metaDescription || '',
          keywords: keywords ? parseKeywordsFromString(keywords) : [],
        },
        contact: {
          email: email || '',
          phone: phone || '',
          address: address || '',
        },
        social: {
          facebook: facebook || '',
          instagram: instagram || '',
          twitter: twitter || '',
          line: line || '',
        },
        features: {
          enableCart,
          enableReviews,
          enableWishlist,
          enableChat,
        },
        notifications: {
          emailNotifications,
          smsNotifications,
          pushNotifications,
        },
        security: {
          twoFactorAuth,
          passwordPolicy: passwordPolicy || 'medium',
          sessionTimeout,
        },
      };

      // Validate using schema
      const sanitizedData = sanitizeSiteSettingsData(settingsData);
      const validationResult = validateUpdateSiteSettingsData(sanitizedData);

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'updateSettings',
        });
      }

      // Call service for business logic
      const result = await settingsService.updateSiteSettings(
        validationResult.data!,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'updateSettings',
        });
      }

      return {
        success: true,
        settings: result.data,
        message: 'บันทึกการตั้งค่าสำเร็จ',
        type: 'updateSettings',
      };
    }
    catch (error) {
      console.error('Update settings error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการบันทึกการตั้งค่า',
        type: 'updateSettings',
      });
    }
  },

  createPaymentGateway: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();

      // Extract form data
      const name = data.get('name')?.toString()?.trim();
      const provider = data.get('provider')?.toString()?.trim();
      const publicKey = data.get('publicKey')?.toString()?.trim();
      const secretKey = data.get('secretKey')?.toString()?.trim();
      const webhookUrl = data.get('webhookUrl')?.toString()?.trim();
      const percentage = parseFloat(data.get('percentage')?.toString() || '0');
      const fixed = parseFloat(data.get('fixed')?.toString() || '0');

      // Route-level validation
      if (!name) {
        return fail(400, {
          message: 'กรุณากรอกชื่อ Payment Gateway',
          type: 'createPaymentGateway',
        });
      }

      if (!provider) {
        return fail(400, {
          message: 'กรุณาเลือก Provider',
          type: 'createPaymentGateway',
        });
      }

      if (!secretKey) {
        return fail(400, {
          message: 'กรุณากรอก Secret Key',
          type: 'createPaymentGateway',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'createPaymentGateway',
        });
      }

      // Prepare gateway data
      const gatewayData = {
        name,
        provider: provider as any,
        config: {
          publicKey: publicKey || '',
          secretKey,
          webhookUrl: webhookUrl || '',
        },
        fees: {
          percentage,
          fixed,
        },
      };

      // Validate using schema
      const sanitizedData = sanitizePaymentGatewayData(gatewayData);
      const validationResult = validateCreatePaymentGatewayData(sanitizedData);

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'createPaymentGateway',
        });
      }

      // Call service for business logic
      const result = await settingsService.createPaymentGateway(
        validationResult.data!,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'createPaymentGateway',
        });
      }

      return {
        success: true,
        paymentGateway: result.data,
        message: 'เพิ่ม Payment Gateway สำเร็จ',
        type: 'createPaymentGateway',
      };
    }
    catch (error) {
      console.error('Create payment gateway error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเพิ่ม Payment Gateway',
        type: 'createPaymentGateway',
      });
    }
  },

  deletePaymentGateway: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const gatewayId = data.get('gatewayId')?.toString()?.trim();

      // Route-level validation
      if (!gatewayId) {
        return fail(400, {
          message: 'กรุณาระบุ Gateway ID',
          type: 'deletePaymentGateway',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'deletePaymentGateway',
        });
      }

      // Call service for business logic
      const result = await settingsService.deletePaymentGateway(gatewayId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deletePaymentGateway',
        });
      }

      return {
        success: true,
        message: 'ลบ Payment Gateway สำเร็จ',
        type: 'deletePaymentGateway',
      };
    }
    catch (error) {
      console.error('Delete payment gateway error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบ Payment Gateway',
        type: 'deletePaymentGateway',
      });
    }
  },
};

<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { Badge, Button, Card, Input, Select, Switch, Textarea } from '$lib/components/ui';
	import { formatKeywordsForDisplay } from '$lib/schemas/settings.schema';

	// Import types from service
	import type { SiteSettings, PaymentGateway } from '$lib/services/settings';

	const { data } = $props<{
		data: {
			siteId: string;
			settings: SiteSettings | null;
			paymentGateways: PaymentGateway[];
			error?: string;
		};
	}>();

	const settings = $derived(data?.settings);
	const paymentGateways = $derived(data?.paymentGateways || []);

	// Form states
	let isLoading = $state(false);
	let activeTab = $state('general');
	let showSecretKeys = $state(false);

	// ฟอร์มการตั้งค่า
	let settingsForm = $state({
		siteName: '',
		siteDescription: '',
		siteUrl: '',
		primaryColor: '#3b82f6',
		secondaryColor: '#64748b',
		fontFamily: 'Inter',
		metaTitle: '',
		metaDescription: '',
		keywords: '',
		email: '',
		phone: '',
		address: '',
		facebook: '',
		instagram: '',
		twitter: '',
		line: '',
		enableCart: true,
		enableReviews: true,
		enableWishlist: true,
		enableChat: false,
		emailNotifications: true,
		smsNotifications: false,
		pushNotifications: true,
		twoFactorAuth: false,
		passwordPolicy: 'medium',
		sessionTimeout: 30,
	});

	// ฟอร์ม Payment Gateway
	let paymentForm = $state({
		name: '',
		provider: 'stripe' as PaymentGateway['provider'],
		publicKey: '',
		secretKey: '',
		webhookUrl: '',
		percentage: 0,
		fixed: 0,
	});

	// Initialize form with server data
	$effect(() => {
		if (settings) {
			settingsForm = {
				siteName: settings.siteName,
				siteDescription: settings.siteDescription || '',
				siteUrl: settings.siteUrl || '',
				primaryColor: settings.theme.primaryColor,
				secondaryColor: settings.theme.secondaryColor,
				fontFamily: settings.theme.fontFamily,
				metaTitle: settings.seo.metaTitle || '',
				metaDescription: settings.seo.metaDescription || '',
				keywords: formatKeywordsForDisplay(settings.seo.keywords || []),
				email: settings.contact.email || '',
				phone: settings.contact.phone || '',
				address: settings.contact.address || '',
				facebook: settings.social.facebook || '',
				instagram: settings.social.instagram || '',
				twitter: settings.social.twitter || '',
				line: settings.social.line || '',
				enableCart: settings.features.enableCart,
				enableReviews: settings.features.enableReviews,
				enableWishlist: settings.features.enableWishlist,
				enableChat: settings.features.enableChat,
				emailNotifications: settings.notifications.emailNotifications,
				smsNotifications: settings.notifications.smsNotifications,
				pushNotifications: settings.notifications.pushNotifications,
				twoFactorAuth: settings.security.twoFactorAuth,
				passwordPolicy: settings.security.passwordPolicy,
				sessionTimeout: settings.security.sessionTimeout,
			};
			
			// Apply theme immediately when settings are loaded
			applyThemeToDOM();
		}
	});

	// Watch for theme changes and apply immediately
	$effect(() => {
		// Apply theme when colors change
		if (settingsForm.primaryColor && settingsForm.secondaryColor && settingsForm.fontFamily) {
			applyThemeToDOM();
		}
	});

	// Handle form results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			if (result.data?.type === 'updateSettings') {
				// อัปเดตข้อมูลทันทีจาก response
				if (result.data?.settings) {
					// อัปเดต settings จาก response
					data.settings = result.data.settings;
				}
				showSuccessMessage(result.data?.message || 'บันทึกการตั้งค่าสำเร็จ');
				
				// Apply theme changes immediately to DOM
				applyThemeToDOM();
			} else if (result.data?.type === 'createPaymentGateway') {
				resetPaymentForm();
				showSuccessMessage(result.data?.message || 'เพิ่ม Payment Gateway สำเร็จ');
			} else if (result.data?.type === 'deletePaymentGateway') {
				showSuccessMessage(result.data?.message || 'ลบ Payment Gateway สำเร็จ');
			}
			// Refresh data
			invalidateAll();
		} else if (result.type === 'failure') {
			// Handle error
			showErrorMessage(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// Apply theme changes to DOM immediately
	function applyThemeToDOM() {
		if (typeof document !== 'undefined' && settingsForm.primaryColor && settingsForm.secondaryColor) {
			const root = document.documentElement;
			
			// Convert hex to HSL for DaisyUI
			const primaryHSL = hexToHSL(settingsForm.primaryColor);
			const secondaryHSL = hexToHSL(settingsForm.secondaryColor);
			
			// Update DaisyUI CSS custom properties
			root.style.setProperty('--p', primaryHSL);
			root.style.setProperty('--pf', primaryHSL);
			root.style.setProperty('--pc', getContrastColor(settingsForm.primaryColor));
			root.style.setProperty('--s', secondaryHSL);
			root.style.setProperty('--sf', secondaryHSL);
			root.style.setProperty('--sc', getContrastColor(settingsForm.secondaryColor));
			
			// Update general CSS custom properties
			root.style.setProperty('--primary-color', settingsForm.primaryColor);
			root.style.setProperty('--secondary-color', settingsForm.secondaryColor);
			root.style.setProperty('--font-family', settingsForm.fontFamily);
			
			// Update body font family
			document.body.style.fontFamily = settingsForm.fontFamily;
			
			console.log('Theme applied:', {
				primary: settingsForm.primaryColor,
				secondary: settingsForm.secondaryColor,
				font: settingsForm.fontFamily
			});
		}
	}

	// Helper function to convert hex to HSL
	function hexToHSL(hex: string): string {
		// Remove # if present
		hex = hex.replace('#', '');
		
		// Convert hex to RGB
		const r = parseInt(hex.substr(0, 2), 16) / 255;
		const g = parseInt(hex.substr(2, 2), 16) / 255;
		const b = parseInt(hex.substr(4, 2), 16) / 255;
		
		const max = Math.max(r, g, b);
		const min = Math.min(r, g, b);
		let h = 0, s = 0, l = (max + min) / 2;
		
		if (max !== min) {
			const d = max - min;
			s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
			
			switch (max) {
				case r: h = (g - b) / d + (g < b ? 6 : 0); break;
				case g: h = (b - r) / d + 2; break;
				case b: h = (r - g) / d + 4; break;
			}
			h /= 6;
		}
		
		return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
	}

	// Helper function to get contrast color (white or black)
	function getContrastColor(hex: string): string {
		// Remove # if present
		hex = hex.replace('#', '');
		
		// Convert to RGB
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		
		// Calculate luminance
		const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
		
		// Return white for dark colors, black for light colors
		return luminance > 0.5 ? '0 0% 0%' : '0 0% 100%';
	}

	// Helper functions
	function showSuccessMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-success">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 3 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 3000);
	}

	function showErrorMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-error">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 5 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 5000);
	}

	// รีเซ็ตฟอร์ม Payment
	function resetPaymentForm() {
		paymentForm = {
			name: '',
			provider: 'stripe',
			publicKey: '',
			secretKey: '',
			webhookUrl: '',
			percentage: 0,
			fixed: 0,
		};
	}

	// ฟังก์ชันสำหรับแปลงชื่อ Provider
	function getProviderName(provider: string) {
		switch (provider) {
			case 'stripe':
				return 'Stripe';
			case 'omise':
				return 'Omise';
			case 'promptpay':
				return 'PromptPay';
			case 'truemoney':
				return 'TrueMoney';
			default:
				return provider;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	}


</script>

<svelte:head>
	<title>การตั้งค่า - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-4 sm:space-y-4">
	<!-- Error Display -->
	{#if data.error}
		<div class="alert alert-error">
			<Icon icon="heroicons:exclamation-triangle" class="h-5 w-5" />
			<span>{data.error}</span>
		</div>
	{/if}

	<!-- No Settings State -->
	{#if !settings && !data.error}
		<div class="text-center py-12">
			<Icon icon="heroicons:cog-6-tooth" class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
			<h3 class="text-lg font-medium mb-2">ไม่พบการตั้งค่า</h3>
			<p class="text-muted-foreground">กำลังโหลดการตั้งค่าเว็บไซต์...</p>
		</div>
	{:else}
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold tracking-tight">การตั้งค่า</h1>
				<p class="text-muted-foreground">จัดการการตั้งค่าเว็บไซต์และระบบ</p>
				{#if settings}
					<p class="text-xs text-muted-foreground mt-1">
						อัปเดตล่าสุด: {formatDate(settings.updatedAt)}
					</p>
				{/if}
			</div>
		<form
			method="POST"
			action="?/updateSettings"
			use:enhance={() => {
				isLoading = true;
				return async ({ result }) => {
					handleFormResult(result);
				};
			}}
		>
			<!-- Hidden inputs for all form data -->
			<input type="hidden" name="siteName" bind:value={settingsForm.siteName} />
			<input type="hidden" name="siteDescription" bind:value={settingsForm.siteDescription} />
			<input type="hidden" name="siteUrl" bind:value={settingsForm.siteUrl} />
			<input type="hidden" name="primaryColor" bind:value={settingsForm.primaryColor} />
			<input type="hidden" name="secondaryColor" bind:value={settingsForm.secondaryColor} />
			<input type="hidden" name="fontFamily" bind:value={settingsForm.fontFamily} />
			<input type="hidden" name="metaTitle" bind:value={settingsForm.metaTitle} />
			<input type="hidden" name="metaDescription" bind:value={settingsForm.metaDescription} />
			<input type="hidden" name="keywords" bind:value={settingsForm.keywords} />
			<input type="hidden" name="email" bind:value={settingsForm.email} />
			<input type="hidden" name="phone" bind:value={settingsForm.phone} />
			<input type="hidden" name="address" bind:value={settingsForm.address} />
			<input type="hidden" name="facebook" bind:value={settingsForm.facebook} />
			<input type="hidden" name="instagram" bind:value={settingsForm.instagram} />
			<input type="hidden" name="twitter" bind:value={settingsForm.twitter} />
			<input type="hidden" name="line" bind:value={settingsForm.line} />
			<input type="hidden" name="enableCart" value={settingsForm.enableCart.toString()} />
			<input type="hidden" name="enableReviews" value={settingsForm.enableReviews.toString()} />
			<input type="hidden" name="enableWishlist" value={settingsForm.enableWishlist.toString()} />
			<input type="hidden" name="enableChat" value={settingsForm.enableChat.toString()} />
			<input type="hidden" name="emailNotifications" value={settingsForm.emailNotifications.toString()} />
			<input type="hidden" name="smsNotifications" value={settingsForm.smsNotifications.toString()} />
			<input type="hidden" name="pushNotifications" value={settingsForm.pushNotifications.toString()} />
			<input type="hidden" name="twoFactorAuth" value={settingsForm.twoFactorAuth.toString()} />
			<input type="hidden" name="passwordPolicy" bind:value={settingsForm.passwordPolicy} />
			<input type="hidden" name="sessionTimeout" value={settingsForm.sessionTimeout.toString()} />

			<button type="submit" class="btn btn-primary" disabled={isLoading}>
				{#if isLoading}
					<span class="loading loading-spinner loading-sm"></span>
				{:else}
					<Icon icon="heroicons:check" class="h-4 w-4 mr-2" />
				{/if}
				บันทึกการตั้งค่า
			</button>
		</form>
	</div>

	{#if isLoading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<!-- แท็บ -->
		<div class="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
			<Button
				variant={activeTab === 'general' ? 'solid' : 'ghost'}
				size="sm"
				onclick={() => (activeTab = 'general')}
			>
				<Icon icon="heroicons:globe-alt" class="h-4 w-4 mr-2" />
				ทั่วไป
			</Button>
			<Button
				variant={activeTab === 'payment' ? 'solid' : 'ghost'}
				size="sm"
				onclick={() => (activeTab = 'payment')}
			>
				<Icon icon="heroicons:credit-card" class="h-4 w-4 mr-2" />
				การชำระเงิน
			</Button>
			<Button
				variant={activeTab === 'security' ? 'solid' : 'ghost'}
				size="sm"
				onclick={() => (activeTab = 'security')}
			>
				<Icon icon="heroicons:shield-check" class="h-4 w-4 mr-2" />
				ความปลอดภัย
			</Button>
			<Button
				variant={activeTab === 'notifications' ? 'solid' : 'ghost'}
				size="sm"
				onclick={() => (activeTab = 'notifications')}
			>
				<Icon icon="heroicons:bell" class="h-4 w-4 mr-2" />
				การแจ้งเตือน
			</Button>
		</div>

		{#if activeTab === 'general'}
			<div class="grid gap-6">
				<!-- ข้อมูลเว็บไซต์ -->
				<Card>
					<div>
						<div>ข้อมูลเว็บไซต์</div>
						<div>ข้อมูลพื้นฐานของเว็บไซต์</div>
					</div>
					<div class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="siteName" class="text-sm font-medium">ชื่อเว็บไซต์</label>
								<Input id="siteName" bind:value={settingsForm.siteName} />
							</div>
							<div class="space-y-2">
								<label for="siteUrl" class="text-sm font-medium">URL เว็บไซต์</label>
								<Input id="siteUrl" bind:value={settingsForm.siteUrl} placeholder="https://example.com" />
							</div>
						</div>
						<div class="space-y-2">
							<label for="siteDescription" class="text-sm font-medium">คำอธิบายเว็บไซต์</label>
							<Textarea id="siteDescription" bind:value={settingsForm.siteDescription} rows={3} />
						</div>
					</div>
				</Card>

				<!-- ธีม -->
				<Card>
					<div>
						<div>ธีมและการแสดงผล</div>
						<div>ปรับแต่งสีและฟอนต์</div>
					</div>
					<div class="space-y-4">
						<div class="grid grid-cols-3 gap-4">
							<div class="space-y-2">
								<label for="primaryColor" class="text-sm font-medium">สีหลัก</label>
								<input id="primaryColor" type="color" bind:value={settingsForm.primaryColor} class="w-full h-10 rounded border" />
							</div>
							<div class="space-y-2">
								<label for="secondaryColor" class="text-sm font-medium">สีรอง</label>
								<input id="secondaryColor" type="color" bind:value={settingsForm.secondaryColor} class="w-full h-10 rounded border" />
							</div>
							<div class="space-y-2">
								<label for="fontFamily" class="text-sm font-medium">ฟอนต์</label>
								<Select
									id="fontFamily"
									bind:value={settingsForm.fontFamily}
									options={[
										{ value: 'Inter', label: 'Inter' },
										{ value: 'Roboto', label: 'Roboto' },
										{
											value: 'Open Sans',
											label: 'Open Sans',
										},
										{ value: 'Lato', label: 'Lato' },
									]}
								/>
							</div>
						</div>
					</div>
				</Card>

				<!-- SEO -->
				<Card>
					<div>
						<div>SEO</div>
						<div>การตั้งค่าสำหรับเครื่องมือค้นหา</div>
					</div>
					<div class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="metaTitle" class="text-sm font-medium">Meta Title</label>
								<Input id="metaTitle" bind:value={settingsForm.metaTitle} />
							</div>
							<div class="space-y-2">
								<label for="keywords" class="text-sm font-medium">Keywords (คั่นด้วยจุลภาค)</label>
								<Input
									id="keywords"
									bind:value={settingsForm.keywords}
									placeholder="keyword1, keyword2, keyword3"
								/>
							</div>
						</div>
						<div class="space-y-2">
							<label for="metaDescription" class="text-sm font-medium">Meta Description</label>
							<Textarea id="metaDescription" bind:value={settingsForm.metaDescription} rows={3} />
						</div>
					</div>
				</Card>

				<!-- ข้อมูลติดต่อ -->
				<Card>
					<div>
						<div>ข้อมูลติดต่อ</div>
						<div>ข้อมูลการติดต่อและโซเชียลมีเดีย</div>
					</div>
					<div class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="email" class="text-sm font-medium">อีเมล</label>
								<Input id="email" type="email" bind:value={settingsForm.email} />
							</div>
							<div class="space-y-2">
								<label for="phone" class="text-sm font-medium">เบอร์โทรศัพท์</label>
								<Input id="phone" bind:value={settingsForm.phone} />
							</div>
						</div>
						<div class="space-y-2">
							<label for="address" class="text-sm font-medium">ที่อยู่</label>
							<Textarea id="address" bind:value={settingsForm.address} rows={2} />
						</div>
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="facebook" class="text-sm font-medium">Facebook</label>
								<Input id="facebook" bind:value={settingsForm.facebook} placeholder="https://facebook.com/..." />
							</div>
							<div class="space-y-2">
								<label for="instagram" class="text-sm font-medium">Instagram</label>
								<Input
									id="instagram"
									bind:value={settingsForm.instagram}
									placeholder="https://instagram.com/..."
								/>
							</div>
							<div class="space-y-2">
								<label for="twitter" class="text-sm font-medium">Twitter</label>
								<Input id="twitter" bind:value={settingsForm.twitter} placeholder="https://twitter.com/..." />
							</div>
							<div class="space-y-2">
								<label for="line" class="text-sm font-medium">Line</label>
								<Input id="line" bind:value={settingsForm.line} placeholder="@lineid" />
							</div>
						</div>
					</div>
				</Card>

				<!-- ฟีเจอร์ -->
				<Card>
					<div>
						<div>ฟีเจอร์เว็บไซต์</div>
						<div>เปิด/ปิดฟีเจอร์ต่างๆ</div>
					</div>
					<div class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="flex items-center justify-between">
								<label for="enableCart" class="text-sm font-medium">ตะกร้าสินค้า</label>
								<input id="enableCart" type="checkbox" bind:checked={settingsForm.enableCart} class="toggle" />
							</div>
							<div class="flex items-center justify-between">
								<label for="enableReviews" class="text-sm font-medium">รีวิวสินค้า</label>
								<input id="enableReviews" type="checkbox" bind:checked={settingsForm.enableReviews} class="toggle" />
							</div>
							<div class="flex items-center justify-between">
								<label for="enableWishlist" class="text-sm font-medium">รายการโปรด</label>
								<input id="enableWishlist" type="checkbox" bind:checked={settingsForm.enableWishlist} class="toggle" />
							</div>
							<div class="flex items-center justify-between">
								<label for="enableChat" class="text-sm font-medium">แชทสด</label>
								<input id="enableChat" type="checkbox" bind:checked={settingsForm.enableChat} class="toggle" />
							</div>
						</div>
					</div>
				</Card>
			</div>
		{:else if activeTab === 'payment'}
			<div class="grid gap-6">
				<!-- เพิ่ม Payment Gateway -->
				<Card>
					<div>
						<div>เพิ่ม Payment Gateway</div>
						<div>เพิ่มช่องทางการชำระเงินใหม่</div>
					</div>
					<div class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="paymentName" class="text-sm font-medium">ชื่อ</label>
								<Input id="paymentName" bind:value={paymentForm.name} placeholder="Stripe Payment" />
							</div>
							<div class="space-y-2">
								<label for="paymentProvider" class="text-sm font-medium">Provider</label>
								<Select
									id="paymentProvider"
									bind:value={paymentForm.provider}
									options={[
										{ value: 'stripe', label: 'Stripe' },
										{ value: 'omise', label: 'Omise' },
										{
											value: 'promptpay',
											label: 'PromptPay',
										},
										{
											value: 'truemoney',
											label: 'TrueMoney',
										},
									]}
								/>
							</div>
						</div>
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="publicKey" class="text-sm font-medium">Public Key</label>
								<Input id="publicKey" bind:value={paymentForm.publicKey} />
							</div>
							<div class="space-y-2">
								<label for="secretKey" class="text-sm font-medium">Secret Key</label>
								<div class="relative">
									<Input
										id="secretKey"
										type={showSecretKeys ? 'text' : 'password'}
										bind:value={paymentForm.secretKey}
									/>
									<Button
										variant="ghost"
										size="sm"
										class="absolute right-2 top-1/2 transform -translate-y-1/2"
										onclick={() => (showSecretKeys = !showSecretKeys)}
									>
										{#if showSecretKeys}
											<Icon icon="heroicons:eye-slash" class="h-4 w-4" />
										{:else}
											<Icon icon="heroicons:eye" class="h-4 w-4" />
										{/if}
									</Button>
								</div>
							</div>
						</div>
						<div class="space-y-2">
							<label for="webhookUrl" class="text-sm font-medium">Webhook URL</label>
							<Input id="webhookUrl" bind:value={paymentForm.webhookUrl} placeholder="https://..." />
						</div>
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="percentage" class="text-sm font-medium">ค่าธรรมเนียม (%)</label>
								<Input id="percentage" type="number" step="0.01" bind:value={paymentForm.percentage} />
							</div>
							<div class="space-y-2">
								<label for="fixed" class="text-sm font-medium">ค่าธรรมเนียมคงที่ (บาท)</label>
								<Input id="fixed" type="number" step="0.01" bind:value={paymentForm.fixed} />
							</div>
						</div>
						<form
							method="POST"
							action="?/createPaymentGateway"
							use:enhance={() => {
								isLoading = true;
								return async ({ result }) => {
									handleFormResult(result);
								};
							}}
						>
							<input type="hidden" name="name" bind:value={paymentForm.name} />
							<input type="hidden" name="provider" bind:value={paymentForm.provider} />
							<input type="hidden" name="publicKey" bind:value={paymentForm.publicKey} />
							<input type="hidden" name="secretKey" bind:value={paymentForm.secretKey} />
							<input type="hidden" name="webhookUrl" bind:value={paymentForm.webhookUrl} />
							<input type="hidden" name="percentage" value={paymentForm.percentage.toString()} />
							<input type="hidden" name="fixed" value={paymentForm.fixed.toString()} />
							
							<Button type="submit" disabled={isLoading}>
								<Icon icon="heroicons:credit-card" class="h-4 w-4 mr-2" />
								เพิ่ม Payment Gateway
							</Button>
						</form>
					</div>
				</Card>

				<!-- รายการ Payment Gateways -->
				<Card>
					<div>
						<div>Payment Gateways</div>
						<div>ช่องทางการชำระเงินที่ใช้งานอยู่</div>
					</div>
					<div>
						{#if paymentGateways.length === 0}
							<div class="text-center py-8 text-muted-foreground">ยังไม่มี Payment Gateway</div>
						{:else}
							<div class="space-y-4">
								{#each paymentGateways as gateway (gateway._id)}
									<div class="flex items-center justify-between p-4 border rounded-lg">
										<div class="flex items-center gap-4">
											<Icon icon="heroicons:credit-card" class="h-8 w-8 text-muted-foreground" />
											<div>
												<h3 class="font-medium">
													{gateway.name}
												</h3>
												<p class="text-sm text-muted-foreground">
													{getProviderName(gateway.provider)} • ค่าธรรมเนียม {gateway.fees
														.percentage}% + {gateway.fees.fixed} บาท
												</p>
												<p class="text-xs text-muted-foreground">
													เพิ่มเมื่อ {formatDate(gateway.createdAt)}
												</p>
											</div>
										</div>
										<div class="flex items-center gap-2">
											<Badge variant={gateway.status === 'active' ? 'solid' : 'outline'}>
												{gateway.status === 'active' ? 'ใช้งาน' : 'ไม่ใช้งาน'}
											</Badge>
											<form
												method="POST"
												action="?/deletePaymentGateway"
												use:enhance={() => {
													isLoading = true;
													return async ({ result }) => {
														handleFormResult(result);
													};
												}}
											>
												<input type="hidden" name="gatewayId" value={gateway._id} />
												<Button
													type="submit"
													variant="ghost"
													size="sm"
													disabled={isLoading}
												>
													ลบ
												</Button>
											</form>
										</div>
									</div>
								{/each}
							</div>
						{/if}
					</div>
				</Card>
			</div>
		{:else if activeTab === 'security'}
			<div class="grid gap-6">
				<Card>
					<div>
						<div>ความปลอดภัย</div>
						<div>การตั้งค่าความปลอดภัยของระบบ</div>
					</div>
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium" for="two-factor-auth">Two-Factor Authentication</label>
								<p class="text-xs text-muted-foreground">
									เพิ่มความปลอดภัยด้วยการยืนยันตัวตน 2 ขั้นตอน
								</p>
							</div>
							<input id="two-factor-auth" type="checkbox" bind:checked={settingsForm.twoFactorAuth} class="toggle" />
						</div>

						<div class="space-y-2">
							<label class="text-sm font-medium" for="password-policy">นโยบายรหัสผ่าน</label>
							<Select
								id="password-policy"
								bind:value={settingsForm.passwordPolicy}
								options={[
									{
										value: 'weak',
										label: 'อ่อน (6 ตัวอักษร)',
									},
									{
										value: 'medium',
										label: 'ปานกลาง (8 ตัวอักษร + ตัวเลข)',
									},
									{
										value: 'strong',
										label: 'แข็งแกร่ง (8 ตัวอักษร + ตัวเลข + สัญลักษณ์)',
									},
								]}
							/>
						</div>

						<div class="space-y-2">
							<label class="text-sm font-medium" for="session-timeout">Session Timeout (นาที)</label>
							<Input id="session-timeout" type="number" bind:value={settingsForm.sessionTimeout} />
						</div>
					</div>
				</Card>
			</div>
		{:else if activeTab === 'notifications'}
			<div class="grid gap-6">
				<Card>
					<div>
						<div>การแจ้งเตือน</div>
						<div>การตั้งค่าการแจ้งเตือนต่างๆ</div>
					</div>
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium" for="email-notifications">การแจ้งเตือนทางอีเมล</label>
								<p class="text-xs text-muted-foreground">รับการแจ้งเตือนผ่านอีเมล</p>
							</div>
							<input id="email-notifications" type="checkbox" bind:checked={settingsForm.emailNotifications} class="toggle" />
						</div>

						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium" for="sms-notifications">การแจ้งเตือนทาง SMS</label>
								<p class="text-xs text-muted-foreground">รับการแจ้งเตือนผ่าง SMS</p>
							</div>
							<input id="sms-notifications" type="checkbox" bind:checked={settingsForm.smsNotifications} class="toggle" />
						</div>

						<div class="flex items-center justify-between">
							<div>
								<label class="text-sm font-medium" for="push-notifications">Push Notifications</label>
								<p class="text-xs text-muted-foreground">รับการแจ้งเตือนแบบ Push</p>
							</div>
							<input id="push-notifications" type="checkbox" bind:checked={settingsForm.pushNotifications} class="toggle" />
						</div>
					</div>
				</Card>
			</div>
		{/if}
	{/if}
{/if}
</div>

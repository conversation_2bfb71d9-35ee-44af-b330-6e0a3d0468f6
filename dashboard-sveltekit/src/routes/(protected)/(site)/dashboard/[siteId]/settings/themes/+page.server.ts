import { themeService } from '$lib/services/theme';
import type { ThemeSettings } from '$lib/types';
import { error, fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params }) => {
  // ไม่ต้องตรวจสอบ auth อีก เพราะ layout จัดการแล้ว
  const { siteId } = params;

  try {
    // ดึงข้อมูล theme จาก API
    const themeResponse = await themeService.getTheme(siteId!, locals.token!);

    if (!themeResponse.success) {
      // ถ้าไม่มี theme ให้ใช้ค่า default
      const defaultTheme = await themeService.getDefaultTheme();
      defaultTheme.siteId = siteId!;
      return {
        theme: defaultTheme,
        isNew: true,
        siteId,
      };
    }

    return {
      theme: themeResponse.data,
      isNew: false,
      siteId,
    };
  }
  catch (err) {
    console.error('Error loading theme:', err);
    // ถ้าเกิดข้อผิดพลาด ให้ใช้ค่า default
    const defaultTheme = await themeService.getDefaultTheme();
    defaultTheme.siteId = siteId!;
    return {
      theme: defaultTheme,
      isNew: true,
      siteId,
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Save Theme - Hybrid Approach
   * Route API + Service Pattern
   */
  saveTheme: async ({ request, locals, params }) => {
    try {
      const { siteId } = params;
      const formData = await request.formData();

      // Basic validation at route level
      if (!siteId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ Site ID',
          type: 'save',
        });
      }

      const themeData: ThemeSettings = {
        siteId: siteId!,
        layout: formData.get('layout') as string,
        headerStyle: formData.get('headerStyle') as string,
        footerStyle: formData.get('footerStyle') as string,
        showSearch: formData.get('showSearch') === 'on',
        showLanguageSelector: formData.get('showLanguageSelector') === 'on',
        showThemeToggle: formData.get('showThemeToggle') === 'on',
        mobileMenuStyle: formData.get('mobileMenuStyle') as string,
        desktopMenuStyle: formData.get('desktopMenuStyle') as string,
        primaryColor: formData.get('primaryColor') as string,
        secondaryColor: formData.get('secondaryColor') as string,
        backgroundColor: formData.get('backgroundColor') as string,
        textColor: formData.get('textColor') as string,
        accentColor: formData.get('accentColor') as string,
        fontFamily: formData.get('fontFamily') as string,
        fontSize: formData.get('fontSize') as string,
        lineHeight: formData.get('lineHeight') as string,
        fontWeight: formData.get('fontWeight') as string,
        containerPadding: formData.get('containerPadding') as string,
        sectionSpacing: formData.get('sectionSpacing') as string,
        elementSpacing: formData.get('elementSpacing') as string,
      };

      // Route-level validation for required fields
      if (!themeData.layout || !themeData.primaryColor) {
        return fail(400, {
          message: 'กรุณาเลือก Layout และสีหลัก',
          type: 'save',
        });
      }

      // Call service for business logic + backend API
      const response = await themeService.saveTheme(themeData, locals.token!);

      if (!response.success) {
        return fail(400, {
          message: response.error,
          type: 'save',
        });
      }

      return {
        success: true,
        data: response.data,
        message: 'บันทึกธีมสำเร็จ',
        type: 'save',
      };
    }
    catch (error) {
      console.error('Save theme error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการบันทึกธีม',
        type: 'save',
      });
    }
  },

  /**
   * ✅ Reset Theme - Hybrid Approach
   * Route API + Service Pattern
   */
  resetTheme: async ({ locals, params }) => {
    try {
      const { siteId } = params;

      // Basic validation at route level
      if (!siteId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ Site ID',
          type: 'reset',
        });
      }

      // Get default theme and set siteId
      const defaultTheme = await themeService.getDefaultTheme();
      defaultTheme.siteId = siteId;

      // Call service for business logic + backend API
      const response = await themeService.saveTheme(defaultTheme, locals.token!);

      if (!response.success) {
        return fail(400, {
          message: response.error,
          type: 'reset',
        });
      }

      return {
        success: true,
        data: response.data,
        message: 'รีเซ็ตธีมเป็นค่าเริ่มต้นสำเร็จ',
        type: 'reset',
      };
    }
    catch (error) {
      console.error('Reset theme error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการรีเซ็ตธีม',
        type: 'reset',
      });
    }
  },

  /**
   * ✅ Delete Theme - Hybrid Approach
   * Route API + Service Pattern
   */
  deleteTheme: async ({ locals, params }) => {
    try {
      const { siteId } = params;

      // Basic validation at route level
      if (!siteId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ Site ID',
          type: 'delete',
        });
      }

      // Call service for business logic + backend API
      const response = await themeService.deleteTheme(siteId, locals.token!);

      if (!response.success) {
        return fail(400, {
          message: response.error,
          type: 'delete',
        });
      }

      return {
        success: true,
        message: 'ลบธีมสำเร็จ',
        type: 'delete',
      };
    }
    catch (error) {
      console.error('Delete theme error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบธีม',
        type: 'delete',
      });
    }
  },
};

import {
  sanitizeShippingData,
  sanitizeShippingMethodData,
  validateCreateShippingData,
  validateCreateShippingMethodData,
  validateUpdateShippingData,
} from '$lib/schemas/shipping.schema';
import { shippingService } from '$lib/services/shipping';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;

  try {
    // ตรวจสอบ token
    if (!locals.token) {
      console.log('Shipping Page Server: No token found');
      return {
        siteId,
        shippings: [],
        shippingMethods: [],
        error: 'ไม่พบ token สำหรับการเข้าถึง',
      };
    }

    // ดึง query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || undefined;
    const status = url.searchParams.get('status') || undefined;
    const carrier = url.searchParams.get('carrier') || undefined;
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

    // ดึงข้อมูล shippings และ shipping methods พร้อมกัน
    const [shippingsResult, methodsResult] = await Promise.all([
      shippingService.getShippings(siteId, locals.token, {
        page,
        limit,
        search,
        status: status as any,
        carrier,
        sortBy,
        sortOrder,
      }),
      shippingService.getShippingMethods(siteId, locals.token),
    ]);

    // ตรวจสอบผลลัพธ์
    if (!shippingsResult.success) {
      console.log('Shipping Page Server: Failed to fetch shippings:', shippingsResult.error);
      return {
        siteId,
        shippings: [],
        shippingMethods: [],
        error: shippingsResult.error,
      };
    }

    if (!methodsResult.success) {
      console.log('Shipping Page Server: Failed to fetch methods:', methodsResult.error);
      // ยังคงส่งข้อมูล shippings แม้ methods จะล้มเหลว
      return {
        siteId,
        shippings: shippingsResult.data?.shippings || [],
        pagination: shippingsResult.data?.pagination,
        shippingMethods: [],
        error: `ไม่สามารถดึงวิธีการจัดส่งได้: ${methodsResult.error}`,
      };
    }

    console.log('Shipping Page Server: Successfully loaded data');
    return {
      siteId,
      shippings: shippingsResult.data?.shippings || [],
      pagination: shippingsResult.data?.pagination,
      shippingMethods: methodsResult.data?.methods || [],
      error: null,
    };
  }
  catch (error) {
    console.error('Shipping Page Server: Error in load function:', error);
    return {
      siteId,
      shippings: [],
      shippingMethods: [],
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const actions: Actions = {
  updateShipping: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const shippingId = data.get('shippingId')?.toString()?.trim();
      const trackingNumber = data.get('trackingNumber')?.toString()?.trim();
      const status = data.get('status')?.toString()?.trim();
      const estimatedDeliveryDate = data.get('estimatedDeliveryDate')?.toString()?.trim();
      const notes = data.get('notes')?.toString()?.trim();

      // Route-level validation
      if (!shippingId) {
        return fail(400, {
          message: 'กรุณาระบุ Shipping ID',
          type: 'updateShipping',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'updateShipping',
        });
      }

      // Prepare update data
      const updateData: any = {};
      if (trackingNumber) updateData.trackingNumber = trackingNumber;
      if (status) updateData.status = status;
      if (estimatedDeliveryDate) updateData.estimatedDeliveryDate = estimatedDeliveryDate;
      if (notes) updateData.notes = notes;

      // Validate using schema
      const sanitizedData = sanitizeShippingData(updateData);
      const validationResult = validateUpdateShippingData(sanitizedData);

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'updateShipping',
        });
      }

      // Call service for business logic
      const result = await shippingService.updateShipping(
        shippingId,
        validationResult.data!,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'updateShipping',
        });
      }

      return {
        success: true,
        shipping: result.data,
        message: 'อัปเดตการจัดส่งสำเร็จ',
        type: 'updateShipping',
      };
    }
    catch (error) {
      console.error('Update shipping error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตการจัดส่ง',
        type: 'updateShipping',
      });
    }
  },

  createShippingMethod: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const name = data.get('name')?.toString()?.trim();
      const carrier = data.get('carrier')?.toString()?.trim();
      const cost = parseFloat(data.get('cost')?.toString() || '0');
      const estimatedDays = parseInt(data.get('estimatedDays')?.toString() || '1');
      const isActive = data.get('isActive') === 'true';

      // Route-level validation
      if (!name) {
        return fail(400, {
          message: 'กรุณาระบุชื่อวิธีการจัดส่ง',
          type: 'createShippingMethod',
        });
      }

      if (!carrier) {
        return fail(400, {
          message: 'กรุณาระบุผู้ให้บริการขนส่ง',
          type: 'createShippingMethod',
        });
      }

      if (cost < 0) {
        return fail(400, {
          message: 'ค่าจัดส่งต้องไม่น้อยกว่า 0',
          type: 'createShippingMethod',
        });
      }

      if (estimatedDays < 1) {
        return fail(400, {
          message: 'ระยะเวลาจัดส่งต้องไม่น้อยกว่า 1 วัน',
          type: 'createShippingMethod',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'createShippingMethod',
        });
      }

      // Prepare method data
      const methodData = {
        name,
        carrier,
        cost,
        estimatedDays,
        isActive,
      };

      // Validate using schema
      const sanitizedData = sanitizeShippingMethodData(methodData);
      const validationResult = validateCreateShippingMethodData(sanitizedData);

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'createShippingMethod',
        });
      }

      // Call service for business logic
      const result = await shippingService.createShippingMethod(
        validationResult.data!,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'createShippingMethod',
        });
      }

      return {
        success: true,
        shippingMethod: result.data,
        message: 'สร้างวิธีการจัดส่งสำเร็จ',
        type: 'createShippingMethod',
      };
    }
    catch (error) {
      console.error('Create shipping method error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างวิธีการจัดส่ง',
        type: 'createShippingMethod',
      });
    }
  },

  deleteShipping: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const shippingId = data.get('shippingId')?.toString()?.trim();

      // Route-level validation
      if (!shippingId) {
        return fail(400, {
          message: 'กรุณาระบุ Shipping ID',
          type: 'deleteShipping',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'deleteShipping',
        });
      }

      // Call service for business logic
      const result = await shippingService.deleteShipping(shippingId, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteShipping',
        });
      }

      return {
        success: true,
        message: 'ลบการจัดส่งสำเร็จ',
        type: 'deleteShipping',
      };
    }
    catch (error) {
      console.error('Delete shipping error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบการจัดส่ง',
        type: 'deleteShipping',
      });
    }
  },

  createShipping: async ({ request, locals, params }) => {
    const { siteId } = params;

    try {
      const data = await request.formData();
      const orderId = data.get('orderId')?.toString()?.trim();
      const carrier = data.get('carrier')?.toString()?.trim();
      const method = data.get('method')?.toString()?.trim();
      const shippingCost = parseFloat(data.get('shippingCost')?.toString() || '0');
      const notes = data.get('notes')?.toString()?.trim();

      // Shipping address
      const addressName = data.get('addressName')?.toString()?.trim();
      const addressPhone = data.get('addressPhone')?.toString()?.trim();
      const address = data.get('address')?.toString()?.trim();
      const district = data.get('district')?.toString()?.trim();
      const province = data.get('province')?.toString()?.trim();
      const postalCode = data.get('postalCode')?.toString()?.trim();

      // Route-level validation
      if (!orderId) {
        return fail(400, {
          message: 'กรุณาระบุ Order ID',
          type: 'createShipping',
        });
      }

      if (!carrier) {
        return fail(400, {
          message: 'กรุณาระบุผู้ให้บริการขนส่ง',
          type: 'createShipping',
        });
      }

      if (!method) {
        return fail(400, {
          message: 'กรุณาระบุวิธีการจัดส่ง',
          type: 'createShipping',
        });
      }

      if (!addressName) {
        return fail(400, {
          message: 'กรุณาระบุชื่อผู้รับ',
          type: 'createShipping',
        });
      }

      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการเข้าถึง',
          type: 'createShipping',
        });
      }

      // Prepare shipping data
      const shippingData = {
        orderId,
        carrier,
        method,
        shippingCost,
        notes,
        shippingAddress: {
          name: addressName,
          phone: addressPhone || '',
          address: address || '',
          district: district || '',
          province: province || '',
          postalCode: postalCode || '',
        },
      };

      // Validate using schema
      const sanitizedData = sanitizeShippingData(shippingData);
      const validationResult = validateCreateShippingData(sanitizedData);

      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'createShipping',
        });
      }

      // Call service for business logic
      const result = await shippingService.createShipping(validationResult.data!, siteId, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'createShipping',
        });
      }

      return {
        success: true,
        shipping: result.data,
        message: 'สร้างการจัดส่งสำเร็จ',
        type: 'createShipping',
      };
    }
    catch (error) {
      console.error('Create shipping error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างการจัดส่ง',
        type: 'createShipping',
      });
    }
  },
};

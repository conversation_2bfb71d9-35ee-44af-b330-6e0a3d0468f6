<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { Badge, Button, Card, Dialog, Input, Label, Select, Table, Search } from '$lib/components/ui';

	interface Shipping {
		_id: string;
		orderId: string;
		trackingNumber?: string;
		carrier: string;
		method: string;
		status: 'preparing' | 'shipped' | 'in_transit' | 'delivered' | 'failed' | 'returned';
		shippingAddress: {
			name: string;
			phone: string;
			address: string;
			district: string;
			province: string;
			postalCode: string;
		};
		estimatedDeliveryDate?: string;
		actualDeliveryDate?: string;
		shippingCost: number;
		notes?: string;
		createdAt: string;
		updatedAt: string;
	}

	interface ShippingMethod {
		_id: string;
		name: string;
		carrier: string;
		cost: number;
		estimatedDays: number;
		isActive: boolean;
	}

	const { data, form } = $props<{
		data: {
			siteId: string;
			shippings: Shipping[];
			shippingMethods: ShippingMethod[];
			pagination?: any;
			error?: string;
		};
		form?: any;
	}>();

	const shippings = $derived(data?.shippings || []);
	const shippingMethods = $derived(data?.shippingMethods || []);
	const error = $derived(data?.error);

	// Form states
	let isLoading = $state(false);
	let searchQuery = $state('');
	let statusFilter = $state('all');
	let carrierFilter = $state('all');
	let selectedShipping: Shipping | null = $state(null);
	let viewDialogOpen = $state(false);
	let updateDialogOpen = $state(false);
	let methodDialogOpen = $state(false);

	// ฟอร์มอัปเดตการจัดส่ง
	let updateForm = $state({
		shippingId: '',
		trackingNumber: '',
		status: 'preparing' as Shipping['status'],
		estimatedDeliveryDate: '',
		notes: '',
	});

	// ฟอร์มวิธีการจัดส่ง
	let methodForm = $state({
		name: '',
		carrier: '',
		cost: 0,
		estimatedDays: 1,
		isActive: true,
	});

	// Handle form results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			if (result.data?.type === 'updateShipping') {
				updateDialogOpen = false;
				showSuccessMessage(result.data?.message || 'อัปเดตการจัดส่งสำเร็จ');
			} else if (result.data?.type === 'createShippingMethod') {
				methodDialogOpen = false;
				resetMethodForm();
				showSuccessMessage(result.data?.message || 'สร้างวิธีการจัดส่งสำเร็จ');
			} else if (result.data?.type === 'deleteShipping') {
				showSuccessMessage(result.data?.message || 'ลบการจัดส่งสำเร็จ');
			}
			// Refresh data
			invalidateAll();
		} else if (result.type === 'failure') {
			// Handle error
			showErrorMessage(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// Helper functions
	function showSuccessMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-success">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 3 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 3000);
	}

	function showErrorMessage(message: string) {
		// สร้าง toast notification
		const toast = document.createElement('div');
		toast.className = 'toast toast-top toast-end';
		toast.innerHTML = `
			<div class="alert alert-error">
				<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>${message}</span>
			</div>
		`;
		document.body.appendChild(toast);
		
		// ลบ toast หลังจาก 5 วินาที
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 5000);
	}
	const filteredShippings = $derived(
		shippings.filter(shipping => {
			const matchesSearch =
				shipping.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
				(shipping.trackingNumber &&
					shipping.trackingNumber.toLowerCase().includes(searchQuery.toLowerCase())) ||
				shipping.shippingAddress.name.toLowerCase().includes(searchQuery.toLowerCase());

			const matchesStatus = statusFilter === 'all' || shipping.status === statusFilter;

			const matchesCarrier = carrierFilter === 'all' || shipping.carrier === carrierFilter;

			return matchesSearch && matchesStatus && matchesCarrier;
		})
	);

	// รีเซ็ตฟอร์มวิธีการจัดส่ง
	function resetMethodForm() {
		methodForm = {
			name: '',
			carrier: '',
			cost: 0,
			estimatedDays: 1,
			isActive: true,
		};
	}

	// เปิดดูรายละเอียด
	function viewShipping(shipping: Shipping) {
		selectedShipping = shipping;
		viewDialogOpen = true;
	}

	// เปิดฟอร์มอัปเดต
	function openUpdateDialog(shipping: Shipping) {
		selectedShipping = shipping;
		updateForm = {
			shippingId: shipping._id,
			trackingNumber: shipping.trackingNumber || '',
			status: shipping.status,
			estimatedDeliveryDate: shipping.estimatedDeliveryDate
				? new Date(shipping.estimatedDeliveryDate).toISOString().split('T')[0]
				: '',
			notes: shipping.notes || '',
		};
		updateDialogOpen = true;
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตามสถานะ
	function getStatusBadgeColor(status: string) {
		switch (status) {
			case 'preparing':
				return 'warning';
			case 'shipped':
				return 'info';
			case 'in_transit':
				return 'info';
			case 'delivered':
				return 'success';
			case 'failed':
				return 'error';
			case 'returned':
				return 'error';
			default:
				return 'neutral';
		}
	}

	// ฟังก์ชันสำหรับแปลงสถานะเป็นภาษาไทย
	function getStatusText(status: string) {
		switch (status) {
			case 'preparing':
				return 'กำลังเตรียม';
			case 'shipped':
				return 'จัดส่งแล้ว';
			case 'in_transit':
				return 'อยู่ระหว่างขนส่ง';
			case 'delivered':
				return 'ส่งมอบแล้ว';
			case 'failed':
				return 'จัดส่งไม่สำเร็จ';
			case 'returned':
				return 'ส่งคืนแล้ว';
			default:
				return status;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}

	// ฟังก์ชันสำหรับแปลงเงิน
	function formatCurrency(amount: number) {
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB',
		}).format(amount);
	}

	// คำนวณสถิติการจัดส่ง
	const shippingStats = $derived({
		total: shippings.length,
		preparing: shippings.filter(s => s.status === 'preparing').length,
		shipped: shippings.filter(s => s.status === 'shipped').length,
		inTransit: shippings.filter(s => s.status === 'in_transit').length,
		delivered: shippings.filter(s => s.status === 'delivered').length,
	});

	// รายการผู้ให้บริการขนส่ง
	const carriers = $derived([...new Set(shippings.map(s => s.carrier))]);


</script>

<svelte:head>
	<title>จัดการการจัดส่ง - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-4 sm:space-y-4">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการการจัดส่ง</h1>
			<p class="text-muted-foreground">จัดการการจัดส่งสินค้าและติดตามสถานะ</p>
		</div>

		<button class="btn btn-primary" onclick={() => (methodDialogOpen = true)}>
			<Icon icon="heroicons:plus" class="h-4 w-4 mr-2" />
			เพิ่มวิธีการจัดส่ง
		</button>
	</div>

	<!-- สถิติการจัดส่ง -->
	<div class="grid gap-4 md:grid-cols-5">
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ทั้งหมด</div>
				<Icon icon="heroicons:package" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold">{shippingStats.total}</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">กำลังเตรียม</div>
				<Icon icon="heroicons:clock" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-orange-600">
					{shippingStats.preparing}
				</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">จัดส่งแล้ว</div>
				<Icon icon="heroicons:truck" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-blue-600">
					{shippingStats.shipped}
				</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ระหว่างขนส่ง</div>
				<Icon icon="heroicons:truck" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-purple-600">
					{shippingStats.inTransit}
				</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ส่งมอบแล้ว</div>
				<Icon icon="heroicons:check-circle" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-green-600">
					{shippingStats.delivered}
				</div>
			</div>
		</Card>
	</div>

	<!-- ตัวกรองและค้นหา -->
	<Card size="full">
		<div class="p-4">
			<div class="flex flex-col md:flex-row gap-4">
				<div class="flex-1">
					<Search
						placeholder="ค้นหาการจัดส่ง..."
						value={searchQuery}
						onChange={(value) => {
							// ใช้ $state() สำหรับการ assign ค่า
							searchQuery = value;
						}}
						onSearch={(value) => {
							searchQuery = value;
						}}
					/>
				</div>
				<Select
					bind:value={statusFilter}
					placeholder="สถานะ"
					class="w-full md:w-48"
					options={[
						{ value: 'all', label: 'ทั้งหมด' },
						{ value: 'preparing', label: 'กำลังเตรียม' },
						{ value: 'shipped', label: 'จัดส่งแล้ว' },
						{ value: 'in_transit', label: 'ระหว่างขนส่ง' },
						{ value: 'delivered', label: 'ส่งมอบแล้ว' },
						{ value: 'failed', label: 'จัดส่งไม่สำเร็จ' },
						{ value: 'returned', label: 'ส่งคืนแล้ว' },
					]}
				/>
				<Select
					bind:value={carrierFilter}
					placeholder="ผู้ให้บริการ"
					class="w-full md:w-48"
					options={[
						{ value: 'all', label: 'ทั้งหมด' },
						...carriers.map(carrier => ({
							value: carrier,
							label: carrier,
						})),
					]}
				/>
			</div>
		</div>
	</Card>

	<!-- Error Alert -->
	{#if error}
		<div class="alert alert-error">
			<Icon icon="mdi:alert-circle" class="w-5 h-5" />
			<span>{error}</span>
		</div>
	{/if}
		<Card>
			<div>
				<div class="flex items-center gap-2">
					<Icon icon="heroicons:truck" class="h-5 w-5" />
					การจัดส่งทั้งหมด ({filteredShippings.length})
				</div>
			</div>
			<div>
				{#if filteredShippings.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || statusFilter !== 'all' || carrierFilter !== 'all'
							? 'ไม่พบการจัดส่งที่ตรงกับเงื่อนไข'
							: 'ยังไม่มีการจัดส่ง'}
					</div>
				{:else}
					<Table>
						<table class="table table-zebra w-full">
							<thead>
								<tr>
									<th>คำสั่งซื้อ</th>
									<th>ผู้รับ</th>
									<th>ผู้ให้บริการ</th>
									<th>เลขติดตาม</th>
									<th>สถานะ</th>
									<th>ค่าจัดส่ง</th>
									<th>วันที่สร้าง</th>
									<th class="text-right">การจัดการ</th>
								</tr>
							</thead>
							<tbody>
								{#each filteredShippings as shipping (shipping._id)}
									<tr>
										<td>
											<span class="font-mono text-sm">{shipping.orderId}</span>
										</td>
										<td>
											<div>
												<p class="font-medium">
													{shipping.shippingAddress.name}
												</p>
												<p class="text-xs text-muted-foreground">
													{shipping.shippingAddress.phone}
												</p>
												<p class="text-xs text-muted-foreground">
													{shipping.shippingAddress.province}
												</p>
											</div>
										</td>
										<td>
											<div>
												<p class="font-medium">
													{shipping.carrier}
												</p>
												<p class="text-xs text-muted-foreground">
													{shipping.method}
												</p>
											</div>
										</td>
										<td>
											{#if shipping.trackingNumber}
												<span class="font-mono text-sm bg-muted px-2 py-1 rounded">
													{shipping.trackingNumber}
												</span>
											{:else}
												<span class="text-muted-foreground">-</span>
											{/if}
										</td>
										<td>
											<Badge color={getStatusBadgeColor(shipping.status)}>
												{getStatusText(shipping.status)}
											</Badge>
										</td>
										<td>
											{formatCurrency(shipping.shippingCost)}
										</td>
										<td class="text-sm text-muted-foreground">
											{formatDate(shipping.createdAt)}
										</td>
										<td class="text-right">
											<div class="flex items-center justify-end gap-2">
												<Button variant="ghost" size="sm" onclick={() => viewShipping(shipping)}>
													<Icon icon="heroicons:eye" class="h-4 w-4" />
												</Button>
												{#if shipping.status !== 'delivered'}
													<Button
														variant="ghost"
														size="sm"
														onclick={() => openUpdateDialog(shipping)}
													>
														<Icon icon="heroicons:pencil" class="h-4 w-4" />
													</Button>
												{/if}
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</Table>
				{/if}
			</div>
		</Card>
</div>
<!-- Dialog ดูรายละเอียด -->
<Dialog open={viewDialogOpen}>
 
		<div class="p-6 max-w-2xl">
			{#if selectedShipping}
				<div class="mb-4">
					<h2 class="text-lg font-bold">รายละเอียดการจัดส่ง</h2>
					<div class="text-sm text-muted-foreground">
						คำสั่งซื้อ {selectedShipping.orderId}
					</div>
				</div>

				<div class="space-y-4">
					<div class="grid grid-cols-2 gap-4">
						<div>
							<p class="text-sm font-medium">ผู้ให้บริการขนส่ง</p>
							<p class="text-sm">{selectedShipping.carrier}</p>
							<p class="text-xs text-muted-foreground">
								{selectedShipping.method}
							</p>
						</div>
						<div>
							<p class="text-sm font-medium">เลขติดตาม</p>
							{#if selectedShipping.trackingNumber}
								<p class="text-sm font-mono">
									{selectedShipping.trackingNumber}
								</p>
							{:else}
								<p class="text-sm text-muted-foreground">ยังไม่มี</p>
							{/if}
						</div>
					</div>

					<div>
						<p class="text-sm font-medium mb-2">ที่อยู่จัดส่ง</p>
						<div class="bg-muted p-3 rounded-lg text-sm">
							<p class="font-medium">
								{selectedShipping.shippingAddress.name}
							</p>
							<p>{selectedShipping.shippingAddress.phone}</p>
							<p>{selectedShipping.shippingAddress.address}</p>
							<p>
								{selectedShipping.shippingAddress.district}
								{selectedShipping.shippingAddress.province}
								{selectedShipping.shippingAddress.postalCode}
							</p>
						</div>
					</div>

					<div class="grid grid-cols-2 gap-4">
						<div>
							<p class="text-sm font-medium">สถานะ</p>
							<Badge color={getStatusBadgeColor(selectedShipping.status)}>
								{getStatusText(selectedShipping.status)}
							</Badge>
						</div>
						<div>
							<p class="text-sm font-medium">ค่าจัดส่ง</p>
							<p class="text-sm font-bold">
								{formatCurrency(selectedShipping.shippingCost)}
							</p>
						</div>
					</div>

					{#if selectedShipping.estimatedDeliveryDate}
						<div>
							<p class="text-sm font-medium">วันที่คาดว่าจะส่งมอบ</p>
							<p class="text-sm">
								{formatDate(selectedShipping.estimatedDeliveryDate)}
							</p>
						</div>
					{/if}

					{#if selectedShipping.actualDeliveryDate}
						<div>
							<p class="text-sm font-medium">วันที่ส่งมอบจริง</p>
							<p class="text-sm">
								{formatDate(selectedShipping.actualDeliveryDate)}
							</p>
						</div>
					{/if}

					{#if selectedShipping.notes}
						<div>
							<p class="text-sm font-medium">หมายเหตุ</p>
							<p class="text-sm whitespace-pre-wrap">
								{selectedShipping.notes}
							</p>
						</div>
					{/if}
				</div>
			{/if}
		</div>
 
</Dialog>

<!-- Dialog อัปเดตการจัดส่ง -->
<Dialog open={updateDialogOpen}>
 
		<div class="p-6">
			{#if selectedShipping}
				<div class="mb-4">
					<h2 class="text-lg font-bold">อัปเดตการจัดส่ง</h2>
					<div class="text-sm text-muted-foreground">
						คำสั่งซื้อ {selectedShipping.orderId}
					</div>
				</div>

				<div class="space-y-4">
					<div class="space-y-2">
						<Label for="tracking" text="เลขติดตามพัสดุ" />
						<Input
							id="tracking"
							placeholder="เลขติดตามพัสดุ"
							bind:value={updateForm.trackingNumber}
						/>
					</div>

					<div class="space-y-2">
						<Label for="status" text="สถานะ" /> 
						<Select
							bind:value={updateForm.status}
							options={[
								{ value: 'preparing', label: 'กำลังเตรียม' },
								{ value: 'shipped', label: 'จัดส่งแล้ว' },
								{ value: 'in_transit', label: 'ระหว่างขนส่ง' },
								{ value: 'delivered', label: 'ส่งมอบแล้ว' },
								{ value: 'failed', label: 'จัดส่งไม่สำเร็จ' },
								{ value: 'returned', label: 'ส่งคืนแล้ว' },
							]}
						/>
					</div>

					<div class="space-y-2">
						<Label for="delivery-date" text="วันที่คาดว่าจะส่งมอบ" /> 
						<input id="delivery-date" type="date" bind:value={updateForm.estimatedDeliveryDate} class="input input-bordered w-full" />
					</div>

					<div class="space-y-2">
						<Label for="notes" text="หมายเหตุ" /> 
						<textarea
							id="notes"
							class="w-full p-2 border rounded-md"
							rows="3"
							placeholder="หมายเหตุเพิ่มเติม..."
							bind:value={updateForm.notes}
						></textarea>
					</div>

				<form
					method="POST"
					action="?/updateShipping"
					use:enhance={() => {
						isLoading = true;
						return async ({ result }) => {
							handleFormResult(result);
						};
					}}
				>
					<input type="hidden" name="shippingId" value={updateForm.shippingId} />
					<input type="hidden" name="trackingNumber" value={updateForm.trackingNumber} />
					<input type="hidden" name="status" value={updateForm.status} />
					<input type="hidden" name="estimatedDeliveryDate" value={updateForm.estimatedDeliveryDate} />
					<input type="hidden" name="notes" value={updateForm.notes} />

					<div class="flex gap-2">
						<button
							type="submit"
							class="btn btn-primary flex-1"
							disabled={isLoading}
						>
							{#if isLoading}
								<span class="loading loading-spinner loading-sm"></span>
							{:else}
								<Icon icon="heroicons:check-circle" class="h-4 w-4 mr-2" />
							{/if}
							อัปเดตการจัดส่ง
						</button>
						<button
							type="button"
							class="btn btn-ghost"
							onclick={() => (updateDialogOpen = false)}
							disabled={isLoading}
						>
							ยกเลิก
						</button>
					</div>
				</form>
				</div>
			{/if}
		</div>
	 
</Dialog>

<!-- Create Shipping Method Dialog -->
{#if methodDialogOpen}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">
				<Icon icon="heroicons:plus" class="w-5 h-5 inline mr-2" />
				เพิ่มวิธีการจัดส่งใหม่
			</h3>
			
			<form
				method="POST"
				action="?/createShippingMethod"
				use:enhance={() => {
					isLoading = true;
					return async ({ result }) => {
						handleFormResult(result);
					};
				}}
			>
				<div class="space-y-4">
					<div class="form-control">
						<label for="shippingName" class="label">
							<span class="label-text">ชื่อวิธีการจัดส่ง *</span>
						</label>
						<input
							id="shippingName"
							type="text"
							name="name"
							bind:value={methodForm.name}
							class="input input-bordered"
							placeholder="เช่น จัดส่งด่วน"
							required
						/>
					</div>

					<div class="form-control">
						<label for="shippingCarrier" class="label">
							<span class="label-text">ผู้ให้บริการขนส่ง *</span>
						</label>
						<input
							id="shippingCarrier"
							type="text"
							name="carrier"
							bind:value={methodForm.carrier}
							class="input input-bordered"
							placeholder="เช่น Kerry Express"
							required
						/>
					</div>

					<div class="grid grid-cols-2 gap-4">
						<div class="form-control">
							<label for="shippingCost" class="label">
								<span class="label-text">ค่าจัดส่ง (บาท) *</span>
							</label>
							<input
								id="shippingCost"
								type="number"
								name="cost"
								bind:value={methodForm.cost}
								class="input input-bordered"
								min="0"
								step="0.01"
								placeholder="0.00"
								required
							/>
						</div>
						
						<div class="form-control">
							<label for="estimatedDays" class="label">
								<span class="label-text">ระยะเวลา (วัน) *</span>
							</label>
							<input
								id="estimatedDays"
								type="number"
								name="estimatedDays"
								bind:value={methodForm.estimatedDays}
								class="input input-bordered"
								min="1"
								placeholder="1"
								required
							/>
						</div>
					</div>

					<div class="form-control">
						<label class="label cursor-pointer">
							<span class="label-text">เปิดใช้งาน</span>
							<input
								type="checkbox"
								name="isActive"
								bind:checked={methodForm.isActive}
								class="checkbox"
							/>
						</label>
					</div>

					<!-- Form Error Display -->
					{#if form?.type === 'createShippingMethod' && form?.message}
						<div class="alert alert-error">
							<Icon icon="mdi:alert-circle" class="w-5 h-5" />
							<span>{form.message}</span>
						</div>
					{/if}
				</div>

				<div class="modal-action">
					<button
						type="submit"
						class="btn btn-primary"
						disabled={isLoading}
					>
						{#if isLoading}
							<span class="loading loading-spinner loading-sm"></span>
						{:else}
							<Icon icon="heroicons:plus" class="w-4 h-4" />
						{/if}
						สร้างวิธีการจัดส่ง
					</button>
					<button
						type="button"
						class="btn btn-ghost"
						onclick={() => {
							methodDialogOpen = false;
							resetMethodForm();
						}}
						disabled={isLoading}
					>
						ยกเลิก
					</button>
				</div>
			</form>
		</div>
	</div>
{/if}
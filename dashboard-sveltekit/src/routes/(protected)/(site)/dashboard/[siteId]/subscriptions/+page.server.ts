import { siteService } from '$lib/services/site';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '$lib/utils/error-handler';
import { LogCategory, logger } from '$lib/utils/logger';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;
  const token = locals.token;

  // Auth check already done in layout
  if (!token) {
    logger.warn(LogCategory.AUTH, 'subscriptions_no_token', 'No token found for subscriptions page', {
      siteId,
    });
    return {
      packages: [],
      subscriptions: [],
      notifications: [],
      error: 'ไม่พบ token การยืนยันตัวตน',
    };
  }

  try {
    logger.info(LogCategory.SYSTEM, 'subscriptions_load_start', 'Loading subscriptions data', {
      siteId,
      userId: locals.user?._id,
    });

    // ดึงข้อมูลแพ็คเกจ
    const packagesResult = await siteService.getSitePackages();

    // ดึงรายการ subscription ของ site
    // const page = url.searchParams.get('page') || '1';
    // const limit = url.searchParams.get('limit') || '10';
    // const status = url.searchParams.get('status') || '';

    return {
      packages: packagesResult.success ? packagesResult.data : [],
      // subscriptions: packagesResult.data [],
      // pagination: packagesResult.success ? packagesResult.data?.pagination : null,
      error: null,
    };
  }
  catch (error) {
    logger.error(LogCategory.SYSTEM, 'subscriptions_load_error', 'Error loading subscriptions data', {
      siteId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return {
      packages: [],
      subscriptions: [],
      notifications: [],
      error: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Renew Subscription - Hybrid Approach
   * Route API + Service Pattern
   */

  rentPackageNow: async ({ request, locals, params, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const { siteId } = params;

    try {
      const formData = await request.formData();
      const subscriptionId = formData.get('subscriptionId') as string;

      logger.info(LogCategory.SYSTEM, 'renew_subscription_attempt', 'User attempting to renew subscription', {
        siteId,
        subscriptionId,
        userId: locals.user?._id,
        clientIP,
        userAgent: userAgent.substring(0, 50) + '...',
      });

      // Basic validation at route level
      if (!subscriptionId?.trim()) {
        logger.warn(LogCategory.SYSTEM, 'renew_subscription_validation_failed', 'Subscription ID missing', {
          siteId,
          userId: locals.user?._id,
        });
        return fail(400, {
          message: 'ไม่พบ subscription ID',
          type: 'renew',
        });
      }

      // Call service for business logic + backend API
      const result = await siteService.rentPackage(
        siteId,
        subscriptionId,
        locals.token!,
      );

      if (!result.success) {
        logger.warn(LogCategory.SYSTEM, 'renew_subscription_failed', 'Renew subscription failed', {
          siteId,
          subscriptionId,
          userId: locals.user?._id,
          error: result.error,
        });

        const userMessage = ErrorHandler.toUserMessage(result.error);
        return fail(400, {
          message: userMessage,
          type: 'renew',
        });
      }

      logger.info(LogCategory.SYSTEM, 'renew_subscription_success', 'Subscription renewed successfully', {
        siteId,
        subscriptionId,
        userId: locals.user?._id,
      });

      return {
        success: true,
        data: result.data,
        message: 'ต่ออายุ subscription สำเร็จ',
        type: 'renew',
      };
    }
    catch (error) {
      logger.error(LogCategory.SYSTEM, 'renew_subscription_exception', 'Exception during renew subscription', {
        siteId,
        userId: locals.user?._id,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        clientIP,
        userAgent,
      });

      const userMessage = ErrorHandler.toUserMessage(error);
      return fail(500, {
        message: userMessage,
        type: 'renew',
      });
    }
  },
};

/**
 * ✅ Cancel Subscription - Hybrid Approach
 * Route API + Service Pattern
 */
//   cancel: async ({ request, locals, params, getClientAddress }) => {
//     const clientIP = getClientAddress();
//     const userAgent = request.headers.get('user-agent') || 'unknown';
//     const { siteId } = params;

//     try {
//       const formData = await request.formData();
//       const subscriptionId = formData.get('subscriptionId') as string;

//       logger.info(LogCategory.SYSTEM, 'cancel_subscription_attempt', 'User attempting to cancel subscription', {
//         siteId,
//         subscriptionId,
//         userId: locals.user?._id,
//         clientIP,
//         userAgent: userAgent.substring(0, 50) + '...',
//       });

//       // Basic validation at route level
//       if (!subscriptionId?.trim()) {
//         logger.warn(LogCategory.SYSTEM, 'cancel_subscription_validation_failed', 'Subscription ID missing', {
//           siteId,
//           userId: locals.user?._id,
//         });
//         return fail(400, {
//           message: 'ไม่พบ subscription ID',
//           type: 'cancel',
//         });
//       }

//       // Call service for business logic + backend API
//       const result = await subscriptionService.cancelSubscription(
//         siteId,
//         subscriptionId,
//         locals.token!,
//       );

//       if (!result.success) {
//         logger.warn(LogCategory.SYSTEM, 'cancel_subscription_failed', 'Cancel subscription failed', {
//           siteId,
//           subscriptionId,
//           userId: locals.user?._id,
//           error: result.error,
//         });

//         const userMessage = ErrorHandler.toUserMessage(result.error);
//         return fail(400, {
//           message: userMessage,
//           type: 'cancel',
//         });
//       }

//       logger.info(LogCategory.SYSTEM, 'cancel_subscription_success', 'Subscription cancelled successfully', {
//         siteId,
//         subscriptionId,
//         userId: locals.user?._id,
//       });

//       return {
//         success: true,
//         data: result.data,
//         message: 'ยกเลิก subscription สำเร็จ',
//         type: 'cancel',
//       };
//     }
//     catch (error) {
//       logger.error(LogCategory.SYSTEM, 'cancel_subscription_exception', 'Exception during cancel subscription', {
//         siteId,
//         userId: locals.user?._id,
//         error: error instanceof Error ? error.message : 'Unknown error',
//         stack: error instanceof Error ? error.stack : undefined,
//         clientIP,
//         userAgent,
//       });

//       const userMessage = ErrorHandler.toUserMessage(error);
//       return fail(500, {
//         message: userMessage,
//         type: 'cancel',
//       });
//     }
//   },

//   /**
//    * ✅ Toggle Auto Renew - Hybrid Approach
//    * Route API + Service Pattern
//    */
//   toggleAutoRenew: async ({ request, locals, params, getClientAddress }) => {
//     const clientIP = getClientAddress();
//     const userAgent = request.headers.get('user-agent') || 'unknown';
//     const { siteId } = params;

//     try {
//       const formData = await request.formData();
//       const subscriptionId = formData.get('subscriptionId') as string;
//       const autoRenew = formData.get('autoRenew') === 'true';

//       logger.info(LogCategory.SYSTEM, 'toggle_auto_renew_attempt', 'User attempting to toggle auto renew', {
//         siteId,
//         subscriptionId,
//         autoRenew,
//         userId: locals.user?._id,
//         clientIP,
//         userAgent: userAgent.substring(0, 50) + '...',
//       });

//       // Basic validation at route level
//       if (!subscriptionId?.trim()) {
//         logger.warn(LogCategory.SYSTEM, 'toggle_auto_renew_validation_failed', 'Subscription ID missing', {
//           siteId,
//           userId: locals.user?._id,
//         });
//         return fail(400, {
//           message: 'ไม่พบ subscription ID',
//           type: 'toggle',
//         });
//       }

//       // Call service for business logic + backend API
//       const result = await subscriptionService.toggleAutoRenew(
//         siteId,
//         subscriptionId,
//         autoRenew,
//         locals.token!,
//       );

//       if (!result.success) {
//         logger.warn(LogCategory.SYSTEM, 'toggle_auto_renew_failed', 'Toggle auto renew failed', {
//           siteId,
//           subscriptionId,
//           autoRenew,
//           userId: locals.user?._id,
//           error: result.error,
//         });

//         const userMessage = ErrorHandler.toUserMessage(result.error);
//         return fail(400, {
//           message: userMessage,
//           type: 'toggle',
//         });
//       }

//       logger.info(LogCategory.SYSTEM, 'toggle_auto_renew_success', 'Auto renew toggled successfully', {
//         siteId,
//         subscriptionId,
//         autoRenew,
//         userId: locals.user?._id,
//       });

//       return {
//         success: true,
//         data: result.data,
//         message: `${autoRenew ? 'เปิด' : 'ปิด'}การต่ออายุอัตโนมัติสำเร็จ`,
//         type: 'toggle',
//       };
//     }
//     catch (error) {
//       logger.error(LogCategory.SYSTEM, 'toggle_auto_renew_exception', 'Exception during toggle auto renew', {
//         siteId,
//         userId: locals.user?._id,
//         error: error instanceof Error ? error.message : 'Unknown error',
//         stack: error instanceof Error ? error.stack : undefined,
//         clientIP,
//         userAgent,
//       });

//       const userMessage = ErrorHandler.toUserMessage(error);
//       return fail(500, {
//         message: userMessage,
//         type: 'toggle',
//       });
//     }
//   },

//   /**
//    * ✅ Mark Notification As Read - Hybrid Approach
//    * Route API + Service Pattern
//    */
//   markNotificationAsRead: async ({ request, locals, getClientAddress }) => {
//     const clientIP = getClientAddress();
//     const userAgent = request.headers.get('user-agent') || 'unknown';

//     try {
//       const formData = await request.formData();
//       const notificationId = formData.get('notificationId') as string;

//       logger.info(
//         LogCategory.SYSTEM,
//         'mark_notification_read_attempt',
//         'User attempting to mark notification as read',
//         {
//           notificationId,
//           userId: locals.user?._id,
//           clientIP,
//           userAgent: userAgent.substring(0, 50) + '...',
//         },
//       );

//       // Basic validation at route level
//       if (!notificationId?.trim()) {
//         logger.warn(LogCategory.SYSTEM, 'mark_notification_read_validation_failed', 'Notification ID missing', {
//           userId: locals.user?._id,
//         });
//         return fail(400, {
//           message: 'ไม่พบ notification ID',
//           type: 'notification',
//         });
//       }

//       // Call service for business logic + backend API
//       const result = await subscriptionService.markNotificationAsRead(
//         notificationId,
//         locals.token!,
//       );

//       if (!result.success) {
//         logger.warn(LogCategory.SYSTEM, 'mark_notification_read_failed', 'Mark notification as read failed', {
//           notificationId,
//           userId: locals.user?._id,
//           error: result.error,
//         });

//         const userMessage = ErrorHandler.toUserMessage(result.error);
//         return fail(400, {
//           message: userMessage,
//           type: 'notification',
//         });
//       }

//       logger.info(
//         LogCategory.SYSTEM,
//         'mark_notification_read_success',
//         'Notification marked as read successfully',
//         {
//           notificationId,
//           userId: locals.user?._id,
//         },
//       );

//       return {
//         success: true,
//         data: result.data,
//         message: 'อ่านการแจ้งเตือนสำเร็จ',
//         type: 'notification',
//       };
//     }
//     catch (error) {
//       logger.error(
//         LogCategory.SYSTEM,
//         'mark_notification_read_exception',
//         'Exception during mark notification as read',
//         {
//           userId: locals.user?._id,
//           error: error instanceof Error ? error.message : 'Unknown error',
//           stack: error instanceof Error ? error.stack : undefined,
//           clientIP,
//           userAgent,
//         },
//       );

//       const userMessage = ErrorHandler.toUserMessage(error);
//       return fail(500, {
//         message: userMessage,
//         type: 'notification',
//       });
//     }
//   },
// };

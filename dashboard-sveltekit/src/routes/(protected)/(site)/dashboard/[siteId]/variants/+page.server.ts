import {
  sanitizeVariantData,
  validateCreateVariantData,
  validateDeleteVariantData,
  validateUpdateVariantData,
} from '$lib/schemas/variant.schema';
import { productService } from '$lib/services/product';
import { variantService } from '$lib/services/variant';
import { error, fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, url }) => {
  try {
    if (!locals.token || !locals.user) {
      throw error(401, 'กรุณาเข้าสู่ระบบ');
    }

    const { siteId } = params;
    const productId = url.searchParams.get('productId');

    if (!siteId || !productId) {
      throw error(400, 'Site ID และ Product ID จำเป็น');
    }

    // Fetch product details
    const productResponse = await productService.getProduct(productId, siteId, locals.token!);

    if (!productResponse.success) {
      throw error(404, productResponse.error || 'ไม่พบสินค้า');
    }

    return {
      product: productResponse.data,
      productId,
    };
  }
  catch (err) {
    console.error('Error loading product variants page:', err);
    if (err instanceof Response) {
      throw err;
    }
    throw error(500, 'เกิดข้อผิดพลาดในการโหลดข้อมูล');
  }
};

export const actions: Actions = {
  addVariant: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'addVariant',
        });
      }

      const { siteId } = params;
      if (!siteId) {
        return fail(400, {
          message: 'Site ID จำเป็น',
          type: 'addVariant',
        });
      }

      const formData = await request.formData();
      const productId = formData.get('productId') as string;

      if (!productId) {
        return fail(400, {
          message: 'Product ID จำเป็น',
          type: 'addVariant',
        });
      }

      // Parse attributes from form
      const attributes: Record<string, string> = {};
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('attributes.')) {
          const attrName = key.replace('attributes.', '');
          attributes[attrName] = value as string;
        }
      }

      const rawData = {
        name: formData.get('name') as string,
        sku: formData.get('sku') as string,
        price: formData.get('price') as string,
        stock: formData.get('stock') as string,
        attributes,
        isActive: formData.get('isActive') === 'on',
      };

      // Sanitize data
      const sanitizedData = sanitizeVariantData(rawData);

      // Validate using schema
      const validationResult = validateCreateVariantData(sanitizedData);
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'addVariant',
        });
      }

      // Call service for business logic
      const result = await variantService.createVariant(
        validationResult.data!,
        productId,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'addVariant',
        });
      }

      return {
        success: true,
        variant: result.data,
        message: 'เพิ่มตัวเลือกสินค้าสำเร็จ',
        type: 'addVariant',
      };
    }
    catch (err) {
      console.error('Error adding variant:', err);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเพิ่มตัวเลือกสินค้า',
        type: 'addVariant',
      });
    }
  },

  updateVariant: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'updateVariant',
        });
      }

      const { siteId } = params;
      if (!siteId) {
        return fail(400, {
          message: 'Site ID จำเป็น',
          type: 'updateVariant',
        });
      }

      const formData = await request.formData();
      const productId = formData.get('productId') as string;
      const variantId = formData.get('variantId') as string;

      if (!productId) {
        return fail(400, {
          message: 'Product ID จำเป็น',
          type: 'updateVariant',
        });
      }

      if (!variantId) {
        return fail(400, {
          message: 'Variant ID จำเป็น',
          type: 'updateVariant',
        });
      }

      // Parse attributes from form
      const attributes: Record<string, string> = {};
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('attributes.')) {
          const attrName = key.replace('attributes.', '');
          attributes[attrName] = value as string;
        }
      }

      const rawData = {
        _id: variantId,
        name: formData.get('name') as string,
        sku: formData.get('sku') as string,
        price: formData.get('price') as string,
        stock: formData.get('stock') as string,
        attributes,
        isActive: formData.get('isActive') === 'on',
      };

      // Sanitize data
      const sanitizedData = sanitizeVariantData(rawData);

      // Validate using schema
      const validationResult = validateUpdateVariantData(sanitizedData);
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'updateVariant',
        });
      }

      // Call service for business logic
      const result = await variantService.updateVariant(
        validationResult.data!,
        variantId,
        productId,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'updateVariant',
        });
      }

      return {
        success: true,
        variant: result.data,
        message: 'อัปเดตตัวเลือกสินค้าสำเร็จ',
        type: 'updateVariant',
      };
    }
    catch (err) {
      console.error('Error updating variant:', err);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตตัวเลือกสินค้า',
        type: 'updateVariant',
      });
    }
  },

  deleteVariant: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, {
          message: 'กรุณาเข้าสู่ระบบ',
          type: 'deleteVariant',
        });
      }

      const { siteId } = params;
      if (!siteId) {
        return fail(400, {
          message: 'Site ID จำเป็น',
          type: 'deleteVariant',
        });
      }

      const formData = await request.formData();
      const productId = formData.get('productId') as string;
      const variantId = formData.get('variantId') as string;

      if (!productId) {
        return fail(400, {
          message: 'Product ID จำเป็น',
          type: 'deleteVariant',
        });
      }

      // Validate using schema
      const validationResult = validateDeleteVariantData({ variantId });
      if (!validationResult.success) {
        return fail(400, {
          message: validationResult.error,
          type: 'deleteVariant',
        });
      }

      // Call service for business logic
      const result = await variantService.deleteVariant(
        variantId,
        productId,
        siteId,
        locals.token,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'deleteVariant',
        });
      }

      return {
        success: true,
        message: 'ลบตัวเลือกสินค้าสำเร็จ',
        type: 'deleteVariant',
      };
    }
    catch (err) {
      console.error('Error deleting variant:', err);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบตัวเลือกสินค้า',
        type: 'deleteVariant',
      });
    }
  },
};

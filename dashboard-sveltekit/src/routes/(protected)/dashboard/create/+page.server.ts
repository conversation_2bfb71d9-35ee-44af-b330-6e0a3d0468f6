import { validateCheckDiscountData } from '$lib/schemas/';
import type { CheckDiscountData, CheckDomainData, CreateSiteData } from '$lib/schemas/site.schema';
import { discountService } from '$lib/services/discount';
import { siteService } from '$lib/services/site';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

/**
 * ✅ HYBRID APPROACH: SvelteKit Route API + Service Pattern
 * - Route-level validation and error handling
 * - Service layer for business logic
 * - Consistent fail() responses
 * - Type-safe data flow
 * - Progressive enhancement with use:enhance
 */

export const load: PageServerLoad = async () => {
  try {
    const packagesResult = await siteService.getSitePackages();
    return packagesResult.data || [];
  }
  catch (error) {
    console.error('Error loading packages:', error);
    return fail(500, {
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ',
      type: 'create',
    });
  }
};

export const actions: Actions = {
  /**
   * ✅ Check domain availability
   */
  checkDomain: async ({ request }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const domainData: CheckDomainData = {
        typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: data.get('subDomain')?.toString(),
        mainDomain: data.get('mainDomain')?.toString(),
        customDomain: data.get('customDomain')?.toString(),
      };

      // Call site service (validation included)
      const result = await siteService.checkDomain(domainData);
      console.log('result', result);

      if (!result.success) {
        return fail(400, {
          statusMessage: 'ล้มเหลว!',
          message: result.message,
          type: 'domain',
        });
      }

      if (!result.data?.available) {
        return fail(400, {
          statusMessage: 'โดเมนไม่ว่าง!',
          message: 'โดเมนนี้ถูกใช้งานแล้ว',
          type: 'domain',
        });
      }

      return {
        success: true,
        data: result.data,
        statusMessage: result.statusMessage || 'โดเมนว่าง',
        message: 'สามารถใช้งานได้',
        type: 'domain',
      };
    }
    catch (error) {
      console.error('Check domain error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน',
        type: 'domain',
      });
    }
  },

  /**
   * ✅ Check discount code with Zod validation
   */
  checkDiscount: async ({ request }) => {
    try {
      const data = await request.formData();
      // console.log('data', data);

      // Extract form data
      const discountData: CheckDiscountData = {
        discountCode: data.get('discountCode')?.toString()?.trim() || '',
        orderAmount: Number(data.get('orderAmount')) || 0,
        // ไม่ต้องส่ง target ถ้าไม่จำเป็น
      };
      console.log('discountData', discountData);

      // ✅ Zod Schema Validation
      const validation = validateCheckDiscountData(discountData);
      if (!validation.success) {
        return fail(400, {
          message: 'ข้อมูลไม่ถูกต้อง',
          errors: validation.errors,
          type: 'discount',
        });
      }

      // ใช้ validated data
      const validatedData = validation.data!;

      // Call subscription service for discount validation
      const result = await discountService.validateDiscount(validatedData.discountCode);
      console.log('result', result);

      if (!result.success) {
        return fail(400, {
          statusMessage: 'ล้มเหลว!',
          message: result.error || 'รหัสส่วนลดไม่ถูกต้องหรือหมดอายุ',
          type: 'discount',
        });
      }

      return {
        success: true,
        data: result.data,
        message: 'รหัสส่วนลดถูกต้อง',
        type: 'discount',
      };
    }
    catch (error) {
      console.error('Check discount error:', error);
      return fail(500, {
        statusMessage: 'ล้มเหลว!',
        message: 'เกิดข้อผิดพลาดในการตรวจสอบรหัสส่วนลด',
        type: 'discount',
      });
    }
  },

  /**
   * ✅ Create new site with enhanced validation
   */
  createSite: async ({ request, locals }) => {
    try {
      const data = await request.formData();
      console.log('data', data);
      console.log('locals', locals);

      // Extract form data
      const siteData: CreateSiteData = {
        siteName: data.get('siteName')?.toString() || '',
        typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: data.get('subDomain')?.toString()?.trim(),
        mainDomain: data.get('mainDomain')?.toString()?.trim(),
        customDomain: data.get('customDomain')?.toString()?.trim(),
        packageType: data.get('packageType')?.toString()?.trim() || '',
      };

      // Call site service (additional validation included)
      const result = await siteService.createSiteWithValidation(
        siteData,
        locals.user?.moneyPoint || 0,
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          statusMessage: 'ล้มเหลว!',
          message: result.error,
          type: 'create',
        });
      }
      // else {
      //   throw redirect(303, `/dashboard/${result.data?._id}`);
      // }
      // redirect(303, `/dashboard/${result.data?._id}`);
      return {
        success: true,
        data: result.data,
        message: 'สร้างเว็บไซต์สำเร็จ',
        type: 'create',
        statusMessage: 'สำเร็จ!',
      };
    }
    catch (error) {
      // Don't log redirects as errors
      // if (error instanceof Response) {
      //   throw error; // Re-throw redirects
      // }

      console.error('Create site error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์',
        type: 'create',
      });
    }
  },
};

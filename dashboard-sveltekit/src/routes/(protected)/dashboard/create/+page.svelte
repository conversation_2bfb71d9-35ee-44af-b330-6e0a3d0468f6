<script lang="ts">
	import Icon from '@iconify/svelte';
	import { _ } from 'svelte-i18n';
	import { enhance } from '$app/forms'; 
	import SEO from '$lib/components/layout/SEO.svelte';
	import {Badge, Button, Card, Input, Radio,Select} from '$lib/components/ui'; 
	import { goto } from '$app/navigation';  
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';
	import type { DiscountData } from '$lib/types/discount';
 

	const { data }: { data: { packages: any[], user: any } } = $props();
	console.log('data', data.packages);

	const user = $derived(data.user);
	const packages = data.packages;

	// ข้อมูลเริ่มต้น
	let siteName = $state('MyCoffee');
	let typeDomain: 'subdomain' | 'custom' = $state('subdomain');
	let subDomain = $state('mycoffee');
	let mainDomain = $state('is1.shop');
	let customDomain = $state('');
	let packageType = $state('monthly');
	let discountCode = $state('');
	let isCheckingDomain = $state(false);
	let isLoading = $state(false);
	let domainAvailable = $state(false);
	let domainChecked = $state(false);
	let isCheckingDiscount = $state(false);
	let discountValid = $state(false);
	let discountInfo = $state<DiscountData | null>(null);
	// โดเมนหลักที่รองรับ
	const availableDomains = ['is1.shop'];

	// ฟังก์ชันหาแพ็คเกจที่เลือก
	function getSelectedPackage() {
		if (!Array.isArray(packages) || packages.length === 0) {
			return null;
		}
		return packages.find((pkg: any) => pkg.id === packageType);
	}

	const currentPrice = $derived(getSelectedPackage()?.moneyPoint || 0);

	// คำนวณราคาหลังส่วนลด
	const finalPrice = $derived(() => {
		if (discountValid && discountInfo) {
			if (discountInfo.type === 'percentage') {
				return Math.max(0, currentPrice - (currentPrice * discountInfo.value) / 100);
			} else {
				return Math.max(0, currentPrice - discountInfo.value);
			}
		}
		return currentPrice;
	});

	const discountAmount = $derived(() => currentPrice - finalPrice());

	// ✅ Hybrid Approach: Enhanced form handling functions

	/**
	 * ✅ Handle form results consistently
	 */
	function handleFormResult(result: any, actionType: string) {
		console.log(`${actionType} result Site:`, result);

		if (result.type === 'success') {
			// Handle success based on action type
			switch (actionType) {
				case 'checkDomain':
					domainAvailable = result.data?.data?.available === true;
					domainChecked = true;
					showSuccess(
						domainAvailable ? 'โดเมนว่าง' : 'โดเมนไม่ว่าง',
						result.message ||
							(domainAvailable ? 'โดเมนนี้สามารถใช้งานได้' : 'โดเมนนี้ถูกใช้งานแล้ว'),
						{ timer: 3000, showConfirmButton: false }
					);
					break;

				case 'checkDiscount':
					discountValid = true;
					discountInfo = result.data.data?.discount || result.data;
					// showSuccess('โค้ดส่วนลดถูกต้อง', result.message);
					break;

				case 'createSite':
					isLoading = false;
				   showSuccess( result.data.statusMessage || 'สำเร็จ!', result.data.message || 'สร้างเว็บไซต์สำเร็จ');
					// Redirect to the new site dashboard
					 result.data.data?._id ? goto(`/dashboard/${result.data.data?._id}`) : goto('/dashboard');
					break;
			}
		} else if (result.type === 'failure') { 
			const errorMessage = result.data?.message || result.data?.error || 'เกิดข้อผิดพลาด';

			switch (actionType) {
				case 'checkDomain':
					domainAvailable = false;
					domainChecked = true;
					showError(result.data?.statusMessage || 'ล้มเหลว!', errorMessage);
					break;

				case 'checkDiscount':
					discountValid = false;
					discountInfo = null;
					showError(result.data?.statusMessage || 'ล้มเหลว!', errorMessage);
					break;

				case 'createSite':
					showError(result.data?.statusMessage || 'ล้มเหลว!', errorMessage);
					break;

				default:
					showError(errorMessage);
			}
		}
	}

	/**
	 * ✅ Cancel discount code
	 */
	function cancelDiscount() {
		discountValid = false;
		discountInfo = null;
		discountCode = '';
		// showSuccess('ยกเลิกส่วนลดแล้ว', '', {
		// 	timer: 2000,
		// 	showConfirmButton: false,
		// });
	}

	/**
	 * ✅ Validate form before submission
	 */
	function validateCreateSiteForm(): string | null {
		if (!siteName?.trim()) {
			return 'กรุณากรอกชื่อเว็บไซต์';
		}

		if (siteName.length < 2) {
			return 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 2 ตัวอักษร';
		}

		if (!packageType) {
			return 'กรุณาเลือกแพ็คเกจ';
		}

		if (typeDomain === 'subdomain') {
			if (!subDomain?.trim()) {
				return 'กรุณากรอกซับโดเมน';
			}
			if (!mainDomain) {
				return 'กรุณาเลือกโดเมนหลัก';
			}
		} else if (typeDomain === 'custom') {
			if (!customDomain?.trim()) {
				return 'กรุณากรอกโดเมนส่วนตัว';
			}
		}

		if (!domainChecked) {
			return 'กรุณาตรวจสอบโดเมนก่อนสร้างเว็บไซต์';
		}

		if (!domainAvailable) {
			return 'โดเมนที่เลือกไม่สามารถใช้งานได้';
		}

		return null;
	}
</script>

<SEO
	title="สร้างเว็บไซต์ใหม่ - Dashboard"
	description="สร้างเว็บไซต์ร้านค้าออนไลน์ของคุณ เลือกโดเมน แพ็คเกจ และเริ่มต้นขายของออนไลน์ได้ทันที"
	keywords="สร้างเว็บไซต์, ร้านค้าออนไลน์, โดเมน, แพ็คเกจ, อีคอมเมิร์ส"
	url="/dashboard/create-site"
	noindex={true}
/>

<!-- {JSON.stringify(data)} -->
<div class="space-y-4">
	<!-- Header -->
	<div class="text-center mb-8">
		<div class="flex items-center justify-center gap-3 mb-4">
			<Icon icon="solar:shop-bold" class="size-10 text-primary" />
			<h1 class="text-3xl font-bold text-primary">สร้างเว็บไซต์ใหม่</h1>
		</div>
		<p class="text-lg text-base-content/70 max-w-2xl mx-auto">
			สร้างเว็บไซต์ร้านค้าออนไลน์ของคุณในไม่กี่ขั้นตอน เลือกโดเมน แพ็คเกจ
			และเริ่มต้นขายของออนไลน์ได้ทันที
		</p>
	</div>

	<div class="w-full flex flex-col lg:flex-row gap-4">
		<Card title="ข้อมูลเว็บไซต์" titleIcon="solar:document-add-bold" size="full" class="space-y-4">
			<!-- ชื่อเว็บไซต์ -->
			<Input
				id="siteName"
				variant="bordered"
				label="ชื่อเว็บไซต์"
				icon="solar:pen-bold"
				bind:value={siteName}
				placeholder="ชื่อร้านค้าของคุณ เช่น MyCoffee"
				required={true}
			/>

			<div class="divider"></div>

			<!-- ประเภทโดเมน -->
			<Radio
				label="ประเภทโดเมน"
				required={true}
				options={[
					{
						value: 'subdomain',
						label: 'ซับโดเมนฟรี',
						description: 'yourname.is1.shop',
						icon: 'solar:shield-network-broken',
					},
					{
						value: 'custom',
						label: 'โดเมนส่วนตัว',
						description: 'yourdomain.com',
						icon: 'solar:shield-network-line-duotone',
					},
				]}
				value={typeDomain}
				onChange={value => (typeDomain = value as 'subdomain' | 'custom')}
				name="doamin-type"
			/>

			<div class="space-y-4">
				<!-- โดเมน -->
				{#if typeDomain === 'subdomain'}
					<div class="space-y-4">
						<div class="flex flex-col sm:flex-row gap-3">
							<div class="flex-1">
								<Input
									label="ซับโดเมน"
									icon="solar:shield-network-broken"
									bind:value={subDomain}
									placeholder="yourname"
									required
								/>
							</div>

							<div class="flex-1">
								<div class="form-control w-full">
									<label for="mainDomain" class="label">
										<span class="label-text font-medium">โดเมนหลัก</span>
									</label>
									<Select
										options={availableDomains.map(domain => ({
											value: domain,
											label: domain,
										}))}
										value={mainDomain}
										placeholder="เลือกโดเมนหลัก"
										onChange={value => (mainDomain = value as string)}
										class="w-full"
										id="mainDomain"
									/>
								</div>
							</div>
						</div>
					</div>
				{:else}
					<div class="space-y-4">
						<Input
							label="โดเมน"
							icon="solar:shield-network-line-duotone"
							bind:value={customDomain}
							placeholder="yourdomain.com"
							required
						/>

						<div class="alert alert-warning alert-soft">
							<Icon icon="solar:danger-triangle-bold" class="w-5 h-5" />
							<div>
								<div class="font-semibold">หมายเหตุ</div>
								<div class="text-sm">คุณต้องตั้งค่า DNS ของโดเมนให้ชี้มาที่เซิร์ฟเวอร์ของเรา</div>
								<div class="overflow-x-auto mt-5">
									<table class="table table-sm">
										<thead>
											<tr class="bg-base-200">
												<th>Type</th>
												<th>Name</th>
												<th>Content</th>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td>A</td>
												<td>{customDomain ? customDomain : '@'}</td>
												<td>76.76.21.21</td>
											</tr>
											<tr>
												<td>CNAME</td>
												<td>*</td>
												<td>cname.vercel-dns.com</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				{/if}

				{#if typeDomain === 'subdomain' || customDomain}
					<div class="alert alert-info alert-soft">
						<Icon icon="solar:info-circle-bold" class="size-8" />
						<div>
							<div class="font-semibold">โดเมนที่จะใช้งาน</div>
							<div class="text-sm">
								{typeDomain === 'subdomain' ? `${subDomain}.${mainDomain}` : customDomain}
							</div>
						</div>
					</div>
				{/if}

				<!-- ✅ ตรวจสอบโดเมน - Hybrid Approach -->
				<form
					method="POST"
					action="?/checkDomain"
					use:enhance={() => {
						isCheckingDomain = true;
						return async ({ result }) => {
							isCheckingDomain = false;
							handleFormResult(result, 'checkDomain');
						};
					}}
				>
					<input type="hidden" name="typeDomain" value={typeDomain} />
					<input type="hidden" name="subDomain" value={subDomain} />
					<input type="hidden" name="mainDomain" value={mainDomain} />
					<input type="hidden" name="customDomain" value={customDomain} />

					<div class="flex gap-2">
						<Button
							icon="solar:search-bold"
							label="ตรวจสอบโดเมน"
							type="submit"
							outline={true}
							disabled={isCheckingDomain}
							class="flex-1"
							loading={isCheckingDomain}
						/>

						{#if domainChecked}
							<Badge
								label={domainAvailable ? 'ว่าง' : 'ไม่ว่าง'}
								variant={domainAvailable ? 'solid' : 'solid'}
								color={domainAvailable ? 'success' : 'error'}
							/>
						{/if}
					</div>
				</form>
			</div>

			<div class="divider"></div>

			<!-- แพ็คเกจ -->
			{#if Array.isArray(packages) && packages.length > 0}
				<Radio
					label="แพ็คเกจ"
					required={true}
					options={packages.map((pkg: any) => ({
						value: pkg.id,
						label: pkg.name,
						description: `${pkg.days >= 36500 ? `฿${pkg.moneyPoint.toLocaleString()} / ไม่จำกัด` : `฿${pkg.moneyPoint.toLocaleString()} / ${pkg.days} วัน`}`,
						icon: 'solar:calendar-bold',
					}))}
					cols={3}
					value={packageType}
					onChange={value => (packageType = value as string)}
					name="package-type"
				/>
			{:else}
				<div class="alert alert-warning">
					<Icon icon="solar:danger-triangle-bold" class="w-5 h-5" />
					<div>
						<div class="font-semibold">ไม่สามารถโหลดแพ็คเกจได้</div>
						<div class="text-sm">กรุณาลองใหม่อีกครั้ง</div>
					</div>
				</div>
			{/if}
		</Card>

		<!-- Summary -->
		<Card title="ข้อมูลแพ็คเกจ" titleIcon="solar:info-circle-bold" size="md" class="space-y-5">
			<div class="space-y-5">
				<div class="space-y-3">
					<div class="flex justify-between">
						<span class="text-base-content/70">แพ็คเกจ:</span>
						<span class="font-medium">{getSelectedPackage()?.name || 'ไม่ระบุ'}</span>
					</div>
					{#if !Array.isArray(packages) || packages.length === 0}
						<div class="alert alert-error alert-sm">
							<Icon icon="solar:danger-triangle-bold" class="w-4 h-4" />
							<div class="text-sm">ไม่สามารถโหลดแพ็คเกจได้</div>
						</div>
					{/if}
					<div class="flex justify-between">
						<span class="text-base-content/70">ประเภทโดเมน:</span>
						<span class="font-medium">{typeDomain === 'subdomain' ? 'ซับโดเมน' : 'โดเมนเอง'}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-base-content/70">โดเมนที่เลือก:</span>
						<span class="font-medium text-info">
							{typeDomain === 'subdomain'
								? `${subDomain}.${mainDomain}`
								: customDomain || 'ยังไม่ได้กรอก'}
						</span>
					</div>
					{#if domainChecked}
						<div class="flex justify-between">
							<span class="text-base-content/70">สถานะโดเมน:</span>
							<Badge
								label={domainAvailable ? 'ว่าง - ใช้งานได้' : 'ไม่ว่าง - ถูกใช้แล้ว'}
								variant="solid"
								color={domainAvailable ? 'success' : 'error'}
								size="sm"
							/>
						</div>
					{/if}
					<div class="flex justify-between">
						<span class="text-base-content/70">ราคา:</span>
						<span class="font-medium text-primary">฿{currentPrice.toLocaleString()}</span>
					</div>

					<div class="flex gap-1 items-end w-full">
						<form
							method="POST"
							action="?/checkDiscount"
							use:enhance={() => {
								isCheckingDiscount = true;
								return async ({ result }) => {
									isCheckingDiscount = false;
									handleFormResult(result, 'checkDiscount');
								};
							}}
							class="flex gap-1 items-end w-full"
						>
							<!-- Hidden field สำหรับส่ง orderAmount -->
							<input type="hidden" name="orderAmount" value={finalPrice()} />

							<Input
								size="sm"
								label="โค้ดส่วนลด"
								icon="solar:bag-heart-line-duotone"
								bind:value={discountCode}
								placeholder="รหัสส่วนลด เช่น WELCOME, SAVE10"
								disabled={isCheckingDiscount || discountValid}
								name="discountCode"
							/>
							 {#if !discountValid}
								<Button
									label="ตรวจสอบ"
									icon="solar:discount-bold"
									color="primary"
									size="sm"
									outline={true}
									loading={isCheckingDiscount}
									disabled={isCheckingDiscount || !discountCode.trim()}
									type="submit"
								/>
							{:else}
								<Button
									label="ยกเลิก"
									icon="solar:close-circle-bold"
									color="error"
									size="sm"
									outline={true}
									onclick={cancelDiscount}
									type="button"
								/>
							{/if}
						</form>
					</div>

					{#if discountValid && discountInfo}
						<div class="alert alert-success">
							<Icon icon="solar:check-circle-bold" class="w-5 h-5" />
							<div class="flex-1">
								<div class="font-semibold">
									{JSON.stringify(discountInfo)}
									โค้ดส่วนลด: {discountInfo?.code}
								</div>
								<div class="text-sm">
									ส่วนลด {discountInfo?.value}{discountInfo?.type === 'percentage' ? '%' : '฿'}
								</div>

								<!-- แสดงข้อมูลเพิ่มเติมถ้ามี -->
								{#if discountInfo?.name}
									<div class="text-xs text-success-content/70 mt-1">
										{discountInfo.name}
									</div>
								{/if}

								{#if discountInfo?.description}
									<div class="text-xs text-success-content/70">
										{discountInfo.description}
									</div>
								{/if}

								{#if discountInfo?.conditions?.minOrderAmount && discountInfo.conditions.minOrderAmount > 0}
									<div class="text-xs text-success-content/70">
										ขั้นต่ำ ฿{discountInfo.conditions.minOrderAmount.toLocaleString()}
									</div>
								{/if}

								{#if discountInfo?.maxDiscountAmount && discountInfo.maxDiscountAmount > 0}
									<div class="text-xs text-success-content/70">
										ส่วนลดสูงสุด ฿{discountInfo.maxDiscountAmount.toLocaleString()}
									</div>
								{/if}

								{#if discountInfo?.conditions?.firstTimeOnly}
									<div class="text-xs text-success-content/70">สำหรับลูกค้าใหม่เท่านั้น</div>
								{/if}

								{#if discountInfo?.endDate}
									<div class="text-xs text-success-content/70">
										ใช้ได้ถึง {new Date(discountInfo.endDate).toLocaleDateString('th-TH')}
									</div>
								{/if}
							</div>
						</div>
					{/if}


					{#if discountValid && discountInfo}
						<div class="flex justify-between">
							<span class="text-base-content/70">ส่วนลด:</span>
							<span class="font-medium text-success">-฿{discountAmount().toLocaleString()}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-base-content/70">ราคาสุทธิ:</span>
							<span class="font-medium text-primary">฿{finalPrice().toLocaleString()}</span>
						</div>
					{/if}
					
				</div>

				<!-- ✅ สร้างเว็บไซต์ - Hybrid Approach -->
				<form
					method="POST"
					action="?/createSite"
					use:enhance={() => {
						// Client-side validation before submission
						// const validationError = validateCreateSiteForm();
						// if (validationError) {
						// 	showError('ข้อมูลไม่ถูกต้อง', validationError);
						// 	return async () => {}; // Cancel submission
						// }

						isLoading = true;
						showLoading('กำลังสร้างเว็บไซต์...');

						return async ({ result }) => {
							console.log('createSite result', result);
							// isLoading = false;
							handleFormResult(result, 'createSite');

							// Handle successful site creation redirect
							// if (result.type === 'success') {
							// 	// SvelteKit will handle the redirect automatically
							// 	// No need to manually call goto()
							// }
						};
					}}
				>
					<!-- Hidden fields for form data -->
					<input type="hidden" name="siteName" value={siteName} />
					<input type="hidden" name="typeDomain" value={typeDomain} />
					<input type="hidden" name="subDomain" value={subDomain} />
					<input type="hidden" name="mainDomain" value={mainDomain} />
					<input type="hidden" name="customDomain" value={customDomain} />
					<input type="hidden" name="packageType" value={packageType} />
					{#if discountValid && discountCode}
						<input type="hidden" name="discountCode" value={discountCode} />
					{/if}

					<div class="flex gap-3">
						<Button
							type="submit"
							color="primary"
							size="xl"
							loading={isLoading}
							disabled={isLoading || !domainChecked || !domainAvailable}
							class="flex-1"
							icon="solar:shop-bold"
							label="สร้างเว็บไซต์ (฿{finalPrice().toLocaleString()})"
						/>
					</div>
				</form>
			</div>
		</Card>
	</div>
</div>

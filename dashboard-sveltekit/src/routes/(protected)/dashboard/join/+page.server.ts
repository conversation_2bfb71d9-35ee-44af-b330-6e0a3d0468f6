import { acceptInvitationSchema, joinWithCodeSchema, rejectInvitationSchema } from '$lib/schemas/invitation.schema';
import { invitationService } from '$lib/services/invitation';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

/**
 * ✅ HYBRID APPROACH: SvelteKit Route API + Service Pattern
 * - Route-level validation และ error handling
 * - Service สำหรับ business logic
 * - Consistent fail() responses
 * - Type-safe data flow
 */

export const load: PageServerLoad = async ({ locals }) => {
  // Auth check already done in layout
  try {
    // ✅ Load invitations using service
    const result = await invitationService.getReceivedInvitations(locals.token!);

    if (!result.success) {
      return {
        invitations: [],
        error: result.error || 'เกิดข้อผิดพลาดในการดึงข้อมูลคำเชิญ',
      };
    }

    // ✅ Log invitation data for debugging
    const invitations = result.data?.invitations || [];
    console.log(
      '📋 Loaded invitations:',
      invitations.map(inv => ({
        _id: inv._id,
        siteName: inv.siteName,
        status: inv.status,
        isValidId: inv._id && inv._id.length > 0,
      })),
    );

    return {
      invitations,
    };
  }
  catch (error) {
    console.error('Error loading invitations:', error);
    return {
      invitations: [],
      error: 'เกิดข้อผิดพลาดในการดึงข้อมูลคำเชิญ',
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Join with invite code
   */
  joinWithCode: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // ✅ Route-level validation
      const validationResult = joinWithCodeSchema.safeParse({
        inviteCode: data.get('inviteCode')?.toString()?.trim(),
      });
      console.log('validationResult', validationResult);

      if (!validationResult.success) {
        return fail(400, {
          error: validationResult.error.issues[0]?.message || 'กรุณากรอกโค้ดเชิญ',
          type: 'joinCode',
        });
      }

      const { inviteCode } = validationResult.data;

      // ✅ Call service for business logic
      const result = await invitationService.joinWithCode(inviteCode, locals.token!);
      console.log('result', result);

      if (!result.success) {
        return fail(400, {
          error: result.error || 'โค้ดเชิญไม่ถูกต้องหรือหมดอายุแล้ว',
          type: 'joinCode',
        });
      }

      return {
        success: true,
        message: result.data?.message || 'เข้าร่วมทีมงานด้วยโค้ดเชิญสำเร็จ',
        type: 'joinCode',
      };
    }
    catch (error) {
      console.error('Join with code error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน',
        type: 'joinCode',
      });
    }
  },

  /**
   * ✅ Accept invitation
   */
  acceptInvitation: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // ✅ Route-level validation
      const validationResult = acceptInvitationSchema.safeParse({
        invitationId: data.get('invitationId')?.toString(),
      });

      if (!validationResult.success) {
        return fail(400, {
          error: validationResult.error.issues[0]?.message || 'ไม่พบรหัสคำเชิญ',
          type: 'accept',
        });
      }

      const { invitationId } = validationResult.data;

      // ✅ Log invitation ID for debugging
      console.log('🔍 Accepting invitation ID:', invitationId);
      console.log('📋 Is valid custom ID:', invitationId && invitationId.length > 0);

      // ✅ Call service for business logic
      const result = await invitationService.acceptInvitation(invitationId, locals.token!);

      if (!result.success) {
        return fail(400, {
          error: result.error || 'เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน',
          type: 'accept',
        });
      }

      return {
        success: true,
        message: result.data?.message || 'เข้าร่วมทีมงานเรียบร้อยแล้ว',
        type: 'accept',
        invitationId,
      };
    }
    catch (error) {
      console.error('Accept invitation error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน',
        type: 'accept',
      });
    }
  },

  /**
   * ✅ Reject invitation
   */
  rejectInvitation: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // ✅ Route-level validation
      const validationResult = rejectInvitationSchema.safeParse({
        invitationId: data.get('invitationId')?.toString(),
      });

      if (!validationResult.success) {
        return fail(400, {
          error: validationResult.error.issues[0]?.message || 'ไม่พบรหัสคำเชิญ',
          type: 'reject',
        });
      }

      const { invitationId } = validationResult.data;

      // ✅ Call service for business logic
      const result = await invitationService.rejectInvitation(invitationId, locals.token!);

      if (!result.success) {
        return fail(400, {
          error: result.error || 'เกิดข้อผิดพลาดในการปฏิเสธคำเชิญ',
          type: 'reject',
        });
      }

      return {
        success: true,
        message: result.data?.message || 'ปฏิเสธคำเชิญเรียบร้อยแล้ว',
        type: 'reject',
        invitationId,
      };
    }
    catch (error) {
      console.error('Reject invitation error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการปฏิเสธคำเชิญ',
        type: 'reject',
      });
    }
  },
};

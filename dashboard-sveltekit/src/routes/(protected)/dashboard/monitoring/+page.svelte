<script lang="ts">
	import Icon from '@iconify/svelte';
	import { goto } from '$app/navigation';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';

	const monitoringPages = [
		{
			title: 'Token Monitoring',
			description: 'ติดตามสถานะและประสิทธิภาพของ authentication tokens',
			icon: 'mdi:shield-key',
			href: '/dashboard/monitoring/tokens',
			color: 'text-primary',
		},
		{
			title: 'reCAPTCHA Monitoring',
			description: 'ติดตามสถิติและประสิทธิภาพของ reCAPTCHA v3',
			icon: 'mdi:shield-check',
			href: '/dashboard/monitoring/recaptcha',
			color: 'text-success',
		},
		{
			title: 'Security Monitor',
			description: 'ติดตามกิจกรรมความปลอดภัยและการเข้าถึงที่ผิดปกติ',
			icon: 'mdi:security',
			href: '/dashboard/monitoring/security',
			color: 'text-warning',
		},
		{
			title: 'System Health',
			description: 'ติดตามสถานะระบบและประสิทธิภาพโดยรวม',
			icon: 'mdi:heart-pulse',
			href: '/dashboard/monitoring/system',
			color: 'text-error',
		},
	];

	function navigateTo(href: string) {
		goto(href);
	}
</script>

<div class="container mx-auto p-6 space-y-4">
	<!-- Header -->
	<div>
		<h1 class="text-3xl font-bold">System Monitoring</h1>
		<p class="text-base-content/70 mt-2">ติดตามและจัดการระบบความปลอดภัยและประสิทธิภาพ</p>
	</div>

	<!-- Monitoring Cards -->
	<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
		{#each monitoringPages as page}
			<Card
				variant="default"
				shadow="lg"
				class="hover:shadow-xl transition-all duration-300 cursor-pointer group"
				onclick={() => navigateTo(page.href)}
			>
				<div class="flex items-start gap-4">
					<div class="flex-shrink-0">
						<div
							class="w-12 h-12 rounded-lg bg-base-200 flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
						>
							<Icon icon={page.icon} class="w-6 h-6 {page.color}" />
						</div>
					</div>
					<div class="flex-1 min-w-0">
						<h3 class="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
							{page.title}
						</h3>
						<p class="text-base-content/70 text-sm mb-4">
							{page.description}
						</p>
						<Button
							color="primary"
							variant="outline"
							size="sm"
							onclick={() => navigateTo(page.href)}
						>
							<Icon icon="mdi:arrow-right" class="w-4 h-4 mr-2" />
							เข้าดู
						</Button>
					</div>
				</div>
			</Card>
		{/each}
	</div>

	<!-- Quick Stats Overview -->
	<Card title="ภาพรวมระบบ" variant="default">
		<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
			<div class="stat">
				<div class="stat-figure text-primary">
					<Icon icon="mdi:shield-check" class="w-8 h-8" />
				</div>
				<div class="stat-title">reCAPTCHA Status</div>
				<div class="stat-value text-sm text-success">Active</div>
				<div class="stat-desc">v3 Score-based</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-secondary">
					<Icon icon="mdi:shield-key" class="w-8 h-8" />
				</div>
				<div class="stat-title">Token System</div>
				<div class="stat-value text-sm text-success">Healthy</div>
				<div class="stat-desc">Auto-rotation enabled</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-accent">
					<Icon icon="mdi:security" class="w-8 h-8" />
				</div>
				<div class="stat-title">Security Level</div>
				<div class="stat-value text-sm text-success">High</div>
				<div class="stat-desc">All systems secure</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-info">
					<Icon icon="mdi:heart-pulse" class="w-8 h-8" />
				</div>
				<div class="stat-title">System Health</div>
				<div class="stat-value text-sm text-success">Optimal</div>
				<div class="stat-desc">All services running</div>
			</div>
		</div>
	</Card>

	<!-- Recent Activity -->
	<Card title="กิจกรรมล่าสุด" variant="default">
		<div class="space-y-3">
			<div class="flex items-center gap-3 p-3 bg-base-100 rounded-lg">
				<Icon icon="mdi:shield-check" class="w-5 h-5 text-success" />
				<div class="flex-1">
					<p class="text-sm font-medium">reCAPTCHA verification successful</p>
					<p class="text-xs text-base-content/60">Action: signup, Score: 0.87</p>
				</div>
				<span class="text-xs text-base-content/60">2 นาทีที่แล้ว</span>
			</div>
			<div class="flex items-center gap-3 p-3 bg-base-100 rounded-lg">
				<Icon icon="mdi:shield-key" class="w-5 h-5 text-primary" />
				<div class="flex-1">
					<p class="text-sm font-medium">Token auto-refresh completed</p>
					<p class="text-xs text-base-content/60">User session extended</p>
				</div>
				<span class="text-xs text-base-content/60">5 นาทีที่แล้ว</span>
			</div>
			<div class="flex items-center gap-3 p-3 bg-base-100 rounded-lg">
				<Icon icon="mdi:security" class="w-5 h-5 text-warning" />
				<div class="flex-1">
					<p class="text-sm font-medium">Security scan completed</p>
					<p class="text-xs text-base-content/60">No threats detected</p>
				</div>
				<span class="text-xs text-base-content/60">15 นาทีที่แล้ว</span>
			</div>
		</div>
	</Card>
</div>

import type { ChangePasswordData, UpdateProfileData } from '$lib/schemas/user.schema';
import { authService } from '$lib/services/auth';
import { userService } from '$lib/services/user';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
  // Auth check already done in layout
  try {
    // ใช้ authService เพื่อให้ได้ข้อมูลเหมือนตอน signin
    const result: any = await authService.getCurrentUser(locals.token!);

    if (!result.success) {
      console.error('Failed to load user profile:', result.error);
      return {
        user: locals.user, // Fallback to locals
        error: result.error,
      };
    }

    return {
      user: result.data?.user,
    };
  }
  catch (error) {
    console.error('Profile load error:', error);
    return {
      user: locals.user, // Fallback to locals
      error: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Update user profile - Hybrid Approach
   * Route API + Service Pattern
   */
  updateProfile: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract and validate form data
      const profileData: UpdateProfileData = {
        firstName: data.get('firstName')?.toString()?.trim() || undefined,
        lastName: data.get('lastName')?.toString()?.trim() || undefined,
      };

      // Basic validation at route level
      if (!profileData.firstName && !profileData.lastName) {
        return fail(400, {
          message: 'กรุณากรอกข้อมูลอย่างน้อย 1 ฟิลด์',
          type: 'profile',
        });
      }

      // Call user service (business logic + backend API)
      const result = await userService.updateProfile(profileData, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'profile',
        });
      }

      return {
        success: true,
        user: result.data,
        message: 'อัปเดตโปรไฟล์สำเร็จ',
        type: 'profile',
      };
    }
    catch (error) {
      console.error('Update profile error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์',
        type: 'profile',
      });
    }
  },

  /**
   * ✅ Change password - Hybrid Approach
   * Route API + Service Pattern
   */
  changePassword: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract and validate form data
      const passwordData: ChangePasswordData = {
        currentPassword: data.get('currentPassword')?.toString()?.trim() || '',
        newPassword: data.get('newPassword')?.toString()?.trim() || '',
        confirmPassword: data.get('confirmPassword')?.toString()?.trim() || undefined,
      };

      // Basic validation at route level
      if (!passwordData.currentPassword) {
        return fail(400, {
          message: 'กรุณากรอกรหัสผ่านปัจจุบัน',
          type: 'password',
        });
      }

      if (!passwordData.newPassword) {
        return fail(400, {
          message: 'กรุณากรอกรหัสผ่านใหม่',
          type: 'password',
        });
      }

      if (passwordData.newPassword.length < 6) {
        return fail(400, {
          message: 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร',
          type: 'password',
        });
      }

      // Call user service (business logic + backend API)
      const result = await userService.changePassword(passwordData, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'password',
        });
      }

      return {
        success: true,
        message: 'เปลี่ยนรหัสผ่านสำเร็จ',
        type: 'password',
      };
    }
    catch (error) {
      console.error('Change password error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน',
        type: 'password',
      });
    }
  },

  /**
   * ✅ Update avatar - Hybrid Approach
   * Route API + Service Pattern
   */
  updateAvatar: async ({ request, locals }) => {
    try {
      const data = await request.formData();
      const avatarFile = data.get('avatar') as File;

      // Basic validation at route level
      if (!avatarFile || avatarFile.size === 0) {
        return fail(400, {
          message: 'กรุณาเลือกไฟล์รูปภาพ',
          type: 'avatar',
        });
      }

      // File size validation (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (avatarFile.size > maxSize) {
        return fail(400, {
          message: 'ไฟล์รูปภาพต้องมีขนาดไม่เกิน 5MB',
          type: 'avatar',
        });
      }

      // File type validation
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(avatarFile.type)) {
        return fail(400, {
          message: 'รองรับเฉพาะไฟล์ JPG, PNG และ WebP เท่านั้น',
          type: 'avatar',
        });
      }

      // Call user service (business logic + backend API)
      const result = await userService.updateAvatar({ avatar: avatarFile }, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'avatar',
        });
      }

      return {
        success: true,
        avatar: result.data?.avatar,
        message: 'อัปเดตรูปโปรไฟล์สำเร็จ',
        type: 'avatar',
      };
    }
    catch (error) {
      console.error('Update avatar error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตรูปโปรไฟล์',
        type: 'avatar',
      });
    }
  },

  /**
   * ✅ Refresh user profile data - Hybrid Approach
   * Route API + Service Pattern - ใช้ authService เพื่อให้ได้ข้อมูลเหมือนตอน signin
   */
  refreshProfile: async ({ locals }) => {
    try {
      // Basic validation at route level
      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการยืนยันตัวตน',
          type: 'refresh',
        });
      }

      // Call auth service (เหมือนตอน signin) เพื่อให้ได้ข้อมูลเต็ม
      const result: any = await authService.getCurrentUser(locals.token);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'refresh',
        });
      }

      return {
        success: true,
        user: result.data?.user,
        message: 'รีโหลดข้อมูลโปรไฟล์สำเร็จ',
        type: 'refresh',
      };
    }
    catch (error) {
      console.error('Refresh profile error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการรีโหลดข้อมูลโปรไฟล์',
        type: 'refresh',
      });
    }
  },

  /**
   * ✅ Load user activity - Hybrid Approach
   * Route API + Service Pattern
   */
  loadActivity: async ({ locals }) => {
    try {
      // Basic validation at route level
      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการยืนยันตัวตน',
          type: 'activity',
        });
      }

      // Call user service for activity data
      const result = await userService.getUserActivity(locals.token);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'activity',
        });
      }

      return {
        success: true,
        activities: result.data,
        message: 'โหลดข้อมูลกิจกรรมสำเร็จ',
        type: 'activity',
      };
    }
    catch (error) {
      console.error('Load activity error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการโหลดข้อมูลกิจกรรม',
        type: 'activity',
      });
    }
  },

  /**
   * ✅ Load user stats - Hybrid Approach
   * Route API + Service Pattern
   */
  loadStats: async ({ locals }) => {
    try {
      // Basic validation at route level
      if (!locals.token) {
        return fail(401, {
          message: 'ไม่พบ token สำหรับการยืนยันตัวตน',
          type: 'stats',
        });
      }

      // Call user service for stats data
      const result = await userService.getUserStats(locals.token);

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'stats',
        });
      }

      return {
        success: true,
        stats: result.data,
        message: 'โหลดข้อมูลสถิติสำเร็จ',
        type: 'stats',
      };
    }
    catch (error) {
      console.error('Load stats error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการโหลดข้อมูลสถิติ',
        type: 'stats',
      });
    }
  },

  /**
   * ✅ Test refresh token - Hybrid Approach
   * Route API + Service Pattern (could be moved to auth service)
   */
  testToken: async ({ cookies }) => {
    try {
      // Basic validation at route level
      const refreshToken = cookies.get('refreshToken');
      console.log('[testToken] Refresh token from cookie:', refreshToken);
      console.log('[testToken] Refresh token type:', typeof refreshToken);
      console.log('[testToken] Refresh token length:', refreshToken?.length);

      if (!refreshToken) {
        console.error('[testToken] No refresh token found in cookies');
        return fail(400, {
          message: 'ไม่พบ refresh token',
          type: 'token',
        });
      }

      // ตรวจสอบ Token Rotation format (hex string ความยาว 128)
      if (refreshToken.length !== 128) {
        console.error('[testToken] Invalid Token Rotation format - length:', refreshToken.length);
        return fail(400, {
          message: 'Refresh token ไม่ถูกต้อง: รูปแบบ Token Rotation ไม่ถูกต้อง',
          type: 'token',
        });
      }

      console.log('[testToken] Using Token Rotation System for refresh');
      console.log('[testToken] Sending refresh token to backend:', {
        tokenPreview: refreshToken.substring(0, 50) + '...',
        tokenLength: refreshToken.length,
      });

      // Call auth service for token refresh (business logic)
      const result = await authService.refreshToken(refreshToken);

      if (!result.success) {
        return fail(400, {
          message: result.error || 'Refresh token ล้มเหลว',
          type: 'token',
        });
      }

      if (!result.data) {
        return fail(400, {
          message: 'ไม่พบข้อมูลจาก token refresh',
          type: 'token',
        });
      }

      console.log('[testToken] Token refresh successful:', {
        hasUser: !!result.data.user,
        hasToken: !!result.data.token,
        hasRefreshToken: !!result.data.refreshToken,
        newRefreshTokenLength: result.data.refreshToken?.length,
      });

      // Update cookies (route-level responsibility)
      const cookieOptions = {
        path: '/',
        httpOnly: true,
        secure: import.meta.env.NODE_ENV === 'production',
        sameSite: 'lax' as const,
      };

      cookies.set('auth_token', result.data.token, {
        ...cookieOptions,
        maxAge: 60 * 60 * 24 * 7, // 7 วัน
      });

      if (result.data.refreshToken) {
        console.log('[testToken] Setting new rotated refresh token');
        cookies.set('refreshToken', result.data.refreshToken, {
          ...cookieOptions,
          maxAge: 60 * 60 * 24 * 30, // 30 วัน
        });
      }

      return {
        success: true,
        user: result.data.user,
        message: 'Token Rotation System refresh สำเร็จ',
        type: 'token',
      };
    }
    catch (error) {
      console.error('Refresh token error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการ refresh token',
        type: 'token',
      });
    }
  },
};

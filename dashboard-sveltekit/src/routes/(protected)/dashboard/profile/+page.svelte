<script lang="ts">
	import Icon from '@iconify/svelte';
	import { fade } from 'svelte/transition';
	import { enhance } from '$app/forms';
	import { page } from '$app/state';
	import SEO from '$lib/components/layout/SEO.svelte';

	import Badge from '$lib/components/ui/Badge.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import FileUpload from '$lib/components/ui/FileUpload.svelte';
	import Image from '$lib/components/ui/Image.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import ActivityList from '$lib/components/activity/ActivityList.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { showError, showInfo, showSuccess } from '$lib/utils/sweetalert';

	const { data } = $props();

	const user = $derived(data?.user || authStore.user);
	let isLoading = $state(false);
	let isRefreshing = $state(false);
	let isChangingPassword = $state(false);
	let isUploadingAvatar = $state(false);

	// ข้อมูลโปรไฟล์
	const profileData = $state({
		firstName: '',
		lastName: '',
		email: '',
		avatar: '',
		cover: '',
		moneyPoint: 0,
		goldPoint: 0,
		isEmailVerified: false,
		role: 'user',
		status: 'active',
		createdAt: '',
		updatedAt: '',
	});

	// ข้อมูลเปลี่ยนรหัสผ่าน
	const passwordData = $state({
		currentPassword: '',
		newPassword: '',
		confirmPassword: '',
	});

	// ข้อมูลอัปโหลดรูป
	let avatarFiles = $state<File[]>([]);

	// ข้อมูลประวัติและกิจกรรม
	let activityData = $state<Array<{
		_id: string;
		type: string;
		title: string;
		description: string;
		createdAt: string;
		icon: string;
		color: string;
		priority: 'low' | 'medium' | 'high' | 'critical';
		status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
		entityType: string;
		entityId: string;
		entityName?: string;
		metadata?: Record<string, any>;
	}>>([]);

	// สถิติผู้ใช้
	let userStats = $state({
		totalLogins: 0,
		totalPoints: 0,
		joinedDays: 0,
		lastActive: new Date().toISOString(),
		moneyPoints: 0,
		goldPoints: 0,
		isEmailVerified: false,
		role: 'user',
		status: 'active',
	});

	// ฟังก์ชันโหลดข้อมูลจริง
	async function loadRealData() {
		try {
			console.log('Loading real data for user:', user); // Debug log
			
			// สร้างข้อมูลกิจกรรมจากข้อมูล user ที่มีอยู่
			const activities = [];

			// กิจกรรมการสมัครสมาชิก
			const createdAt = user?.createdAt || (user?._id ? new Date() : null);
			if (createdAt) {
				activities.push({
					_id: '1',
					type: 'user_signup',
					title: 'สมัครสมาชิก',
					description: 'สร้างบัญชีใหม่',
					createdAt: createdAt.toISOString(),
					icon: 'mdi:account-plus',
					color: 'text-blue-500',
					priority: 'medium',
					status: 'completed',
					entityType: 'user',
					entityId: user?._id || '1',
					entityName: user?.firstName + ' ' + user?.lastName,
				});
			}

			// กิจกรรมการยืนยันอีเมล
			if (user?.isEmailVerified && user?.updatedAt) {
				activities.push({
					_id: '2',
					type: 'user_email_verify',
					title: 'ยืนยันอีเมล',
					description: 'ยืนยันอีเมลเรียบร้อยแล้ว',
					createdAt: new Date(user.updatedAt).toISOString(),
					icon: 'mdi:email-check',
					color: 'text-green-500',
					priority: 'medium',
					status: 'completed',
					entityType: 'user',
					entityId: user?._id || '2',
					entityName: user?.firstName + ' ' + user?.lastName,
				});
			}

			// กิจกรรมการอัปเดตโปรไฟล์
			if (user?.updatedAt && user?.updatedAt > user?.createdAt) {
				activities.push({
					_id: '3',
					type: 'user_profile_update',
					title: 'อัปเดตข้อมูลโปรไฟล์',
					description: 'แก้ไขข้อมูลส่วนตัว',
					createdAt: new Date(user.updatedAt).toISOString(),
					icon: 'mdi:account-edit',
					color: 'text-purple-500',
					priority: 'medium',
					status: 'completed',
					entityType: 'user',
					entityId: user?._id || '3',
					entityName: user?.firstName + ' ' + user?.lastName,
				});
			}

			// กิจกรรมการเข้าสู่ระบบล่าสุด
			activities.push({
				_id: '4',
				type: 'user_login',
				title: 'เข้าสู่ระบบ',
				description: 'เข้าสู่ระบบล่าสุด',
				createdAt: new Date().toISOString(),
				icon: 'mdi:login',
				color: 'text-green-500',
				priority: 'medium',
				status: 'completed',
				entityType: 'user',
				entityId: user?._id || '4',
				entityName: user?.firstName + ' ' + user?.lastName,
			});

			// เพิ่มกิจกรรมเริ่มต้นถ้าไม่มีข้อมูล
			if (activities.length === 0) {
				activities.push({
					_id: '1',
					type: 'user_signup',
					title: 'ยินดีต้อนรับ',
					description: 'เริ่มต้นใช้งานระบบ',
					createdAt: new Date().toISOString(),
					icon: 'mdi:hand-wave',
					color: 'text-blue-500',
					priority: 'medium',
					status: 'completed',
					entityType: 'user',
					entityId: user?._id || '1',
					entityName: user?.firstName + ' ' + user?.lastName,
				});
			}

			// เรียงลำดับตามเวลา (ใหม่สุดก่อน)
			activityData = activities.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) as any;
			
			console.log('Generated activities:', activityData); // Debug log

			// สร้างข้อมูลสถิติจากข้อมูล user
			if (user) {
				const joinedDate = new Date(user.createdAt || new Date());
				const now = new Date();
				const joinedDays = Math.floor((now.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));

				userStats = {
					totalLogins: user.loginCount || 1,
					totalPoints: (user.moneyPoint || 0) + (user.goldPoint || 0),
					joinedDays: Math.max(1, joinedDays),
					lastActive: user.lastLoginAt || user.updatedAt || user.createdAt || new Date().toISOString(),
					moneyPoints: user.moneyPoint || 0,
					goldPoints: user.goldPoint || 0,
					isEmailVerified: user.isEmailVerified || false,
					role: user.role || 'user',
					status: user.status || 'active',
				};
				
				console.log('Generated stats:', userStats); // Debug log
			}
		} catch (error) {
			console.error('Error loading real data:', error);
		}
	}

	// State สำหรับ UI
	let activeTab: 'profile' | 'password' | 'activity' | 'stats' | 'settings' = $state('profile');
	let showImageUpload = $state(false);
	let showPasswordModal = $state(false);

	// อัปเดตข้อมูลเมื่อ user เปลี่ยน
	$effect(() => {
		if (user) {
			console.log('User data:', user); // Debug log
			console.log('User createdAt:', user.createdAt); // Debug log
			console.log('User updatedAt:', user.updatedAt); // Debug log
			console.log('User isEmailVerified:', user.isEmailVerified); // Debug log
			
			profileData.firstName = user.firstName || '';
			profileData.lastName = user.lastName || '';
			profileData.email = user.email || '';
			profileData.avatar = user.avatar || '';
			profileData.cover = user.cover || '';
			profileData.moneyPoint = user.moneyPoint || 0;
			profileData.goldPoint = user.goldPoint || 0;
			profileData.isEmailVerified = user.isEmailVerified || false;
			profileData.role = user.role || 'user';
			profileData.status = user.status || 'active';
			profileData.createdAt = user.createdAt || '';
			profileData.updatedAt = user.updatedAt || '';

			// โหลดข้อมูลจริงเมื่อมี user
			loadRealData();
		}
	});

	// ฟังก์ชันจัดการผลลัพธ์จาก server actions
	function handleActionResult(result: any) {
		console.log('handleActionResult', result);
		if (result.type === 'success') {
			// อัปเดตข้อมูลใน auth store ถ้ามี user data
			if (result.data?.user) {
				authStore.updateUser(result.data.user);
			}
			// อัปเดตข้อมูลใน profileData ถ้ามี user data
			if (result.data?.user) {
				Object.assign(profileData, result.data.user);
			}
			// อัปเดต avatar ถ้ามี
			if (result.data?.avatar) {
				profileData.avatar = result.data.avatar;
			}
			showSuccess(result.data?.message || 'ดำเนินการสำเร็จ!');
		} else if (result.type === 'failure') {
			showError(result.data?.message || 'เกิดข้อผิดพลาด');
		}
	}

	// ฟังก์ชันจัดการ form submission
	function handleFormSubmit() {
		isLoading = true;
	}

	function handleFormResult(result: any) {
		isLoading = false;
		handleActionResult(result);
	}

	function handlePasswordSubmit() {
		isChangingPassword = true;
	}

	function handlePasswordResult(result: any) {
		isChangingPassword = false;
		handleActionResult(result);
		// Clear form after success
		if (result.type === 'success') {
			passwordData.currentPassword = '';
			passwordData.newPassword = '';
			passwordData.confirmPassword = '';
			showPasswordModal = false;
		}
	}

	function handleAvatarSubmit() {
		isUploadingAvatar = true;
	}

	function handleAvatarResult(result: any) {
		isUploadingAvatar = false;
		handleActionResult(result);
		// Clear file after success
		if (result.type === 'success') {
			avatarFiles = [];
			showImageUpload = false;
			// อัปเดต avatar ใน profileData ถ้ามี
			if (result.data?.avatar) {
				profileData.avatar = result.data.avatar;
			}
		}
	}

	function handleFieldChange(field: keyof typeof profileData, value: string | number | boolean) {
		(profileData as any)[field] = value;
	}

	function handlePasswordFieldChange(field: keyof typeof passwordData, value: string) {
		(passwordData as any)[field] = value;
	}

	function handleAvatarFilesSelected(files: File[]) {
		avatarFiles = files;
	}

	function handleAvatarFileRemoved(file: File, index: number) {
		avatarFiles = avatarFiles.filter((_, i) => i !== index);
	}

	function handleAvatarError(error: string) {
		showError('ข้อผิดพลาด', error);
	}

	function validatePasswordForm() {
		if (!passwordData.currentPassword) return 'กรุณากรอกรหัสผ่านปัจจุบัน';
		if (!passwordData.newPassword) return 'กรุณากรอกรหัสผ่านใหม่';
		if (passwordData.newPassword.length < 6) return 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร';
		if (passwordData.newPassword !== passwordData.confirmPassword) return 'รหัสผ่านใหม่ไม่ตรงกัน';
		return null;
	}

	const isPasswordFormValid = $derived(!validatePasswordForm());
</script>

<SEO
	title="โปรไฟล์ - จัดการข้อมูลส่วนตัว"
	description="จัดการข้อมูลส่วนตัว อัปเดตโปรไฟล์ เปลี่ยนรหัสผ่าน และทดสอบ refresh token"
	keywords="โปรไฟล์, ข้อมูลส่วนตัว, เปลี่ยนรหัสผ่าน, refresh token, authentication"
	url="/dashboard/profile"
	noindex={true}
/>

<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<!-- Profile Header with Cover & Avatar -->
	<div class="relative mb-8 animate-fade-in">
		<!-- Cover Image -->
		<div
			class="relative h-48 md:h-64 bg-gradient-to-r from-primary to-secondary rounded-t-2xl overflow-hidden"
		>
			{#if profileData.cover}
				<Image
					publicId={profileData.cover}
					width={1600}
					height={600}
					cover={true}
					center={true}
					alt="Cover Image"
					class="w-full h-full object-cover"
					lazy
				/>
			{:else}
				<div
					class="w-full h-full bg-gradient-to-br from-primary/80 via-secondary/60 to-accent/40 flex items-center justify-center"
				>
					<Icon icon="mdi:image" class="w-16 h-16 text-white/50" />
				</div>
			{/if}

			<!-- Cover Upload Button -->
			<button
				class="absolute top-4 right-4 btn btn-sm btn-circle bg-black/20 border-none text-white hover:bg-black/40"
				onclick={() => (showImageUpload = !showImageUpload)}
			>
				<Icon icon="mdi:camera" class="w-4 h-4" />
			</button>
		</div>

		<!-- Profile Info Section -->
		<div class="relative bg-base-100 rounded-b-2xl shadow-xl p-6">
			<!-- Avatar -->
			<div class="absolute -top-16 left-6">
				<div class="relative">
					<div
						class="w-32 h-32 rounded-full border-4 border-base-100 shadow-xl overflow-hidden bg-base-200"
					>
						{#if profileData.avatar}
							<Image
								publicId={profileData.avatar}
								alt="Avatar"
								width={128}
								height={128}
								cover={true}
								center={true}
								class="w-full h-full object-cover"
								lazy
							/>
						{:else}
							<div
								class="w-full h-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center"
							>
								<Icon icon="mdi:account" class="w-16 h-16 text-white" />
							</div>
						{/if}
					</div>

					<!-- Avatar Upload Button -->
					<button
						class="absolute bottom-2 right-2 btn btn-xs btn-circle btn-primary"
						onclick={() => (showImageUpload = !showImageUpload)}
					>
						<Icon icon="mdi:camera" class="w-3 h-3" />
					</button>
				</div>
			</div>

			<!-- Profile Header Info -->
			<div class="ml-40">
				<div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
					<div>
						{#if profileData.firstName || profileData.lastName}
							<h1 class="text-3xl font-bold text-base-content mb-2">
								{profileData.firstName}
								{profileData.lastName}
							</h1>
						{/if}
						<div class="flex items-center gap-4 text-base-content/70">
							<div class="flex items-center gap-2">
								<Icon icon="mdi:email" class="w-4 h-4" />
								<span>{profileData.email}</span>
								{#if profileData.isEmailVerified}
									<Badge label="ยืนยันแล้ว" color="success" size="sm" />
								{/if}
							</div>
						</div>
						<div class="flex items-center gap-4 mt-2 text-sm text-base-content/60">
							<div class="flex items-center gap-1">
								<Icon icon="mdi:calendar" class="w-4 h-4" />
								<span
									>เข้าร่วมเมื่อ {profileData.createdAt
										? new Date(profileData.createdAt).toLocaleDateString('th-TH')
										: 'ไม่ระบุ'}</span
								>
							</div>
							<div class="flex items-center gap-1">
								<Icon icon="mdi:clock" class="w-4 h-4" />
								<span>ออนไลน์ล่าสุด {userStats.lastActive ? new Date(userStats.lastActive).toLocaleString('th-TH') : 'ไม่ระบุ'}</span>
							</div>
						</div>
					</div>

					<!-- Quick Stats -->
					<div class="flex gap-4">
						<div class="text-center">
							<div class="text-2xl font-bold text-primary">
								{userStats.moneyPoints?.toLocaleString() || '0'}
							</div>
							<div class="text-xs text-base-content/60">Money Points</div>
						</div>
						<div class="text-center">
							<div class="text-2xl font-bold text-warning">
								{userStats.goldPoints?.toLocaleString() || '0'}
							</div>
							<div class="text-xs text-base-content/60">Gold Points</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Tab Navigation -->
	<div class="tabs tabs-boxed mb-6 animate-fade-in bg-base-100 rounded-lg p-1 w-fit mx-auto">
		<button
			class="tab {activeTab === 'profile' ? 'tab-active' : ''}"
			onclick={() => (activeTab = 'profile')}
		>
			<Icon icon="mdi:account" class="w-4 h-4 mr-2" />
			ข้อมูลโปรไฟล์
		</button>
		<button
			class="tab {activeTab === 'password' ? 'tab-active' : ''}"
			onclick={() => (activeTab = 'password')}
		>
			<Icon icon="mdi:lock" class="w-4 h-4 mr-2" />
			เปลี่ยนรหัสผ่าน
		</button>
		<button
			class="tab {activeTab === 'activity' ? 'tab-active' : ''}"
			onclick={() => (activeTab = 'activity')}
		>
			<Icon icon="mdi:history" class="w-4 h-4 mr-2" />
			ประวัติกิจกรรม
		</button>
		<button
			class="tab {activeTab === 'stats' ? 'tab-active' : ''}"
			onclick={() => (activeTab = 'stats')}
		>
			<Icon icon="mdi:chart-line" class="w-4 h-4 mr-2" />
			สถิติ
		</button>
		<button
			class="tab {activeTab === 'settings' ? 'tab-active' : ''}"
			onclick={() => (activeTab = 'settings')}
		>
			<Icon icon="mdi:cog" class="w-4 h-4 mr-2" />
			การตั้งค่า
		</button>
	</div>

	<!-- Image Upload Modal -->
	{#if showImageUpload}
		<div
			class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in"
			transition:fade
		>
			<div class="bg-base-100 rounded-2xl p-6 max-w-md w-full mx-4">
				<div class="flex items-center justify-between mb-4">
					<h3 class="text-lg font-semibold">อัปโหลดรูปโปรไฟล์</h3>
					<button class="btn btn-sm btn-circle btn-ghost" onclick={() => (showImageUpload = false)}>
						<Icon icon="mdi:close" class="w-4 h-4" />
					</button>
				</div>

				<form
					method="POST"
					action="?/updateAvatar"
					enctype="multipart/form-data"
					use:enhance={() => {
						handleAvatarSubmit();
						return async ({ result }) => {
							handleAvatarResult(result);
						};
					}}
				>
					<div class="space-y-4">
						<FileUpload
							multiple={false}
							accept="image/*"
							maxSize={5 * 1024 * 1024}
							preview={true}
							previewSize="w-16 h-16"
							placeholder="ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์"
							onFilesSelected={handleAvatarFilesSelected}
							onFileRemoved={handleAvatarFileRemoved}
							onError={handleAvatarError}
						/>

						<div class="flex gap-2">
							<Button 
								type="submit" 
								color="primary" 
								loading={isUploadingAvatar}
								disabled={isUploadingAvatar || avatarFiles.length === 0}
								block
							>
								<Icon icon="mdi:upload" class="w-4 h-4 mr-2" />
								อัปโหลด
							</Button>
							<Button variant="ghost" onclick={() => (showImageUpload = false)}>ยกเลิก</Button>
						</div>
					</div>
				</form>
			</div>
		</div>
	{/if}

	<!-- Tab Content -->
	<div class="animate-fade-in">
		{#if activeTab === 'profile'}
			<!-- Profile Tab Content -->
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<!-- Profile Form -->
				<Card title="แก้ไขข้อมูลโปรไฟล์" size="full">
					<form
						method="POST"
						action="?/updateProfile"
						use:enhance={() => {
							handleFormSubmit();
							return async ({ result }) => {
								handleFormResult(result);
							};
						}}
					>
						<div class="space-y-4">
							<Input
								type="text"
								name="firstName"
								value={profileData.firstName}
								label="ชื่อ"
								placeholder="ชื่อ"
								icon="mdi:account"
								onchange={e => handleFieldChange('firstName', (e.target as HTMLInputElement).value)}
							/>

							<Input
								type="text"
								name="lastName"
								value={profileData.lastName}
								label="นามสกุล"
								placeholder="นามสกุล"
								icon="mdi:account"
								onchange={e => handleFieldChange('lastName', (e.target as HTMLInputElement).value)}
							/>

							<Input
								type="email"
								value={profileData.email}
								label="อีเมล"
								placeholder="<EMAIL>"
								icon="mdi:email"
								disabled
							/>

							<div class="flex items-center gap-2">
								<span class="text-sm text-base-content/70">สถานะอีเมล:</span>
								<Badge
									label={profileData.isEmailVerified ? 'ยืนยันแล้ว' : 'ยังไม่ยืนยัน'}
									color={profileData.isEmailVerified ? 'success' : 'warning'}
									size="sm"
								/>
							</div>

							<Button type="submit" color="primary" loading={isLoading} disabled={isLoading} block>
								<Icon icon="mdi:content-save" class="w-4 h-4 mr-2" />
								บันทึกการเปลี่ยนแปลง
							</Button>
						</div>
					</form>
				</Card>

				<!-- Account Info -->
				<Card title="ข้อมูลบัญชี" size="full">
					<div class="space-y-4">
						<!-- Account Details -->
						<div class="space-y-3">
							<div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
								<span class="text-sm text-base-content/70">บทบาท:</span>
								<Badge label={profileData.role} color="primary" size="sm" />
							</div>

							<div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
								<span class="text-sm text-base-content/70">สถานะบัญชี:</span>
								<Badge
									label={profileData.status === 'active' ? 'ใช้งาน' : 'ระงับการใช้งาน'}
									color={profileData.status === 'active' ? 'success' : 'error'}
									size="sm"
								/>
							</div>

							<div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
								<span class="text-sm text-base-content/70">User ID:</span>
								<span class="text-xs font-mono text-base-content/80">
									{user?._id || 'ไม่ระบุ'}
								</span>
							</div>
						</div>

						<!-- Quick Actions -->
						<div class="space-y-2">
							<Button 
								variant="outline" 
								color="primary" 
								block
								onclick={() => (activeTab = 'password')}
							>
								<Icon icon="mdi:lock" class="w-4 h-4 mr-2" />
								เปลี่ยนรหัสผ่าน
							</Button>
							<Button 
								variant="outline" 
								color="secondary" 
								block
								onclick={() => (showImageUpload = true)}
							>
								<Icon icon="mdi:camera" class="w-4 h-4 mr-2" />
								เปลี่ยนรูปโปรไฟล์
							</Button>
						</div>
					</div>
				</Card>
			</div>
		{:else if activeTab === 'password'}
			<!-- Password Tab Content -->
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<!-- Change Password Form -->
				<Card title="เปลี่ยนรหัสผ่าน" size="full">
					<form
						method="POST"
						action="?/changePassword"
						use:enhance={() => {
							handlePasswordSubmit();
							return async ({ result }) => {
								handlePasswordResult(result);
							};
						}}
					>
						<div class="space-y-4">
							<Input
								type="password"
								name="currentPassword"
								bind:value={passwordData.currentPassword}
								label="รหัสผ่านปัจจุบัน"
								placeholder="กรอกรหัสผ่านปัจจุบัน"
								icon="mdi:lock"
								required
								onchange={e => handlePasswordFieldChange('currentPassword', (e.target as HTMLInputElement).value)}
							/>

							<Input
								type="password"
								name="newPassword"
								bind:value={passwordData.newPassword}
								label="รหัสผ่านใหม่"
								placeholder="กรอกรหัสผ่านใหม่ (อย่างน้อย 6 ตัวอักษร)"
								icon="mdi:lock-plus"
								required
								onchange={e => handlePasswordFieldChange('newPassword', (e.target as HTMLInputElement).value)}
							/>

							<Input
								type="password"
								name="confirmPassword"
								bind:value={passwordData.confirmPassword}
								label="ยืนยันรหัสผ่านใหม่"
								placeholder="กรอกรหัสผ่านใหม่อีกครั้ง"
								icon="mdi:lock-check"
								required
								onchange={e => handlePasswordFieldChange('confirmPassword', (e.target as HTMLInputElement).value)}
							/>

							{#if passwordData.confirmPassword && passwordData.newPassword !== passwordData.confirmPassword}
								<div class="text-error text-sm flex items-center gap-1">
									<Icon icon="mdi:alert-circle" class="w-4 h-4" />
									รหัสผ่านใหม่ไม่ตรงกัน
								</div>
							{/if}

							<Button 
								type="submit" 
								color="primary" 
								loading={isChangingPassword} 
								disabled={isChangingPassword || !isPasswordFormValid}
								block
							>
								<Icon icon="mdi:lock-reset" class="w-4 h-4 mr-2" />
								เปลี่ยนรหัสผ่าน
							</Button>
						</div>
					</form>
				</Card>

				<!-- Password Security Info -->
				<Card title="ความปลอดภัย" size="full">
					<div class="space-y-4">
						<div class="bg-base-200 p-4 rounded-lg">
							<h4 class="font-semibold text-base-content mb-3">คำแนะนำความปลอดภัย</h4>
							<div class="text-sm text-base-content/70 space-y-2">
								<div class="flex items-start gap-2">
									<Icon icon="mdi:check-circle" class="w-4 h-4 text-success mt-0.5" />
									<span>ใช้รหัสผ่านที่แข็งแกร่ง (ตัวอักษร ตัวเลข และสัญลักษณ์)</span>
								</div>
								<div class="flex items-start gap-2">
									<Icon icon="mdi:check-circle" class="w-4 h-4 text-success mt-0.5" />
									<span>หลีกเลี่ยงการใช้ข้อมูลส่วนตัวในรหัสผ่าน</span>
								</div>
								<div class="flex items-start gap-2">
									<Icon icon="mdi:check-circle" class="w-4 h-4 text-success mt-0.5" />
									<span>เปลี่ยนรหัสผ่านเป็นประจำ</span>
								</div>
								<div class="flex items-start gap-2">
									<Icon icon="mdi:check-circle" class="w-4 h-4 text-success mt-0.5" />
									<span>ไม่แชร์รหัสผ่านกับผู้อื่น</span>
								</div>
							</div>
						</div>

						<div class="bg-base-200 p-4 rounded-lg">
							<h4 class="font-semibold text-base-content mb-3">ข้อมูลการเข้าสู่ระบบ</h4>
							<div class="text-sm text-base-content/70 space-y-2">
								<div class="flex justify-between">
									<span>เข้าสู่ระบบล่าสุด:</span>
									<span>{new Date(userStats.lastActive).toLocaleString('th-TH')}</span>
								</div>
								<div class="flex justify-between">
									<span>จำนวนครั้งที่เข้าสู่ระบบ:</span>
									<span>{userStats.totalLogins} ครั้ง</span>
								</div>
								<div class="flex justify-between">
									<span>สถานะ:</span>
									<Badge label="ออนไลน์" color="success" size="sm" />
								</div>
							</div>
						</div>
					</div>
				</Card>
			</div>
		{:else if activeTab === 'activity'}
			<!-- Activity Tab Content -->
			<Card title="ประวัติกิจกรรม" size="full">
				<ActivityList 
					activities={activityData}
					showPriority={true}
					showStatus={true}
					showEntity={true}
					maxItems={20}
					emptyMessage="ไม่มีข้อมูลกิจกรรม"
				/>
			</Card>
		{:else if activeTab === 'stats'}
			<!-- Stats Tab Content -->
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				<!-- Login Stats -->
				<Card title="สถิติการเข้าสู่ระบบ" size="full">
					<div class="space-y-4">
						<div class="text-center">
							<div class="text-4xl font-bold text-primary mb-2">
								{userStats.totalLogins || 0}
							</div>
							<div class="text-sm text-base-content/70">ครั้งทั้งหมด</div>
						</div>
						<div class="bg-base-200 p-3 rounded-lg">
							<div class="text-xs text-base-content/60 mb-1">เข้าสู่ระบบล่าสุด</div>
							<div class="text-sm font-medium">
								{userStats.lastActive ? new Date(userStats.lastActive).toLocaleString('th-TH') : 'ไม่ระบุ'}
							</div>
						</div>
					</div>
				</Card>

				<!-- Points Stats -->
				<Card title="สถิติคะแนน" size="full">
					<div class="space-y-4">
						<div class="text-center">
							<div class="text-4xl font-bold text-secondary mb-2">
								{userStats.totalPoints || 0}
							</div>
							<div class="text-sm text-base-content/70">คะแนนรวม</div>
						</div>
						<div class="space-y-2">
							<div class="flex justify-between text-sm">
								<span class="text-base-content/70">Money Points:</span>
								<span class="font-medium">{userStats.moneyPoints?.toLocaleString() || '0'}</span>
							</div>
							<div class="flex justify-between text-sm">
								<span class="text-base-content/70">Gold Points:</span>
								<span class="font-medium">{userStats.goldPoints?.toLocaleString() || '0'}</span>
							</div>
						</div>
					</div>
				</Card>

				<!-- Membership Stats -->
				<Card title="สถิติสมาชิก" size="full">
					<div class="space-y-4">
						<div class="text-center">
							<div class="text-4xl font-bold text-accent mb-2">
								{userStats.joinedDays || 0}
							</div>
							<div class="text-sm text-base-content/70">วันที่เป็นสมาชิก</div>
						</div>
						<div class="bg-base-200 p-3 rounded-lg">
							<div class="text-xs text-base-content/60 mb-1">เข้าร่วมเมื่อ</div>
							<div class="text-sm font-medium">
								{profileData.createdAt
									? new Date(profileData.createdAt).toLocaleDateString('th-TH')
									: 'ไม่ระบุ'}
							</div>
						</div>
					</div>
				</Card>
			</div>
		{:else if activeTab === 'settings'}
			<!-- Settings Tab Content -->
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<!-- System Test -->
				<Card title="ทดสอบระบบ" size="full">
					<div class="space-y-4">
						<!-- รีโหลดข้อมูลโปรไฟล์ -->
						<form
							method="POST"
							action="?/refreshProfile"
							use:enhance={() => {
								isRefreshing = true;
								return async ({ result }) => {
									isRefreshing = false;
									handleActionResult(result);
								};
							}}
						>
							<Button
								type="submit"
								color="primary"
								loading={isRefreshing}
								disabled={isRefreshing}
								block
							>
								<Icon icon="mdi:refresh" class="w-4 h-4 mr-2" />
								รีโหลดข้อมูลโปรไฟล์
							</Button>
						</form>

						<!-- ทดสอบ Refresh Token -->
						<form
							method="POST"
							action="?/testToken"
							use:enhance={() => {
								isLoading = true;
								return async ({ result }) => {
									isLoading = false;
									handleActionResult(result);
								};
							}}
						>
							<Button
								type="submit"
								color="secondary"
								loading={isLoading}
								disabled={isLoading}
								block
							>
								<Icon icon="mdi:shield-check" class="w-4 h-4 mr-2" />
								ทดสอบ Refresh Token
							</Button>
						</form>

						<Button
							variant="ghost"
							block
							onclick={() =>
								showInfo(
									'ข้อมูลการทดสอบ',
									'ใช้ปุ่มเหล่านี้เพื่อทดสอบการทำงานของ refresh token และการอัปเดตข้อมูลโปรไฟล์'
								)}
						>
							<Icon icon="mdi:help-circle" class="w-4 h-4 mr-2" />
							วิธีใช้งาน
						</Button>
					</div>
				</Card>

				<!-- Token Information -->
				<Card title="ข้อมูล Token" size="full">
					<div class="space-y-4">
						<div class="bg-base-200 p-4 rounded-lg">
							<h4 class="font-semibold text-base-content mb-3">ข้อมูลการเข้าสู่ระบบ</h4>
							<div class="space-y-2 text-sm">
								<div class="flex justify-between">
									<span class="text-base-content/70">สร้างเมื่อ:</span>
									<span class="text-base-content">
										{profileData.createdAt
											? new Date(profileData.createdAt).toLocaleString('th-TH')
											: 'ไม่ระบุ'}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-base-content/70">อัปเดตล่าสุด:</span>
									<span class="text-base-content">
										{profileData.updatedAt
											? new Date(profileData.updatedAt).toLocaleString('th-TH')
											: 'ไม่ระบุ'}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-base-content/70">สถานะ:</span>
									<Badge label="เข้าสู่ระบบแล้ว" color="success" size="sm" />
								</div>
							</div>
						</div>

						<div class="bg-base-200 p-4 rounded-lg">
							<h4 class="font-semibold text-base-content mb-3">คำแนะนำการทดสอบ</h4>
							<div class="text-sm text-base-content/70 space-y-2">
								<p>
									1. <strong>รีโหลดข้อมูลโปรไฟล์:</strong> ทดสอบการดึงข้อมูลใหม่จาก API
								</p>
								<p>
									2. <strong>ทดสอบ Refresh Token:</strong> ทดสอบการ refresh token เมื่อหมดอายุ
								</p>
								<p>
									3. <strong>บันทึกการเปลี่ยนแปลง:</strong> ทดสอบการอัปเดตข้อมูลโปรไฟล์
								</p>
								<p class="text-warning">💡 เปิด Developer Console เพื่อดู log การทำงานของ token</p>
							</div>
						</div>
					</div>
				</Card>
			</div>
		{/if}
	</div>
</div>

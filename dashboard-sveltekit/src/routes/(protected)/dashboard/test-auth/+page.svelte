<script lang="ts">
	import Icon from '@iconify/svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import { authStore } from '$lib/stores/auth.svelte.ts';
	import { authService } from '$lib/services/auth';

	// State
	let testResults = $state<Record<string, any>>({});
	let isLoading = $state(false);

	// Derived values
	const user = $derived(authStore.user);
	const isAuthenticated = $derived(authStore.isAuthenticated);
	const isInitialized = $derived(authStore.isInitialized);

	// Test functions
	async function testTokenHealth() {
		isLoading = true;
		try {
			const result = await authStore.checkTokenHealth();
			testResults.tokenHealth = {
				success: true,
				data: result,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.tokenHealth = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testCookieStatus() {
		isLoading = true;
		try {
			const cookies = document.cookie.split(';').reduce((acc, cookie) => {
				const [key, value] = cookie.trim().split('=');
				if (key) acc[key] = value || '';
				return acc;
			}, {} as Record<string, string>);

			const authToken = cookies['auth_token'];
			const refreshToken = cookies['refreshToken'];
			const sessionId = cookies['session_id'];

			testResults.cookieStatus = {
				success: true,
				data: {
					hasAuthToken: !!authToken,
					hasRefreshToken: !!refreshToken,
					hasSessionId: !!sessionId,
					authTokenLength: authToken?.length || 0,
					refreshTokenLength: refreshToken?.length || 0,
					allCookies: Object.keys(cookies),
					cookieCount: Object.keys(cookies).length,
				},
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.cookieStatus = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testReloadPage() {
		if (confirm('คุณต้องการรีโหลดหน้าเว็บเพื่อตรวจสอบการโหลด auth state ใหม่หรือไม่?')) {
			window.location.reload();
		}
	}

	async function testGoToSignin() {
		if (confirm('คุณต้องการไปหน้า Sign In เพื่อล็อกอินใหม่หรือไม่? (จะไม่ออกจากระบบอัตโนมัติ)')) {
			window.location.href = '/signin';
		}
	}

	async function testClearAllCookies() {
		if (confirm('คุณต้องการลบ cookies ทั้งหมดและไปหน้า Sign In หรือไม่?')) {
			// Clear all auth-related cookies
			const cookiesToClear = [
				'auth_token',
				'refreshToken', 
				'session_id',
				'session',
				'csrf_token',
				'remember_me'
			];

			cookiesToClear.forEach(cookieName => {
				const expireDate = 'Thu, 01 Jan 1970 00:00:00 UTC';
				document.cookie = `${cookieName}=; expires=${expireDate}; path=/;`;
				document.cookie = `${cookieName}=; expires=${expireDate}; path=/; domain=${window.location.hostname};`;
			});

			testResults.clearCookies = {
				success: true,
				message: 'All cookies cleared, redirecting to signin',
				timestamp: new Date().toISOString(),
			};

			setTimeout(() => {
				window.location.href = '/signin';
			}, 1000);
		}
	}

	async function testAuthStoreState() {
		isLoading = true;
		try {
			// ตรวจสอบ internal state ของ auth store
			const storeState = {
				isInitialized,
				isAuthenticated,
				hasUser: !!user,
				userId: user?._id,
				userEmail: user?.email,
				// ตรวจสอบ private variables ผ่าน console
				hasAccessTokenInMemory: !!(authStore as any)._accessToken,
				hasRefreshTokenInMemory: !!(authStore as any)._refreshToken,
				isRefreshing: !!(authStore as any)._isRefreshing,
				lastRefreshTime: (authStore as any).lastRefreshTime || 0,
			};

			testResults.authStoreState = {
				success: true,
				data: storeState,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.authStoreState = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testServerSideAuth() {
		isLoading = true;
		try {
			// ทดสอบการเรียก API ที่ต้องการ authentication
			const response = await fetch('/api/test-auth', {
				method: 'GET',
				credentials: 'include', // ส่ง cookies ไปด้วย
			});

			const result = await response.json();

			testResults.serverSideAuth = {
				success: response.ok,
				data: result,
				status: response.status,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.serverSideAuth = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testManualRefresh() {
		isLoading = true;
		try {
			const result = await authStore.refreshToken();
			testResults.manualRefresh = {
				success: result,
				message: result ? 'Token refreshed successfully' : 'Token refresh failed (but not signed out)',
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.manualRefresh = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testSmartRefresh() {
		isLoading = true;
		try {
			const result = await authStore.smartRefresh();
			testResults.smartRefresh = {
				success: result,
				message: result ? 'Smart refresh completed successfully' : 'Smart refresh failed or not needed',
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.smartRefresh = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testGetCurrentUser() {
		isLoading = true;
		try {
			// ใช้ access token แทน refresh token
			const accessToken = getTokenFromCookies();
			if (!accessToken) {
				testResults.getCurrentUser = {
					success: false,
					error: 'No access token available in cookies',
					timestamp: new Date().toISOString(),
				};
				return;
			}
			
			const result = await authService.getCurrentUser(accessToken);
			testResults.getCurrentUser = {
				success: result.success,
				data: result.success ? result.data : null,
				error: result.success ? null : result.error,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.getCurrentUser = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testRefreshUser() {
		isLoading = true;
		try {
			await authStore.refreshUser();
			testResults.refreshUser = {
				success: true,
				message: 'User data refreshed successfully',
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.refreshUser = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	async function testForceSignout() {
		if (!confirm('คุณแน่ใจหรือไม่ที่จะออกจากระบบ? การทดสอบนี้จะทำให้คุณต้องล็อกอินใหม่')) {
			return;
		}

		isLoading = true;
		try {
			await authStore.signout();
			testResults.forceSignout = {
				success: true,
				message: 'Signout initiated successfully',
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			testResults.forceSignout = {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString(),
			};
		} finally {
			isLoading = false;
		}
	}

	function getTokenFromCookies(): string | null {
		const cookies = document.cookie.split(';').reduce((acc, cookie) => {
			const [key, value] = cookie.trim().split('=');
			acc[key] = value;
			return acc;
		}, {} as Record<string, string>);
		
		return cookies['auth_token'] || null;
	}

	function getRefreshTokenFromCookies(): string | null {
		const cookies = document.cookie.split(';').reduce((acc, cookie) => {
			const [key, value] = cookie.trim().split('=');
			acc[key] = value;
			return acc;
		}, {} as Record<string, string>);
		
		return cookies['refreshToken'] || null;
	}

	function clearTestResults() {
		testResults = {};
	}

	function formatJson(obj: any) {
		return JSON.stringify(obj, null, 2);
	}
</script>

<div class="space-y-4">
	<!-- Header -->
	<div class="flex justify-between items-center">
		<div>
			<h1 class="text-3xl font-bold text-base-content">
				<Icon icon="mdi:test-tube" class="w-8 h-8 inline mr-2" />
				ทดสอบระบบ Authentication
			</h1>
			<p class="text-base-content/60 mt-1">ทดสอบการทำงานของ refresh token และ authentication</p>
		</div>
		<div class="flex gap-2">
			<Button href="../dashboard" variant="outline">
				<Icon icon="mdi:arrow-left" class="w-5 h-5" />
				กลับ
			</Button>
			<Button color="secondary" onclick={clearTestResults}>
				<Icon icon="mdi:refresh" class="w-5 h-5" />
				ล้างผลลัพธ์
			</Button>
		</div>
	</div>

	<!-- Auth Status -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
		<Card title="สถานะ Authentication" size="full">
			<div class="space-y-3">
				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Initialized:</span>
					<span class="badge {isInitialized ? 'badge-success' : 'badge-error'}">
						{isInitialized ? 'Yes' : 'No'}
					</span>
				</div>
				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Authenticated:</span>
					<span class="badge {isAuthenticated ? 'badge-success' : 'badge-error'}">
						{isAuthenticated ? 'Yes' : 'No'}
					</span>
				</div>
				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">User ID:</span>
					<span class="text-sm font-mono">{user?._id || 'N/A'}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Email:</span>
					<span class="text-sm">{user?.email || 'N/A'}</span>
				</div>
			</div>
		</Card>

		<Card title="Tokens" size="full">
			<div class="space-y-3">
				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Auth Token:</span>
					<span class="badge {getTokenFromCookies() ? 'badge-success' : 'badge-error'}">
						{getTokenFromCookies() ? 'Present' : 'Missing'}
					</span>
				</div>
				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Refresh Token:</span>
					<span class="badge {getRefreshTokenFromCookies() ? 'badge-success' : 'badge-error'}">
						{getRefreshTokenFromCookies() ? 'Present' : 'Missing'}
					</span>
				</div>
				{#if getTokenFromCookies()}
					<div class="text-xs text-base-content/60 break-all">
						Auth: {getTokenFromCookies()?.substring(0, 20)}...
					</div>
				{/if}
				{#if getRefreshTokenFromCookies()}
					<div class="text-xs text-base-content/60 break-all">
						Refresh: {getRefreshTokenFromCookies()?.substring(0, 20)}...
					</div>
				{/if}
			</div>
		</Card>

		<Card title="การดำเนินการ" size="full">
			<div class="space-y-2">
				<Button 
					color="primary" 
					size="sm" 
					block 
					onclick={testTokenHealth}
					loading={isLoading}
				>
					<Icon icon="mdi:heart-pulse" class="w-4 h-4 mr-2" />
					ตรวจสอบสุขภาพ Token
				</Button>
				
				<Button 
					color="secondary" 
					size="sm" 
					block 
					onclick={testManualRefresh}
					loading={isLoading}
				>
					<Icon icon="mdi:refresh" class="w-4 h-4 mr-2" />
					Manual Refresh
				</Button>
				
				<Button 
					color="accent" 
					size="sm" 
					block 
					onclick={testSmartRefresh}
					loading={isLoading}
				>
					<Icon icon="mdi:auto-fix" class="w-4 h-4 mr-2" />
					Smart Refresh
				</Button>
				
				<Button 
					color="info" 
					size="sm" 
					block 
					onclick={testGetCurrentUser}
					loading={isLoading}
				>
					<Icon icon="mdi:account" class="w-4 h-4 mr-2" />
					Get Current User
				</Button>
				
				<Button 
					color="warning" 
					size="sm" 
					block 
					onclick={testRefreshUser}
					loading={isLoading}
				>
					<Icon icon="mdi:account-refresh" class="w-4 h-4 mr-2" />
					Refresh User Data
				</Button>

				<Button 
					color="neutral" 
					size="sm" 
					block 
					onclick={testCookieStatus}
					loading={isLoading}
				>
					<Icon icon="mdi:cookie" class="w-4 h-4 mr-2" />
					Check Cookies
				</Button>

				<Button 
					color="ghost" 
					size="sm" 
					block 
					onclick={testReloadPage}
					loading={isLoading}
				>
					<Icon icon="mdi:refresh" class="w-4 h-4 mr-2" />
					Reload Page
				</Button>

				<Button 
					color="warning" 
					size="sm" 
					block 
					onclick={testGoToSignin}
					loading={isLoading}
				>
					<Icon icon="mdi:login" class="w-4 h-4 mr-2" />
					Go to Sign In
				</Button>

				<Button 
					color="error" 
					size="sm" 
					block 
					onclick={testClearAllCookies}
					loading={isLoading}
				>
					<Icon icon="mdi:cookie-off" class="w-4 h-4 mr-2" />
					Clear All Cookies
				</Button>

				<Button 
					color="info" 
					size="sm" 
					block 
					onclick={testAuthStoreState}
					loading={isLoading}
				>
					<Icon icon="mdi:database-eye" class="w-4 h-4 mr-2" />
					Check Store State
				</Button>

				<Button 
					color="secondary" 
					size="sm" 
					block 
					onclick={testServerSideAuth}
					loading={isLoading}
				>
					<Icon icon="mdi:server" class="w-4 h-4 mr-2" />
					Test Server Auth
				</Button>

				<Button 
					color="error" 
					size="sm" 
					block 
					onclick={testForceSignout}
					loading={isLoading}
				>
					<Icon icon="mdi:logout" class="w-4 h-4 mr-2" />
					Force Signout
				</Button>
			</div>
		</Card>
	</div>

	<!-- Test Results -->
	{#if Object.keys(testResults).length > 0}
		<Card title="ผลลัพธ์การทดสอบ" size="full">
			<div class="space-y-4">
				{#each Object.entries(testResults) as [testName, result]}
					<div class="border border-base-300 rounded-lg p-4">
						<div class="flex items-center justify-between mb-2">
							<h4 class="font-semibold flex items-center gap-2">
								<Icon 
									icon={result.success ? 'mdi:check-circle' : 'mdi:alert-circle'} 
									class="w-5 h-5 {result.success ? 'text-success' : 'text-error'}" 
								/>
								{testName}
							</h4>
							<span class="text-xs text-base-content/60">
								{new Date(result.timestamp).toLocaleTimeString('th-TH')}
							</span>
						</div>
						
						<div class="bg-base-200 rounded p-3">
							<pre class="text-xs overflow-x-auto">{formatJson(result)}</pre>
						</div>
					</div>
				{/each}
			</div>
		</Card>
	{/if}

	<!-- Debug Info -->
	<Card title="Debug Information" size="full">
		<div class="space-y-4">
			<div>
				<h4 class="font-semibold mb-2">All Cookies:</h4>
				<div class="bg-base-200 rounded p-3">
					<pre class="text-xs overflow-x-auto">{document.cookie || 'No cookies'}</pre>
				</div>
			</div>

			<div>
				<h4 class="font-semibold mb-2">Parsed Cookies:</h4>
				<div class="bg-base-200 rounded p-3">
					<pre class="text-xs overflow-x-auto">{formatJson(
						document.cookie.split(';').reduce((acc, cookie) => {
							const [key, value] = cookie.trim().split('=');
							if (key) acc[key] = value || '';
							return acc;
						}, {} as Record<string, string>)
					)}</pre>
				</div>
			</div>

			<div>
				<h4 class="font-semibold mb-2">Auth Store State:</h4>
				<div class="bg-base-200 rounded p-3">
					<pre class="text-xs overflow-x-auto">{formatJson({
						isInitialized,
						isAuthenticated,
						hasUser: !!user,
						userId: user?._id,
						userEmail: user?.email,
					})}</pre>
				</div>
			</div>
			
			<div>
				<h4 class="font-semibold mb-2">Browser Info:</h4>
				<div class="bg-base-200 rounded p-3">
					<pre class="text-xs overflow-x-auto">{formatJson({
						userAgent: navigator.userAgent,
						currentUrl: window.location.href,
						timestamp: new Date().toISOString(),
					})}</pre>
				</div>
			</div>
		</div>
	</Card>

	<!-- Instructions -->
	<Card title="คำแนะนำการใช้งาน" size="full">
		<div class="prose prose-sm max-w-none">
			<h4>การทดสอบระบบ Authentication:</h4>
			<ol>
				<li><strong>Check Cookies:</strong> ตรวจสอบ cookies ที่มีอยู่ในเบราว์เซอร์</li>
				<li><strong>Check Store State:</strong> ตรวจสอบสถานะภายใน auth store</li>
				<li><strong>Test Server Auth:</strong> ทดสอบ authentication ฝั่ง server</li>
				<li><strong>Token Health Check:</strong> ตรวจสอบว่า token ยังใช้งานได้หรือไม่</li>
				<li><strong>Manual Refresh:</strong> บังคับ refresh token (ไม่ออกจากระบบถ้าไม่สำเร็จ)</li>
				<li><strong>Smart Refresh:</strong> ใช้ระบบ smart refresh อัจฉริยะ</li>
				<li><strong>Get Current User:</strong> ดึงข้อมูล user ด้วย access token</li>
				<li><strong>Refresh User Data:</strong> รีเฟรชข้อมูล user ใน store</li>
			</ol>
			
			<h4>การแก้ไขปัญหา Token Rotation System:</h4>
			<ul>
				<li><strong>ไม่มี Cookies:</strong> Token อาจหมดอายุหรือถูก clear แล้ว → ใช้ "Clear All Cookies" แล้วล็อกอินใหม่</li>
				<li><strong>refreshUser สำเร็จแต่ getCurrentUser ไม่สำเร็จ:</strong> Access token หมดอายุ → ใช้ "Manual Refresh"</li>
				<li><strong>Manual/Smart Refresh ออกจากระบบ:</strong> ตอนนี้แก้ไขแล้ว จะไม่ออกจากระบบอัตโนมัติ</li>
				<li><strong>Token Rotation:</strong> ใน Token Rotation System, tokens จะถูกเปลี่ยนเป็นระยะ</li>
				<li><strong>Cross-tab Issues:</strong> หลายแท็บอาจทำให้ token rotation ทำงานผิดปกติ</li>
			</ul>

			<h4>ลำดับการแก้ไขปัญหา:</h4>
			<ol>
				<li>กด "Check Cookies" → ดูว่ามี auth cookies หรือไม่</li>
				<li>กด "Check Store State" → ดูสถานะ auth store</li>
				<li>กด "Test Server Auth" → ทดสอบ server-side authentication</li>
				<li>ถ้าไม่มี cookies → กด "Clear All Cookies" แล้วล็อกอินใหม่</li>
				<li>ถ้ามี cookies แต่ getCurrentUser ไม่สำเร็จ → กด "Manual Refresh"</li>
			</ol>
		</div>
	</Card>
</div>
<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Recaptcha from '$lib/components/ui/Recaptcha.svelte';
	import { forgotPasswordSchema } from '$lib/schemas/auth.schema';
	import { authStore } from '$lib/stores/auth.svelte';
	import { createValidator } from '$lib/utils/validation';
	import { showError, showSuccess } from '$lib/utils/sweetalert';

	const { form } = $props<{
		form?: any;
	}>();

	let email = $state('');
	let errors = $state<Record<string, string>>({});
	let recaptchaToken = $state<string>('');
	let recaptchaComponent: Recaptcha;
	let debounceTimer: NodeJS.Timeout;

	// Form action result
	const formResult = $derived(form);

	// สร้าง validator
	const validator = createValidator(forgotPasswordSchema);

	// Realtime validation with debouncing
	$effect(() => {
		clearTimeout(debounceTimer);
		debounceTimer = setTimeout(() => {
			const emailError = validator.validateField('email', email, true);
			if (emailError !== errors.email) {
				errors = { ...errors, email: emailError || '' };
			}
		}, 300); // รอ 300ms หลังหยุดพิมพ์
	});

	// Check if form is valid
	const isValid = $derived(Object.keys(errors).length === 0);

	onMount(() => {
		// ถ้า login แล้วให้ redirect ไป dashboard
		if (authStore.isAuthenticated) {
			goto('/dashboard');
		}
	});

	// Handle form result
	$effect(() => {
		if (formResult?.success) {
			showSuccess('สำเร็จ!', formResult.message);
			setTimeout(() => {
				goto('/signin');
			}, 2000);
		} else if (formResult?.error) {
			showError('เกิดข้อผิดพลาด', formResult.error);
		}
	});

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const target = event.target as HTMLElement;
			const form = target?.closest('form') as HTMLFormElement;
			if (form) {
				form.requestSubmit();
			}
		}
	}
</script>

<SEO title={$t('auth.forgotPasswordTitle')} />

<Card centered variant="default" shadow="lg" size="sm" title={$t('auth.forgotPasswordTitle')}>
	<!-- Form Action Messages -->
	{#if formResult?.success}
		<div class="alert alert-success">
			<Icon icon="mdi:check-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{:else if formResult?.error}
		<div class="alert alert-error">
			<Icon icon="mdi:alert-circle" class="w-5 h-5" />
			<span>{formResult.error}</span>
		</div>
	{/if}

	<!-- Forgot Password Form -->
	<form
		method="POST"
		action="?/forgotPassword"
		class="space-y-4"
		use:enhance={() => {
			return async ({ result }) => {
				if (result.type === 'failure') {
					showError(`เกิดข้อผิดพลาด ${result.data?.error || 'ไม่สามารถส่งอีเมลรีเซ็ตรหัสผ่านได้'}`);
				}
			};
		}}
	>
		<div class="text-center mb-6">
			<Icon icon="mdi:lock-reset" class="w-16 h-16 text-primary mx-auto mb-4" />
			<h2 class="text-xl font-semibold mb-2">
				{$t('auth.forgotPasswordTitle')}?
			</h2>
			<p class="text-base-content/70">
				{$t('auth.forgotPasswordDescription')}
			</p>
		</div>

		<Input
			id="email"
			name="email"
			type="email"
			bind:value={email}
			label={$t('auth.email')}
			placeholder="<EMAIL>"
			icon="mdi:email"
			validate={errors.email}
			required
			autocomplete="email"
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<!-- reCAPTCHA - Flexible (v2 default, can easily switch to v3) -->
		<Recaptcha
			bind:this={recaptchaComponent}
			version="v2"
			action="forgot_password"
			callback={(token: string) => {
				console.log('reCAPTCHA token received:', token.substring(0, 50) + '...');
				recaptchaToken = token;
			}}
			expiredCallback={() => {
				console.log('reCAPTCHA expired');
				recaptchaToken = '';
			}}
			errorCallback={() => {
				console.log('reCAPTCHA error');
				recaptchaToken = '';
				showError('เกิดข้อผิดพลาด', 'เกิดข้อผิดพลาดในการตรวจสอบ reCAPTCHA กรุณาลองใหม่อีกครั้ง');
			}}
		/>

		<!-- Hidden field สำหรับส่ง reCAPTCHA token -->
		<input type="hidden" name="recaptchaToken" value={recaptchaToken} />

		<Button
			type="submit"
			color="primary"
			size="lg"
			block
			loading={authStore.isLoading}
			disabled={authStore.isLoading || !recaptchaToken || !isValid}
		>
			{$t('auth.sendResetEmail')}
		</Button>

		{#if !recaptchaToken}
			<div class="text-center text-sm text-warning">
				<Icon icon="mdi:shield-alert" class="w-4 h-4 inline mr-1" />
				กรุณายืนยัน reCAPTCHA ก่อนส่งอีเมลรีเซ็ตรหัสผ่าน
			</div>
		{/if}
	</form>

	<!-- Back to Login Link -->
	<div class="text-center mt-6">
		<span class="text-base-content/60">{$t('auth.rememberPassword')} </span>
		<a href="/signin" class="link link-primary">
			{$t('auth.signin')}
		</a>
	</div>
</Card>

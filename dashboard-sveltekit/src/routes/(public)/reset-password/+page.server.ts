import { authService } from '$lib/services/auth';
import { LogCategory, logger } from '$lib/utils/logger';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, url }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  // ตรวจสอบ token จาก URL
  const token = url.searchParams.get('token');

  return {
    user: null,
    token: token || null,
  };
};

export const actions: Actions = {
  /**
   * ✅ Reset Password - Hybrid Approach
   * Route API + Service Pattern
   */
  resetPassword: async ({ request, url, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    try {
      const formData = await request.formData();
      const token = url.searchParams.get('token');

      // ✅ Route-level validation - Extract and validate form data
      const resetData = {
        token: token?.trim(),
        password: formData.get('password')?.toString()?.trim(),
        confirmPassword: formData.get('confirmPassword')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!resetData.token) {
        logger.warn(LogCategory.AUTH, 'reset_password_no_token', 'No token provided', {
          clientIP,
          userAgent,
        });

        return fail(400, {
          message: 'Token ไม่ถูกต้อง',
          type: 'reset',
          fields: {
            token: 'Token ไม่ถูกต้อง',
          },
        });
      }

      if (!resetData.password) {
        return fail(400, {
          message: 'กรุณากรอกรหัสผ่าน',
          type: 'reset',
          fields: {
            password: 'กรุณากรอกรหัสผ่าน',
          },
        });
      }

      if (!resetData.confirmPassword) {
        return fail(400, {
          message: 'กรุณายืนยันรหัสผ่าน',
          type: 'reset',
          fields: {
            confirmPassword: 'กรุณายืนยันรหัสผ่าน',
          },
        });
      }

      if (resetData.password !== resetData.confirmPassword) {
        return fail(400, {
          message: 'รหัสผ่านไม่ตรงกัน',
          type: 'reset',
          fields: {
            confirmPassword: 'รหัสผ่านไม่ตรงกัน',
          },
        });
      }

      if (resetData.password.length < 6) {
        return fail(400, {
          message: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร',
          type: 'reset',
          fields: {
            password: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร',
          },
        });
      }

      logger.info(LogCategory.AUTH, 'reset_password_attempt', 'Password reset attempt', {
        tokenLength: resetData.token.length,
        clientIP,
        userAgent,
      });

      // ✅ Call service for business logic
      const result = await authService.resetPassword({
        token: resetData.token,
        password: resetData.password,
      });

      if (!result.success) {
        logger.warn(LogCategory.AUTH, 'reset_password_failed', 'Password reset failed', {
          error: result.error,
          tokenLength: resetData.token.length,
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถรีเซ็ตรหัสผ่านได้',
          type: 'reset',
        });
      }

      logger.info(LogCategory.AUTH, 'reset_password_success', 'Password reset successful', {
        tokenLength: resetData.token.length,
        clientIP,
      });

      return {
        success: true,
        message: 'รีเซ็ตรหัสผ่านสำเร็จ! กรุณาเข้าสู่ระบบด้วยรหัสผ่านใหม่',
        type: 'reset',
      };
    }
    catch (error) {
      logger.error(LogCategory.AUTH, 'reset_password_error', 'Password reset error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน',
        type: 'reset',
      });
    }
  },
};

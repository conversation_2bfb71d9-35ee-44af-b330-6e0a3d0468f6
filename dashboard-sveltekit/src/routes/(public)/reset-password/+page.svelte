<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';

	// ✅ Props from server
	const { data, form } = $props<{
		data: { token: string | null; user: any };
		form?: any;
	}>();

	// ✅ Client state management
	let isLoading = $state(false);

	// ✅ Form data using $state (Svelte 5 compatible)
	let formData = $state({
		password: '',
		confirmPassword: '',
	});

	// ✅ Form validation errors
	let formErrors = $state<Record<string, string>>({});

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Derived state for form validation
	const isFormValid = $derived(
		formData.password && 
		formData.confirmPassword &&
		formData.password.length >= 6 &&
		formData.password === formData.confirmPassword
	);

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			showSuccess('รีเซ็ตรหัสผ่านสำเร็จ!', formResult.message);
			setTimeout(() => {
				goto('/signin');
			}, 2000);
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			showSuccess('รีเซ็ตรหัสผ่านสำเร็จ!', result.data?.message);
			setTimeout(() => {
				goto('/signin');
			}, 2000);
		} else if (result.type === 'failure') {
			showError('รีเซ็ตรหัสผ่านไม่สำเร็จ', result.data?.message);
			
			// Handle field-specific errors
			if (result.data?.fields) {
				formErrors = result.data.fields;
			}
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const target = event.target as HTMLElement;
			const form = target?.closest('form') as HTMLFormElement;
			if (form) {
				form.requestSubmit();
			}
		}
	}
</script>

<SEO
	title={$t('auth.resetPasswordTitle')}
	description={$t('auth.resetPasswordDescription')}
	keywords="ตั้งรหัสผ่านใหม่, รีเซ็ตรหัสผ่าน, เปลี่ยนรหัสผ่าน"
	url="/reset-password"
	noindex={true}
/>

<div
	class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4"
>
	<Card class="w-full max-w-md p-8 space-y-4">
		<!-- Header -->
		<div class="text-center space-y-2">
			<div class="flex justify-center mb-4">
				<div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
					<Icon icon="solar:key-bold" class="w-6 h-6 text-primary-foreground" />
				</div>
			</div>
			<h1 class="text-2xl font-bold text-foreground">{$t('auth.resetPasswordTitle')}</h1>
			<p class="text-muted-foreground">{$t('auth.resetPasswordDescription')}</p>
		</div>

		{#if !data.token}
			<!-- No token error -->
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>Token ไม่ถูกต้องหรือหมดอายุ กรุณาขอรีเซ็ตรหัสผ่านใหม่</span>
			</div>
			
			<div class="text-center">
				<a href="/forgot-password" class="text-primary hover:text-primary/80 font-medium transition-colors">
					ขอรีเซ็ตรหัสผ่านใหม่
				</a>
			</div>
		{:else}
			<!-- ✅ Reset Password Form -->
			<form
				method="POST"
				action="?/resetPassword"
				use:enhance={() => {
					isLoading = true;
					showLoading('กำลังตั้งรหัสผ่านใหม่...');

					return async ({ result }) => {
						handleFormResult(result);
					};
				}}
				class="space-y-4"
			>
				<div class="space-y-2">
					<Input
						id="password"
						name="password"
						type="password"
						placeholder="••••••••"
						bind:value={formData.password}
						label={$t('auth.newPassword')}
						icon="mdi:lock"
						error={formErrors.password}
						required
						showPasswordToggle
						autocomplete="new-password"
						onkeydown={handleKeydown}
					/>
				</div>

				<div class="space-y-2">
					<Input
						id="confirmPassword"
						name="confirmPassword"
						type="password"
						placeholder="••••••••"
						bind:value={formData.confirmPassword}
						label={$t('auth.confirmNewPassword')}
						icon="mdi:lock-check"
						error={formErrors.confirmPassword}
						required
						showPasswordToggle
						autocomplete="new-password"
						onkeydown={handleKeydown}
					/>
				</div>

				<Button
					type="submit"
					disabled={isLoading || !isFormValid}
					loading={isLoading}
					color="primary"
					size="lg"
					block
				>
					{#if !isLoading}
						<Icon icon="solar:key-line-duotone" class="w-4 h-4 mr-2" />
					{/if}
					{isLoading ? $t('auth.resettingPassword') : $t('auth.resetPassword')}
				</Button>
			</form>
		{/if}

		<div class="relative">
			<div class="absolute inset-0 flex items-center">
				<div class="w-full border-t border-border"></div>
			</div>
			<div class="relative flex justify-center text-sm">
				<span class="px-2 bg-card text-muted-foreground">{$t('auth.or')}</span>
			</div>
		</div>

		<div class="text-center">
			<a href="/signin" class="text-primary hover:text-primary/80 font-medium transition-colors">
				{$t('auth.backToSignin')}
			</a>
		</div>
	</Card>
</div>

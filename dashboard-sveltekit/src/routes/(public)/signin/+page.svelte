<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import {Button, Card, Input} from '$lib/components/ui';
	import { authStore, type User } from '$lib/stores/auth.svelte';
	import { ErrorHandler } from '$lib/utils/error-handler';
	import { LogCategory, logger } from '$lib/utils/logger';

	import {
	showError,
		showLoading,
		showLoginError,
		showToast, 
	} from '$lib/utils/sweetalert';

	const { form } = $props<{
		form?: any;
	}>();

	// ✅ Client state management
	let isLoading = $state(false);

	// ✅ Form data using $state (Svelte 5 compatible)
	let formData = $state({
		email: '',
		password: '',
		rememberMe: false,
	});

	// ✅ Form validation errors
	let formErrors = $state<Record<string, string>>({});

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Derived state for form validation
	const isFormValid = $derived(
		// Basic validation
		formData.email && 
		formData.password &&
		// Email validation
		/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
		// Password validation
		formData.password.length >= 6
	);

	// ✅ Debug validation state
	$effect(() => {
		console.log('Form validation state:', {
			isValid: isFormValid,
			data: formData,
			errors: formErrors,
			isLoading
		});
	});

	// ✅ Real-time validation
	$effect(() => {
		const errors: Record<string, string> = {};

		// Email validation
		if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = 'รูปแบบอีเมลไม่ถูกต้อง';
		}

		// Password validation
		if (formData.password && formData.password.length < 6) {
			errors.password = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
		}

		formErrors = errors;
	});

	onMount(() => {
		// ถ้า login แล้วให้ redirect ไป dashboard
		if (authStore.isAuthenticated) {
			goto('/dashboard');
		}

		// Clear sensitive data from localStorage if exists
		if (typeof window !== 'undefined') {
			const oldToken = localStorage.getItem('auth_token');
			if (oldToken) {
				localStorage.removeItem('auth_token');
				logger.info(LogCategory.AUTH, 'old_token_cleared', 'Cleared old auth token on signin page');
			}
		}
	});

	/**
	 * ✅ Handle form results consistently - Hybrid Approach
	 */
	function handleFormResult(result: any) {
		isLoading = false;

		console.log('Signin form result:', result);

		if (result.type === 'success' && result.data?.success) {
			// ✅ Success case
			if (result.data?.user) {
				authStore.updateUser(result.data.user as User);
			}

			logger.info(LogCategory.AUTH, 'client_signin_success', 'Client-side signin success');

			// Redirect to dashboard
			goto('/dashboard');

			showToast('success', 'เข้าสู่ระบบสำเร็จ!', {
				timer: 1500,
				timerProgressBar: true,
				showCloseButton: false,
				allowEscapeKey: true,
			});
		} else if (result.type === 'failure') {
			// ✅ Handle different error types
			const errorData = result.data;
			const errorMessage = errorData?.message || 'เข้าสู่ระบบล้มเหลว';

			logger.warn(LogCategory.AUTH, 'client_signin_failed', 'Client-side signin failed', {
				error: errorMessage,
			});

			// Show specific error messages
			switch (errorData?.type) {
				case 'validation':
					showError('ข้อมูลไม่ถูกต้อง', errorMessage);
					break;
				case 'signin':
					showError('เข้าสู่ระบบล้มเหลว', errorMessage);
					break;
				case 'server_error':
					showError('เกิดข้อผิดพลาด', errorMessage);
					break;
				default:
					showError('เกิดข้อผิดพลาด', errorMessage);
			}
		}
	}

	/**
	 * ✅ Handle Enter key submission
	 */
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const target = event.target as HTMLElement;
			const form = target?.closest('form') as HTMLFormElement;
			if (form && !isLoading && isFormValid) {
				form.requestSubmit();
			}
		}
	}
</script>

<SEO title={$t('auth.signin')} />

<Card centered variant="default" shadow="lg" size="sm" class="space-y-4">
	<div class="text-center space-y-2">
		<h2 class="text-xl sm:text-2xl font-bold tracking-tight">
			{$t('auth.signin')}
		</h2>
		<p class="max-sm:text-sm">
			{$t('auth.signinDescription')}
		</p>
	</div>

	<!-- Form Action Messages -->
	{#if formResult?.success}
		<div class="alert alert-success">
			<Icon icon="mdi:check-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{:else if formResult?.message}
		<div class="alert alert-error">
			<Icon icon="mdi:alert-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{/if}

	<!-- ✅ Signin Form - Hybrid Approach with use:enhance -->
	<form
		method="POST"
		action="?/signin"
		class="space-y-4"
		use:enhance={() => {
			isLoading = true;
			showLoading('กำลังเข้าสู่ระบบ...');

			return async ({ result }) => {
				handleFormResult(result);
			};
		}}
	>
		<Input
			id="email"
			name="email"
			type="email"
			bind:value={formData.email}
			label={$t('auth.email')}
			placeholder="<EMAIL>"
			icon="solar:letter-line-duotone"
			validate={formErrors.email}
			required
			autocomplete="email"
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<Input
			id="password"
			name="password"
			type="password"
			bind:value={formData.password}
			label={$t('auth.password')}
			placeholder="••••••••"
			icon="solar:key-minimalistic-square-2-line-duotone"
			validate={formErrors.password}
			required
			showPasswordToggle
			autocomplete="current-password"
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<div class="flex items-center justify-between">
			<label for="rememberMe" class="label cursor-pointer">
				<input
					id="rememberMe"
					name="rememberMe"
					type="checkbox"
					bind:checked={formData.rememberMe}
					class="checkbox checkbox-primary checkbox-sm"
				/>
				<span class="label-text ml-2">{$t('auth.rememberMe')}</span>
			</label>

			<a
				href="/forgot-password"
				class="link link-primary text-sm"
			>
				{$t('auth.forgotPassword')}
			</a>
		</div>

		{#if authStore.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{authStore.error}</span>
			</div>
		{/if}

		<!-- ✅ Submit button with proper loading state -->
		<Button
			type="submit"
			color="primary"
			size="lg"
			block
			loading={isLoading}
			disabled={isLoading || !isFormValid}
		>
			{isLoading ? 'กำลังเข้าสู่ระบบ...' : $t('auth.signin')}
		</Button>

		<!-- ✅ Debug information for development -->
		{#if import.meta.env.DEV}
			<div class="text-xs text-gray-500 mt-2">
				<div>Form Valid: {isFormValid}</div>
				<div>Loading: {isLoading}</div>
				<div>Remember Me: {formData.rememberMe}</div>
			</div>
		{/if}
	</form>

	<!-- Register Link -->
	<div class="text-center mt-6">
		<span class="text-base-content/60">{$t('auth.noAccount')} </span>
		<a href="/signup" class="link link-primary">
			{$t('auth.signup')}
		</a>
	</div>
</Card>

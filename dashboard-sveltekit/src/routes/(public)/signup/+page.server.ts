import { validateSignupData } from '$lib/schemas/auth.schema';
import { authService } from '$lib/services/auth';
import { LogCategory, logger } from '$lib/utils/logger';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  return {
    user: null,
  };
};

export const actions: Actions = {
  /**
   * ✅ User Signup - Hybrid Approach
   * Route API + Service Pattern
   */
  signup: async ({ request, cookies, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const signupData = {
        email: formData.get('email')?.toString()?.trim(),
        password: formData.get('password')?.toString()?.trim(),
        confirmPassword: formData.get('confirmPassword')?.toString()?.trim(),
        agreeToTerms: formData.get('agreeToTerms') === 'on',
      };

      // ✅ Basic validation at route level
      if (!signupData.email || !signupData.password || !signupData.confirmPassword) {
        return fail(400, {
          message: 'กรุณากรอกข้อมูลให้ครบถ้วน',
          type: 'validation',
          fields: {
            email: !signupData.email ? 'กรุณากรอกอีเมล' : null,
            password: !signupData.password ? 'กรุณากรอกรหัสผ่าน' : null,
            confirmPassword: !signupData.confirmPassword ? 'กรุณายืนยันรหัสผ่าน' : null,
          },
        });
      }

      if (!signupData.agreeToTerms) {
        return fail(400, {
          message: 'กรุณายอมรับข้อตกลงการใช้งาน',
          type: 'validation',
          fields: {
            agreeToTerms: 'กรุณายอมรับข้อตกลงการใช้งาน',
          },
        });
      }

      // ✅ Schema validation using Zod
      const validation = validateSignupData(signupData);
      if (!validation.success) {
        return fail(400, {
          message: validation.error || 'ข้อมูลไม่ถูกต้อง',
          type: 'validation',
        });
      }

      // ✅ Call auth service for business logic + backend API
      const result = await authService.signup({
        email: signupData.email,
        password: signupData.password,
        confirmPassword: signupData.confirmPassword,
        agreeToTerms: signupData.agreeToTerms,
      });

      if (!result.success) {
        logger.warn(LogCategory.AUTH, 'signup_service_failed', 'Auth service signup failed', {
          email: signupData.email?.substring(0, 3) + '***',
          error: result.error,
          clientIP,
          userAgent,
        });

        return fail(400, {
          message: result.error || result.message || 'ลงทะเบียนล้มเหลว',
          type: 'signup',
        });
      }

      // ✅ Set authentication cookies for successful signup
      if (result.data?.token) {
        const cookieOptions = {
          path: '/',
          httpOnly: true,
          secure: import.meta.env.NODE_ENV === 'production',
          sameSite: 'strict' as const,
          maxAge: 60 * 60 * 24 * 7, // 7 days
        };

        cookies.set('auth_token', result.data.token, cookieOptions);

        if (result.data.refreshToken) {
          cookies.set('refresh_token', result.data.refreshToken, cookieOptions);
        }
      }

      logger.info(LogCategory.AUTH, 'signup_success', 'User signup successful', {
        email: signupData.email?.substring(0, 3) + '***',
        userId: result.data?.user?.id,
        clientIP,
        userAgent,
      });

      // ✅ Return success response for SvelteKit
      return {
        success: true,
        user: result.data?.user,
        message: result.message || 'ลงทะเบียนสำเร็จ กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชี',
        type: 'signup',
      };
    }
    catch (error) {
      logger.error(LogCategory.AUTH, 'signup_action_error', 'Signup action error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
        userAgent,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลงทะเบียน กรุณาลองใหม่อีกครั้ง',
        type: 'server_error',
      });
    }
  },
};

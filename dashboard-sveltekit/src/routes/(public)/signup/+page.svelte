<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Input from '$lib/components/ui/Input.svelte';

	import { authStore } from '$lib/stores/auth.svelte';
	import { 
		showLoading,
		showRegisterError,
		showSuccessToast,
		showError
	} from '$lib/utils/sweetalert';
	import Swal from 'sweetalert2-neutral';
	import { Label } from '$lib/components/ui';
	import Checkbox from '$lib/components/ui/Checkbox.svelte';

	// ✅ Props from server
	const { form } = $props<{
		form?: any;
	}>();

	// ✅ Client state management
	let isLoading = $state(false);

	// ✅ Form data using $state (Svelte 5 compatible)
	let formData = $state({
		email: '',
		password: '',
		confirmPassword: '',
		agreeToTerms: false,
	});

	// ✅ Form validation errors
	let formErrors = $state<Record<string, string>>({});

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Derived state for form validation
	const isFormValid = $derived(
		// Basic validation
		formData.email && 
		formData.password && 
		formData.confirmPassword &&
		// Email validation
		/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
		// Password validation
		formData.password.length >= 6 &&
		// Confirm password validation
		formData.password === formData.confirmPassword &&
		// Terms agreement validation
		formData.agreeToTerms
	);

	// ✅ Debug validation state
	$effect(() => {
		console.log('Form validation state:', {
			isValid: isFormValid,
			data: formData,
			errors: formErrors,
			isLoading
		});
	});

	// ✅ Real-time validation
	$effect(() => {
		const errors: Record<string, string> = {};

		// Email validation
		if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = 'รูปแบบอีเมลไม่ถูกต้อง';
		}

		// Password validation
		if (formData.password && formData.password.length < 6) {
			errors.password = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
		}

		// Confirm password validation
		if (formData.confirmPassword && formData.password !== formData.confirmPassword) {
			errors.confirmPassword = 'รหัสผ่านไม่ตรงกัน';
		}

		formErrors = errors;
	});

	onMount(() => {
		// ถ้า login แล้วให้ redirect ไป dashboard
		if (authStore.isAuthenticated) {
			goto('/dashboard');
		}
	});

	/**
	 * ✅ Handle form results consistently - Hybrid Approach
	 */
	function handleFormResult(result: any) {
		isLoading = false;
		Swal.close();

		console.log('Signup form result:', result);

		if (result.type === 'success' && result.data?.success) {
			// ✅ Success case - แสดงข้อความสำเร็จและแจ้งให้ยืนยันอีเมล
			const successMessage = result.data.message || 'ลงทะเบียนสำเร็จ กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชี';
			showSuccessToast(successMessage);

			// ✅ Redirect to signin page for email verification
			setTimeout(() => {
				goto('/signin');
			}, 2000);
		} else if (result.type === 'failure') {
			// ✅ Handle different error types
			const errorData = result.data;
			console.log('err', errorData)
			const errorMessage = errorData?.message || errorData?.error || 'เกิดข้อผิดพลาดในการลงทะเบียน';
			console.log(errorMessage)

			// Show specific error messages
			switch (errorData?.type) {
				case 'validation':
					showError('ข้อมูลไม่ถูกต้อง', errorMessage);
					break;
				case 'signup':
					showError('ลงทะเบียนล้มเหลว', errorMessage);
					break;
				case 'server_error':
					showError('เกิดข้อผิดพลาด', errorMessage);
					break;
				default:
					showError('เกิดข้อผิดพลาด', errorMessage);
			}
		}
	}

	/**
	 * ✅ Handle Enter key submission
	 */
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const target = event.target as HTMLElement;
			const form = target?.closest('form') as HTMLFormElement;
			if (form && !isLoading && isFormValid) {
				form.requestSubmit();
			}
		}
	}


// Terms & Conditions
const termList = [
  {
    icon: 'solar:info-circle-bold',
    label: 'การมีสิทธิ์ของผู้ใช้',
    content:
      'เว็บไซต์นี้เปิดให้บริการเฉพาะแก่องค์กรและบุคคลที่บรรลุนิติภาวะและมีความเหมาะสมที่จะทำธุรกรรมที่มีผลผูกมัดทางกฎหมายตามข้อกฎหมายที่เกี่ยวข้องได้ หากคุณไม่มีคุณสมบัติเหมาะสม ไม่อนุญาตให้ใช้งานเว็บไซต์',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การแก้ไขเปลี่ยนแปลงข้อตกลง',
    content:
      'ทางผู้ให้บริการขอสงวนสิทธิที่จะแก้ไขหรือเปลี่ยนแปลงข้อตกลงการใช้งานที่ระบุไว้ในเว็บไซต์นี้ ท่านมีหน้าที่ตรวจสอบข้อตกลงการใช้งานเว็บไซต์นี้ รวมถึงข้อกำหนดเพิ่มเติมใดๆ ที่ระบุไว้ในเว็บไซต์ของเราอย่างสม่ำเสมอ',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การใช้และการเปิดเผยข้อมูลส่วนบุคคล',
    content:
      'เว็บไซต์แห่งนี้ อนุญาติให้ผู้ใช้งาน เสพข้อมูล เพิ่มเนื้อหาเองได้โดยอิสระ และทางผู้ให้บริการจะไม่ขอรับผิดชอบกับเนื้อหาที่ผู้ใช้งาน เขียน อัพโหลด หรือเพิ่มเข้ามาในเว็บไซต์แห่งนี้',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การชดใช้ค่าเสียหาย',
    content:
      'ท่านตกลงว่าจะชดใช้ค่าเสียหายให้แก่ทางเราสำหรับข้อเรียกร้อง ความสูญเสีย ค่าใช้จ่าย และความเสียหายใดๆ ที่ทางเราต้องรับภาระหรือเกิดขึ้นแก่การให้บริการเว็บไซต์หรือบริการต่างๆ',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'นโยบายคุ้มครองข้อมูลส่วนบุคคล',
    content:
      'ข้อมูลส่วนบุคคลของผู้ใช้งานสำหรับการใช้งานเว็บไซต์นี้ ได้รับความคุ้มครองภายใต้นโยบายคุ้มครองข้อมูลส่วนบุคคล (Privacy Policy)',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'นโยบายการรักษาความมั่นคงปลอดภัย',
    content:
      'ทางเราได้เลือกใช้เทคโนโลยี มาตรการรักษาความมั่นคงปลอดภัยในการทำธุรกรรมบนเครือข่ายอินเทอร์เน็ต เพื่อป้องกันข้อมูลของผู้ใช้งาน',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'การละเมิดข้อตกลงและเงื่อนไข',
    content:
      'ในกรณีที่มีการละเมิดข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์ของเรา ทางเราขอสงวนสิทธิ์ที่จะระงับการเข้าถึงหรือยกเลิกการให้บริการใด ๆ',
  },
  {
    icon: 'solar:info-circle-bold',
    label: 'กฎหมายที่บังคับใช้',
    content: 'ข้อตกลงและเงื่อนไขการการใช้งานเว็บไซต์นี้อยู่ภายใต้การบังคับใช้แห่งกฎหมายไทย',
  },
]
</script>

<SEO title={$t('auth.signup')} />
<div class="flex flex-col md:flex-row w-full gap-5">
	<!-- Terms & Conditions -->
	<div class="w-full sm:min-w-sm space-y-5">
	  <h2 class="text-lg text-center font-bold">
		ข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์
	  </h2>
	  <div class="join join-vertical bg-base-100">
		{#each termList as term }
		<div class="collapse collapse-arrow join-item border-base-300 border">
		  <input type="radio" name="my-accordion-1"  />
		  <div class="collapse-title font-semibold">{term.label}</div>
		  <div class="collapse-content text-sm">{term.content}</div>
		</div> 
		{/each}
	  </div>

	</div>

<Card centered variant="default" shadow="lg" size="sm" title={$t('auth.signup')}>
	<!-- ✅ Form Action Messages - Progressive Enhancement -->
	{#if formResult?.success}
		<div class="alert alert-success">
			<Icon icon="mdi:check-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{:else if formResult?.message}
		<div class="alert alert-error">
			<Icon icon="mdi:alert-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{/if}

	<!-- ✅ Signup Form - Hybrid Approach with use:enhance -->
	<form
		method="POST"
		action="?/signup"
		class="space-y-4"
		use:enhance={() => {
			isLoading = true;
			showLoading('กำลังสมัครสมาชิก...');

			return async ({ result }) => {
				handleFormResult(result);
			};
		}}
	>
		<Input
			id="email"
			name="email"
			type="email"
			variant="bordered"
			bind:value={formData.email}
			label={$t('auth.email')}
			placeholder="<EMAIL>"
			icon="solar:letter-line-duotone"
			validate={formErrors.email}
			required
			autocomplete="email"
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<Input
			id="password"
			name="password"
			type="password"
			bind:value={formData.password}
			label={$t('auth.password')}
			placeholder="••••••••"
			icon="solar:key-minimalistic-square-2-line-duotone"
			validate={formErrors.password}
			required
			showPasswordToggle
			autocomplete="new-password"
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<Input
			id="confirmPassword"
			name="confirmPassword"
			type="password"
			bind:value={formData.confirmPassword}
			label={$t('auth.confirmPassword')}
			placeholder="••••••••"
			icon="solar:key-square-line-duotone"
			validate={formErrors.confirmPassword}
			required
			showPasswordToggle
			autocomplete="new-password"
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<div class="flex items-start gap-2">
			<Checkbox
				id="agreeToTerms"
				name="agreeToTerms"
				label={$t('auth.agreeToTerms') + $t('auth.termsOfService')} 
				bind:checked={formData.agreeToTerms} 
			/> 
		</div>

		{#if authStore.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{authStore.error}</span>
			</div>
		{/if}

		<!-- ✅ Submit button with proper loading state -->
		<Button
			type="submit"
			color="primary"
			size="lg"
			block
			loading={isLoading}
			disabled={isLoading || !isFormValid}
			label={$t('auth.signup')} />

		<!-- ✅ Debug information for development -->
		{#if import.meta.env.DEV}
			<div class="text-xs text-gray-500 mt-2">
				<div>Form Valid: {isFormValid}</div>
				<div>Loading: {isLoading}</div>
				<div>Agree to Terms: {formData.agreeToTerms}</div>
			</div>
		{/if}
	</form>

	<!-- Login Link -->
	<div class="text-center mt-6">
		<span class="text-base-content/60">{$t('auth.hasAccount')} </span>
		<a href="/signin" class="link link-primary">
			{$t('auth.signin')}
		</a>
	</div>
</Card>
</div>


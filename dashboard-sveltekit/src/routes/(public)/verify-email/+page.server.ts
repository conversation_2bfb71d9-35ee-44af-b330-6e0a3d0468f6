import { authService } from '$lib/services/auth';
import { LogCategory, logger } from '$lib/utils/logger';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, url }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  // ตรวจสอบ token จาก URL
  const token = url.searchParams.get('token');

  return {
    user: null,
    token: token || null,
  };
};

export const actions: Actions = {
  /**
   * ✅ Verify Email - Hybrid Approach
   * Route API + Service Pattern
   */
  verifyEmail: async ({ request, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const verifyData = {
        token: formData.get('token')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!verifyData.token) {
        logger.warn(LogCategory.AUTH, 'verify_email_no_token', 'No token provided', {
          clientIP,
          userAgent,
        });

        return fail(400, {
          message: 'Token ไม่ถูกต้อง',
          type: 'verification',
          fields: {
            token: 'กรุณากรอก Token ยืนยันอีเมล',
          },
        });
      }

      logger.info(LogCategory.AUTH, 'verify_email_attempt', 'Email verification attempt', {
        tokenLength: verifyData.token.length,
        clientIP,
        userAgent,
      });

      // ✅ Call service for business logic
      const result = await authService.verifyEmail({ token: verifyData.token });

      if (!result.success) {
        logger.warn(LogCategory.AUTH, 'verify_email_failed', 'Email verification failed', {
          error: result.error,
          tokenLength: verifyData.token.length,
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถยืนยันอีเมลได้',
          type: 'verification',
        });
      }

      logger.info(LogCategory.AUTH, 'verify_email_success', 'Email verification successful', {
        tokenLength: verifyData.token.length,
        clientIP,
      });

      return {
        success: true,
        message: 'ยืนยันอีเมลสำเร็จ! กรุณาเข้าสู่ระบบ',
        type: 'verification',
      };
    }
    catch (error) {
      logger.error(LogCategory.AUTH, 'verify_email_error', 'Email verification error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการยืนยันอีเมล',
        type: 'verification',
      });
    }
  },

  /**
   * ✅ Resend Verification Email - Hybrid Approach
   * Route API + Service Pattern
   */
  resendVerification: async ({ request, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    try {
      const formData = await request.formData();

      // ✅ Route-level validation - Extract and validate form data
      const resendData = {
        email: formData.get('email')?.toString()?.trim(),
      };

      // ✅ Basic validation at route level
      if (!resendData.email) {
        return fail(400, {
          message: 'กรุณากรอกอีเมล',
          type: 'resend',
          fields: {
            email: 'กรุณากรอกอีเมล',
          },
        });
      }

      // Email format validation
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(resendData.email)) {
        return fail(400, {
          message: 'รูปแบบอีเมลไม่ถูกต้อง',
          type: 'resend',
          fields: {
            email: 'รูปแบบอีเมลไม่ถูกต้อง',
          },
        });
      }

      logger.info(LogCategory.AUTH, 'resend_verification_attempt', 'Resend verification attempt', {
        email: resendData.email.substring(0, 3) + '***',
        clientIP,
        userAgent,
      });

      // ✅ Call service for business logic
      const result = await authService.resendVerification({ email: resendData.email });

      if (!result.success) {
        logger.warn(LogCategory.AUTH, 'resend_verification_failed', 'Resend verification failed', {
          error: result.error,
          email: resendData.email.substring(0, 3) + '***',
          clientIP,
        });

        return fail(400, {
          message: result.error || 'ไม่สามารถส่งอีเมลยืนยันได้',
          type: 'resend',
        });
      }

      logger.info(LogCategory.AUTH, 'resend_verification_success', 'Resend verification successful', {
        email: resendData.email.substring(0, 3) + '***',
        clientIP,
      });

      return {
        success: true,
        message: 'ส่งอีเมลยืนยันใหม่สำเร็จ กรุณาตรวจสอบอีเมลของคุณ',
        type: 'resend',
      };
    }
    catch (error) {
      logger.error(LogCategory.AUTH, 'resend_verification_error', 'Resend verification error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        clientIP,
      });

      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการส่งอีเมลยืนยัน',
        type: 'resend',
      });
    }
  },
};

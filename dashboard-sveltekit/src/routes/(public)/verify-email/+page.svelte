<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';

	// ✅ Props from server
	const { data, form } = $props<{
		data: { token: string | null; user: any };
		form?: any;
	}>();

	// ✅ Client state management
	let isVerifyLoading = $state(false);
	let isResendLoading = $state(false);

	// ✅ Form data using $state (Svelte 5 compatible)
	let verifyFormData = $state({
		token: data.token || '',
	});

	let resendFormData = $state({
		email: '',
	});

	// ✅ Form validation errors
	let formErrors = $state<Record<string, string>>({});

	// ✅ Derived state for form result
	const formResult = $derived(form);

	// ✅ Derived state for form validation
	const isVerifyFormValid = $derived(
		verifyFormData.token && verifyFormData.token.trim().length > 0
	);

	const isResendFormValid = $derived(
		resendFormData.email && 
		/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(resendFormData.email)
	);

	// ✅ Auto-verify if token exists in URL
	onMount(() => {
		if (data.token && data.token.trim()) {
			// Auto-submit verification form
			const form = document.querySelector('form[action="?/verifyEmail"]') as HTMLFormElement;
			if (form) {
				form.requestSubmit();
			}
		}
	});

	// ✅ Handle form results using $effect
	$effect(() => {
		if (formResult?.success) {
			if (formResult.type === 'verification') {
				showSuccess('ยืนยันอีเมลสำเร็จ!', formResult.message);
				setTimeout(() => {
					goto('/signin');
				}, 2000);
			} else if (formResult.type === 'resend') {
				showSuccess('ส่งอีเมลสำเร็จ!', formResult.message);
			}
		} else if (formResult?.message) {
			showError('เกิดข้อผิดพลาด', formResult.message);
		}
	});

	// ✅ Handle form submission results
	function handleVerifyResult(result: any) {
		isVerifyLoading = false;

		if (result.type === 'success') {
			showSuccess('ยืนยันอีเมลสำเร็จ!', result.data?.message);
			setTimeout(() => {
				goto('/signin');
			}, 2000);
		} else if (result.type === 'failure') {
			showError('ยืนยันอีเมลไม่สำเร็จ', result.data?.message);
			
			// Handle field-specific errors
			if (result.data?.fields) {
				formErrors = result.data.fields;
			}
		}
	}

	function handleResendResult(result: any) {
		isResendLoading = false;

		if (result.type === 'success') {
			showSuccess('ส่งอีเมลสำเร็จ!', result.data?.message);
			// Clear form
			resendFormData.email = '';
		} else if (result.type === 'failure') {
			showError('ส่งอีเมลไม่สำเร็จ', result.data?.message);
			
			// Handle field-specific errors
			if (result.data?.fields) {
				formErrors = { ...formErrors, ...result.data.fields };
			}
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const target = event.target as HTMLElement;
			const form = target?.closest('form') as HTMLFormElement;
			if (form) {
				form.requestSubmit();
			}
		}
	}
</script>

<SEO
	title={$t('auth.verifyEmailTitle')}
	description={$t('auth.verifyEmailDescription')}
	keywords="ยืนยันอีเมล, เปิดใช้งานบัญชี, ยืนยันตัวตน"
	url="/verify-email"
	noindex={true}
/>

<div
	class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4"
>
	<Card class="w-full max-w-md p-8 space-y-4">
		<!-- Header -->
		<div class="text-center space-y-2">
			<div class="flex justify-center mb-4">
				<div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
					<Icon icon="mdi:email-check" class="w-6 h-6 text-primary-foreground" />
				</div>
			</div>
			<h1 class="text-2xl font-bold text-foreground">{$t('auth.verifyEmailTitle')}</h1>
			<p class="text-muted-foreground">
				{#if data.token}
					{$t('auth.verifyingEmail')}
				{:else}
					{$t('auth.verifyEmailTokenPlaceholder')}
				{/if}
			</p>
		</div>

		<!-- ✅ Verify Email Form - Always show for manual token entry -->
		{#if !data.token}
			<form
				method="POST"
				action="?/verifyEmail"
				use:enhance={() => {
					isVerifyLoading = true;
					showLoading('กำลังยืนยันอีเมล...');

					return async ({ result }) => {
						handleVerifyResult(result);
					};
				}}
				class="space-y-4"
			>
				<Input
					id="token"
					name="token"
					type="text"
					bind:value={verifyFormData.token}
					placeholder="กรอก Token ที่ได้รับจากอีเมล"
					label="Token ยืนยันอีเมล"
					icon="mdi:key"
					error={formErrors.token}
					required
					onkeydown={handleKeydown}
				/>

				<Button
					type="submit"
					color="primary"
					size="lg"
					block
					loading={isVerifyLoading}
					disabled={isVerifyLoading || !isVerifyFormValid}
				>
					{isVerifyLoading ? 'กำลังยืนยัน...' : 'ยืนยันอีเมล'}
				</Button>
			</form>

			<div class="relative">
				<div class="absolute inset-0 flex items-center">
					<div class="w-full border-t border-border"></div>
				</div>
				<div class="relative flex justify-center text-sm">
					<span class="px-2 bg-card text-muted-foreground">หรือ</span>
				</div>
			</div>

			<!-- ✅ Resend Verification Form -->
			<form
				method="POST"
				action="?/resendVerification"
				use:enhance={() => {
					isResendLoading = true;
					showLoading('กำลังส่งอีเมล...');

					return async ({ result }) => {
						handleResendResult(result);
					};
				}}
				class="space-y-4"
			>
				<div class="text-center mb-4">
					<p class="text-sm text-muted-foreground">ไม่ได้รับอีเมลยืนยัน?</p>
				</div>

				<Input
					id="email"
					name="email"
					type="email"
					bind:value={resendFormData.email}
					placeholder="<EMAIL>"
					label="อีเมล"
					icon="mdi:email"
					error={formErrors.email}
					required
					autocomplete="email"
					onkeydown={handleKeydown}
				/>

				<Button
					type="submit"
					color="secondary"
					size="lg"
					block
					loading={isResendLoading}
					disabled={isResendLoading || !isResendFormValid}
				>
					{isResendLoading ? 'กำลังส่ง...' : 'ส่งอีเมลยืนยันใหม่'}
				</Button>
			</form>
		{:else}
			<!-- ✅ Auto-verification form (hidden) -->
			<form
				method="POST"
				action="?/verifyEmail"
				use:enhance={() => {
					isVerifyLoading = true;
					showLoading('กำลังยืนยันอีเมล...');

					return async ({ result }) => {
						handleVerifyResult(result);
					};
				}}
				class="hidden"
			>
				<input type="hidden" name="token" value={data.token} />
			</form>

			<div class="text-center">
				{#if isVerifyLoading}
					<div class="flex items-center justify-center mb-4">
						<Icon icon="mdi:loading" class="animate-spin w-8 h-8 text-primary" />
					</div>
					<p class="text-muted-foreground">กำลังยืนยันอีเมล...</p>
				{:else}
					<div class="flex items-center justify-center mb-4">
						<Icon icon="mdi:email-check" class="w-8 h-8 text-primary" />
					</div>
					<p class="text-muted-foreground">พบ Token ในลิงก์ กำลังดำเนินการยืนยัน...</p>
				{/if}
			</div>
		{/if}

		<div class="text-center">
			<a href="/signin" class="text-primary hover:text-primary-focus">กลับไปหน้าเข้าสู่ระบบ</a>
		</div>
	</Card>
</div>

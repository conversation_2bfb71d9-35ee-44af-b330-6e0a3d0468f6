import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

// Mock data - ในการใช้งานจริงจะเชื่อมต่อกับ database
const mockAddons = [
  {
    id: 'news',
    siteId: 'site1',
    name: 'ระบบข่าวสาร',
    isActive: true,
    rentedAt: new Date('2024-01-01'),
    expiresAt: new Date('2024-12-31'),
  },
];

export const GET: RequestHandler = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ในการใช้งานจริง จะดึงข้อมูลจาก database
    const siteAddons = mockAddons.filter(addon => addon.siteId === siteId);

    return json(siteAddons);
  }
  catch (error) {
    console.error('Error fetching addons:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

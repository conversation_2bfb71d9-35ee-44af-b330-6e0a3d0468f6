import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ params, locals }) => {
  const { siteId, addonId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ตรวจสอบว่าได้เช่า addon นี้แล้วหรือไม่
    // ในการใช้งานจริงจะตรวจสอบจาก database

    // เปิดใช้งาน addon
    // ในการใช้งานจริงจะอัปเดตใน database
    console.log(`Activating addon ${addonId} for site ${siteId}`);

    // เรียก backend API เพื่อเปิดใช้งาน
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL || 'http://localhost:3000'}/v1/addons/sites/${siteId}/${addonId}/activate`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${locals.token}`,
        },
      },
    );

    if (!backendResponse.ok) {
      const backendError = await backendResponse.json();
      return json({ error: backendError.message || 'ไม่สามารถเปิดใช้งาน addon ได้' }, { status: backendResponse.status });
    }

    return json({
      success: true,
      message: 'เปิดใช้งานระบบเสริมเรียบร้อยแล้ว',
    });
  }
  catch (error) {
    console.error('Error activating addon:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

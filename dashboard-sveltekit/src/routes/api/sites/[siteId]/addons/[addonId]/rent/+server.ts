import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ params, locals }) => {
  const { siteId, addonId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ตรวจสอบว่า addon นี้มีอยู่จริงหรือไม่
    const validAddons = ['news', 'blog', 'novel'];
    if (!validAddons.includes(addonId)) {
      return json({ error: 'Invalid addon' }, { status: 400 });
    }

    // ตรวจสอบว่าเช่าแล้วหรือยัง
    // ในการใช้งานจริงจะตรวจสอบจาก database

    // สร้างการเช่าใหม่
    const newRental = {
      id: addonId,
      siteId,
      name: getAddonName(addonId),
      isActive: false, // เช่าแล้วแต่ยังไม่เปิดใช้งาน
      rentedAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 วัน
    };

    // ในการใช้งานจริงจะบันทึกลง database
    console.log('New rental created:', newRental);

    // เรียก backend API เพื่อสร้างการเช่า
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL || 'http://localhost:3000'}/v1/addons/sites/${siteId}/${addonId}/rent`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${locals.token}`,
        },
      },
    );

    if (!backendResponse.ok) {
      const backendError = await backendResponse.json();
      return json({ error: backendError.message || 'ไม่สามารถเช่า addon ได้' }, { status: backendResponse.status });
    }

    const backendResult = await backendResponse.json();

    return json({
      success: true,
      message: 'เช่าระบบเสริมเรียบร้อยแล้ว',
      rental: backendResult.data,
    });
  }
  catch (error) {
    console.error('Error renting addon:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

function getAddonName(addonId: string): string {
  const names: Record<string, string> = {
    news: 'ระบบข่าวสาร',
    blog: 'ระบบบล็อก',
    novel: 'ระบบนิยาย',
  };
  return names[addonId] || addonId;
}

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// Mock data - ในการใช้งานจริงจะเชื่อมต่อกับ database
const mockBlogs = [
  {
    id: 'blog1',
    siteId: 'site1',
    title: 'บล็อกตัวอย่าง',
    content: '<p>นี่คือเนื้อหาบล็อกตัวอย่าง</p>',
    excerpt: 'นี่คือสรุปบล็อกตัวอย่าง',
    published: true,
    featuredImage: null,
    tags: ['ตัวอย่าง', 'บล็อก'],
    authorId: 'user1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

export const GET: RequestHandler = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ในการใช้งานจริงจะดึงข้อมูลจาก database
    const siteBlogs = mockBlogs.filter(blog => blog.siteId === siteId);

    return json(siteBlogs);
  }
  catch (error) {
    console.error('Error fetching blogs:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ params, locals, request }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();
    const { title, content, excerpt, tags, published, featuredImage, authorId } = data;

    // Validation
    if (!title || !content) {
      return json({ error: 'กรุณากรอกหัวข้อและเนื้อหา' }, { status: 400 });
    }

    // สร้างบล็อกใหม่
    const newBlog = {
      id: `blog_${Date.now()}`,
      siteId,
      title,
      content,
      excerpt: excerpt || content.replace(/<[^>]*>/g, '').substring(0, 200) + '...',
      published: published || false,
      featuredImage: featuredImage || null,
      tags: tags || [],
      authorId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // ในการใช้งานจริงจะบันทึกลง database
    console.log('New blog created:', newBlog);

    return json({
      success: true,
      message: 'สร้างบล็อกเรียบร้อยแล้ว',
      blog: newBlog,
    });
  }
  catch (error) {
    console.error('Error creating blog:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

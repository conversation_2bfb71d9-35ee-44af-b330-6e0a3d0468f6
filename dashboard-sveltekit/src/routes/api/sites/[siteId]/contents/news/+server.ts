import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// Mock data - ในการใช้งานจริงจะเชื่อมต่อกับ database
const mockNews = [
  {
    id: 'news1',
    siteId: 'site1',
    title: 'ข่าวสารตัวอย่าง',
    content: '<p>นี่คือเนื้อหาข่าวสารตัวอย่าง</p>',
    excerpt: 'นี่คือสรุปข่าวสารตัวอย่าง',
    published: true,
    featuredImage: null,
    categoryId: 'cat1',
    category: { id: 'cat1', name: 'ข่าวทั่วไป' },
    authorId: 'user1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

export const GET: RequestHandler = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ในการใช้งานจริงจะดึงข้อมูลจาก database
    const siteNews = mockNews.filter(news => news.siteId === siteId);

    return json(siteNews);
  }
  catch (error) {
    console.error('Error fetching news:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ params, locals, request }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();
    const { title, content, excerpt, categoryId, published, featuredImage, authorId } = data;

    // Validation
    if (!title || !content) {
      return json({ error: 'กรุณากรอกหัวข้อและเนื้อหา' }, { status: 400 });
    }

    // เรียก backend API
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL || 'http://localhost:3000'}/v1/content/news/sites/${siteId}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${locals.token}`,
        },
        body: JSON.stringify({
          title,
          content,
          excerpt: excerpt || content.replace(/<[^>]*>/g, '').substring(0, 200) + '...',
          categoryId: categoryId || null,
          published: published || false,
          featuredImage: featuredImage || null,
        }),
      },
    );

    if (!backendResponse.ok) {
      const backendError = await backendResponse.json();
      return json({ error: backendError.message || 'ไม่สามารถสร้างข่าวได้' }, { status: backendResponse.status });
    }

    const backendResult = await backendResponse.json();

    return json({
      success: true,
      message: 'สร้างข่าวเรียบร้อยแล้ว',
      news: backendResult.data,
    });
  }
  catch (error) {
    console.error('Error creating news:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// Mock categories data
const mockCategories = [
  {
    id: 'cat1',
    siteId: 'site1',
    name: 'ข่าวทั่วไป',
    slug: 'general-news',
    description: 'ข่าวสารทั่วไป',
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'cat2',
    siteId: 'site1',
    name: 'ประชาสัมพันธ์',
    slug: 'announcements',
    description: 'ข่าวประชาสัมพันธ์',
    createdAt: new Date('2024-01-01'),
  },
];

export const GET: RequestHandler = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ในการใช้งานจริงจะดึงข้อมูลจาก database
    const siteCategories = mockCategories.filter(cat => cat.siteId === siteId);

    return json(siteCategories);
  }
  catch (error) {
    console.error('Error fetching categories:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ params, locals, request }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();
    const { name, description } = data;

    // Validation
    if (!name) {
      return json({ error: 'กรุณากรอกชื่อหมวดหมู่' }, { status: 400 });
    }

    // สร้าง slug จากชื่อ
    const slug = name.toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');

    // สร้างหมวดหมู่ใหม่
    const newCategory = {
      id: `cat_${Date.now()}`,
      siteId,
      name,
      slug,
      description: description || '',
      createdAt: new Date(),
    };

    // ในการใช้งานจริงจะบันทึกลง database
    console.log('New category created:', newCategory);

    return json({
      success: true,
      message: 'สร้างหมวดหมู่เรียบร้อยแล้ว',
      category: newCategory,
    });
  }
  catch (error) {
    console.error('Error creating category:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

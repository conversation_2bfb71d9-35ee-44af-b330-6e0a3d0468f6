import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// Mock data - ในการใช้งานจริงจะเชื่อมต่อกับ database
const mockNovels = [
  {
    id: 'novel1',
    siteId: 'site1',
    title: 'นิยายตัวอย่าง',
    description: 'นี่คือคำอธิบายนิยายตัวอย่าง',
    genre: 'แฟนตาซี',
    published: true,
    completed: false,
    coverImage: null,
    tags: ['แฟนตาซี', 'ผจญภัย'],
    authorId: 'user1',
    chaptersCount: 5,
    viewsCount: 100,
    likesCount: 10,
    rating: 4.5,
    ratingCount: 20,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

export const GET: RequestHandler = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ในการใช้งานจริงจะดึงข้อมูลจาก database
    const siteNovels = mockNovels.filter(novel => novel.siteId === siteId);

    return json(siteNovels);
  }
  catch (error) {
    console.error('Error fetching novels:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ params, locals, request }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();
    const { title, description, genre, tags, published, coverImage, authorId } = data;

    // Validation
    if (!title || !description) {
      return json({ error: 'กรุณากรอกชื่อเรื่องและคำอธิบาย' }, { status: 400 });
    }

    // สร้างนิยายใหม่
    const newNovel = {
      id: `novel_${Date.now()}`,
      siteId,
      title,
      description,
      genre: genre || null,
      tags: tags || [],
      published: published || false,
      completed: false,
      coverImage: coverImage || null,
      authorId,
      chaptersCount: 0,
      viewsCount: 0,
      likesCount: 0,
      rating: 0,
      ratingCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // ในการใช้งานจริงจะบันทึกลง database
    console.log('New novel created:', newNovel);

    return json({
      success: true,
      message: 'สร้างนิยายเรียบร้อยแล้ว',
      novel: newNovel,
    });
  }
  catch (error) {
    console.error('Error creating novel:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

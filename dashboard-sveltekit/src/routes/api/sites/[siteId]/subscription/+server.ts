import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// Mock subscription data
const mockSubscription = {
  id: 'sub_123',
  siteId: 'site1',
  planName: 'Premium',
  planPrice: 999,
  status: 'active',
  createdAt: new Date('2024-01-01'),
  expiresAt: new Date('2024-12-31'),
};

export const GET: RequestHandler = async ({ params, locals }) => {
  const { siteId } = params;

  if (!locals.user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // ในการใช้งานจริงจะดึงข้อมูลจาก database
    // const subscription = await db.subscriptions.findOne({ siteId });

    return json(mockSubscription);
  }
  catch (error) {
    console.error('Error fetching subscription:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

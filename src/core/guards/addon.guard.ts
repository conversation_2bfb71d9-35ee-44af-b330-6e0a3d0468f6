import { Elysia } from 'elysia';
import { AddonService } from '../../modules/addon/addon.service';
import { HttpError } from '../utils/error';

export const addonGuard = (requiredAddon: string) => {
  return new Elysia({ name: `addon-guard-${requiredAddon}` })
    .decorate('addonService', new AddonService())
    .derive(async ({ params, addonService }) => {
      const siteId = (params as any)?.siteId;

      if (!siteId) {
        throw new HttpError(400, 'Site ID is required');
      }

      try {
        // ตรวจสอบว่า addon เปิดใช้งานหรือไม่
        const isActive = await addonService.isAddonActive(siteId, requiredAddon);

        if (!isActive) {
          throw new HttpError(403, `ระบบ ${requiredAddon} ยังไม่ได้เปิดใช้งาน กรุณาเช่าและเปิดใช้งานก่อน`);
        }

        return { addonActive: true, requiredAddon };
      }
      catch (err) {
        if (err instanceof HttpError) {
          throw err;
        }
        console.error('Addon guard error:', err);
        throw new HttpError(500, 'Internal server error');
      }
    })
    .as('scoped');
};

import { AccessCheckOptions, AccessControlService } from '@/core/services/access-control.service';
import { logger } from '@/core/utils/logger';
import { Site } from '@/modules/site/site.model';
import { getUserById } from '@/modules/user/user.service';
import { Elysia } from 'elysia';

// Re-export types for backward compatibility
export interface RoleCheckResult {
  hasPermission: boolean;
  role?: string;
  permissions?: string[];
}

export interface RoleCheckOptions extends AccessCheckOptions {
  requirePermission?: boolean;
}

// Helper function สำหรับตรวจสอบ role และ permission (wrapper for backward compatibility)
export const checkUserRole = async (siteId: string, userId: string): Promise<RoleCheckResult> => {
  const accessResult = await AccessControlService.checkUserAccess(siteId, userId);

  return {
    hasPermission: accessResult.hasAccess,
    role: accessResult.role,
    permissions: accessResult.permissions,
  };
};

// Helper function สำหรับตรวจสอบ permission เฉพาะ
export const checkPermission = async (siteId: string, userId: string, requiredPermission: string): Promise<boolean> => {
  return await AccessControlService.checkPermission(siteId, userId, requiredPermission, 'user');
};

// Function สำหรับใช้ใน beforeHandle
export const checkSiteAccess = async (c: any, options: RoleCheckOptions = {}) => {
  const { params, user, set, store } = c;
  const siteId = params?.siteId;
  const userId = user?.userId;

  if (!siteId || !userId) {
    set.status = 400;
    throw new Error('ข้อมูลไม่ครบถ้วน');
  }

  try {
    const accessResult = await AccessControlService.checkUserAccess(siteId, userId);
    const validation = AccessControlService.validateAccess(accessResult, options);

    if (!validation.isValid) {
      set.status = 403;
      throw new Error(validation.error || 'ไม่มีสิทธิ์เข้าถึง');
    }

    // เพิ่มข้อมูลลงใน store
    store.site = await Site.findById(siteId);
    store.siteId = siteId;
    store.userId = userId;
    store.userRole = accessResult.role;
    store.userPermissions = accessResult.permissions;
    store.hasPermission = accessResult.hasAccess;
  }
  catch (error) {
    logger.warn('Site access check failed', {
      siteId,
      userId,
      options,
      error: error instanceof Error ? error.message : String(error),
    });

    throw error;
  }
};

// Function สำหรับใช้ใน beforeHandle
export const getResultUser = async (c: any) => {
  const { user, set, store } = c;
  const userId = user?.userId;

  if (!userId) {
    set.status = 400;
    throw new Error('ข้อมูลไม่ครบถ้วน');
  }

  const resultUser = await getUserById(userId);

  if (!resultUser) {
    set.status = 404;
    throw new Error('ไม่พบผู้ใช้');
  }

  store.user = resultUser;

  return resultUser;
};

// Helper functions สำหรับตรวจสอบ role เฉพาะ
export const isSiteOwner = async (siteId: string, userId: string): Promise<boolean> => {
  const result = await AccessControlService.isSiteOwner(siteId, userId);
  console.log('🔍 isSiteOwner', result);
  return result;
};

export const isSiteAdmin = async (siteId: string, userId: string): Promise<boolean> => {
  return await AccessControlService.isSiteAdmin(siteId, userId);
};

// Function สำหรับตรวจสอบ role แบบละเอียด
export const checkRoleSite = async (
  siteId: string,
  userId: string,
  options: RoleCheckOptions = {},
): Promise<RoleCheckResult> => {
  const roleCheck = await checkUserRole(siteId, userId);

  if (!roleCheck.hasPermission) {
    return roleCheck;
  }

  // ตรวจสอบ required role
  if (options.requiredRole && roleCheck.role !== options.requiredRole) {
    return { hasPermission: false, role: roleCheck.role, permissions: roleCheck.permissions };
  }

  // ตรวจสอบ allow roles
  if (options.allowRoles && !options.allowRoles.includes(roleCheck.role || '')) {
    return { hasPermission: false, role: roleCheck.role, permissions: roleCheck.permissions };
  }

  return roleCheck;
};

// Plugin สำหรับใช้ใน .use()
export const checkUser = new Elysia({ name: 'checkUser' }).derive(async (context: any) => {
  const { params, user, store } = context;
  if (params?.siteId && user?.userId) {
    const roleCheck = await checkUserRole(params.siteId, user.userId);
    store.userRole = roleCheck.role;
    store.userPermissions = roleCheck.permissions;
    store.hasPermission = roleCheck.hasPermission;
  }
});

export const userPlugin = new Elysia({ name: 'user' }).derive(async (context: any) => {
  const { params, user, store, headers } = context;

  // ดึง siteId จาก params หรือ headers
  const siteId = params?.siteId || headers?.['x-site-id'] || 'default-site';

  if (siteId && user?.userId) {
    const roleCheck = await checkUserRole(siteId, user.userId);
    store.siteId = siteId; // เพิ่ม siteId ใน store
    store.userRole = roleCheck.role;
    store.userPermissions = roleCheck.permissions;
    store.hasPermission = roleCheck.hasPermission;
  }
  else {
    // ตั้งค่า default siteId ถ้าไม่มี
    store.siteId = siteId || 'default-site';
  }
});

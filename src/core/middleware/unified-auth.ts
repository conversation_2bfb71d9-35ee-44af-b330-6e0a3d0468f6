import { standardResponseSchema } from '@/core/schemas/response.schema';
import { AccessControlService } from '@/core/services/access-control.service';
import { AccessCheckOptions } from '@/core/services/access-control.service';
import { extractBearerToken, verifyCustomerToken, verifyUserToken } from '@/core/services/jwt.service';
import { HttpError } from '@/core/utils/error';
import { logger } from '@/core/utils/logger';
import { Customer } from '@/modules/customer/customer.model';
import { Site } from '@/modules/site/site.model';
import { User } from '@/modules/user/user.model';
import { Elysia } from 'elysia';

// Unified Auth Options
export interface UnifiedAuthOptions extends AccessCheckOptions {
  type?: 'user' | 'customer' | 'both';
  requireAuth?: boolean;
  requireSiteAccess?: boolean;
  loadUserData?: boolean;
  loadSiteData?: boolean;
}

/**
 * สร้าง unified auth middleware ที่รองรับทั้ง user และ customer
 */
export const createUnifiedAuth = (options: UnifiedAuthOptions = {}) => {
  const {
    type = 'user',
    requireAuth = true,
    requireSiteAccess = false,
    loadUserData = true,
    loadSiteData = false,
    ...accessOptions
  } = options;

  return new Elysia({ name: `unified-auth-${type}` })
    .derive(async ({ headers, params, store }: any) => {
      const bearer = extractBearerToken(headers['authorization']);
      const siteId = params?.siteId;

      // Initialize store
      store.user = null;
      store.customer = null;
      store.site = null;
      store.hasAccess = false;
      store.userRole = null;
      store.customerRole = null;
      store.permissions = [];

      if (!bearer) {
        if (requireAuth) {
          throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง - ไม่พบ token');
        }
        return {};
      }

      try {
        // Handle User Authentication
        if (type === 'user' || type === 'both') {
          const userPayload = await verifyUserToken(bearer);

          if (userPayload) {
            if (loadUserData) {
              const userData = await User.findById(userPayload.userId);
              store.user = userData;
            }
            else {
              store.user = userPayload;
            }

            // Check site access if required
            if (requireSiteAccess && siteId) {
              const accessResult = await AccessControlService.checkUserAccess(siteId, userPayload.userId);
              const validation = AccessControlService.validateAccess(accessResult, accessOptions);

              if (!validation.isValid) {
                throw new HttpError(403, validation.error || 'ไม่มีสิทธิ์เข้าถึงไซต์นี้');
              }

              store.hasAccess = true;
              store.userRole = accessResult.role;
              store.permissions = accessResult.permissions;

              if (loadSiteData) {
                store.site = await Site.findById(siteId);
              }
            }
          }
        }

        // Handle Customer Authentication
        if (type === 'customer' || type === 'both') {
          const customerPayload = await verifyCustomerToken(bearer);

          if (customerPayload) {
            if (loadUserData) {
              const customerData = await Customer.findById(customerPayload.customerId);
              store.customer = customerData;
            }
            else {
              store.customer = customerPayload;
            }

            // Check site access if required
            if (requireSiteAccess && siteId) {
              const accessResult = await AccessControlService.checkCustomerAccess(siteId, customerPayload.customerId);
              const validation = AccessControlService.validateAccess(accessResult, accessOptions);

              if (!validation.isValid) {
                throw new HttpError(403, validation.error || 'ไม่มีสิทธิ์เข้าถึงไซต์นี้');
              }

              store.hasAccess = true;
              store.customerRole = accessResult.role;
              store.permissions = accessResult.permissions;

              if (loadSiteData) {
                store.site = await Site.findById(siteId);
              }
            }
          }
        }

        // Check if authentication is required
        if (requireAuth && !store.user && !store.customer) {
          throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง - token ไม่ถูกต้อง');
        }

        return {};
      }
      catch (error) {
        if (error instanceof HttpError) {
          throw error;
        }

        logger.warn('Unified auth failed', {
          type,
          siteId,
          options,
          error: error instanceof Error ? error.message : String(error),
        });

        if (requireAuth) {
          throw new HttpError(401, 'การตรวจสอบสิทธิ์ล้มเหลว');
        }

        return {};
      }
    })
    .guard({ response: standardResponseSchema })
    .as('scoped');
};

/**
 * Pre-configured auth middlewares
 */

// User authentication (required)
export const requireUserAuth = createUnifiedAuth({
  type: 'user',
  requireAuth: true,
  loadUserData: true,
});

// User authentication (optional)
export const optionalUserAuth = createUnifiedAuth({
  type: 'user',
  requireAuth: false,
  loadUserData: true,
});

// Customer authentication (required)
export const requireCustomerAuth = createUnifiedAuth({
  type: 'customer',
  requireAuth: true,
  loadUserData: true,
});

// Customer authentication (optional)
export const optionalCustomerAuth = createUnifiedAuth({
  type: 'customer',
  requireAuth: false,
  loadUserData: true,
});

// Site owner authentication
export const requireSiteOwner = createUnifiedAuth({
  type: 'user',
  requireAuth: true,
  requireSiteAccess: true,
  requiredRole: 'owner',
  loadUserData: true,
  loadSiteData: true,
});

// Site admin authentication (owner or admin)
export const requireSiteAdmin = createUnifiedAuth({
  type: 'user',
  requireAuth: true,
  requireSiteAccess: true,
  allowRoles: ['owner', 'admin'],
  loadUserData: true,
  loadSiteData: true,
});

// Site member authentication (any role)
export const requireSiteMember = createUnifiedAuth({
  type: 'user',
  requireAuth: true,
  requireSiteAccess: true,
  loadUserData: true,
  loadSiteData: true,
});

// Customer site access
export const requireCustomerSiteAccess = createUnifiedAuth({
  type: 'customer',
  requireAuth: true,
  requireSiteAccess: true,
  loadUserData: true,
  loadSiteData: true,
});

/**
 * Helper functions for manual access checks
 */
export const checkUserAccess = AccessControlService.checkUserAccess;
export const checkCustomerAccess = AccessControlService.checkCustomerAccess;
export const checkPermission = AccessControlService.checkPermission;
export const validateAccess = AccessControlService.validateAccess;
export const isSiteOwner = AccessControlService.isSiteOwner;
export const isSiteAdmin = AccessControlService.isSiteAdmin;
export const isSiteCustomer = AccessControlService.isSiteCustomer;

/**
 * JWT helper functions - re-export from jwt.service
 */
export { extractBearerToken, generateToken, verifyCustomerToken, verifyUserToken } from '@/core/services/jwt.service';

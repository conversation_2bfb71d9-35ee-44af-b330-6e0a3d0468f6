import { customAlphabet } from 'nanoid';

export const generateFileName = (originalName: string, isGif: boolean): string => {
  const timestamp = Date.now();
  const baseName = originalName.split('.')[0];
  return `${timestamp}-${baseName}${isGif ? '.gif' : '.webp'}`;
};

export const paginate = (page: number, limit: number) => ({
  skip: (page - 1) * limit,
  limit,
});

export function generateFileId(length: number) {
  const timestamp = Date.now().toString(36);
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', length || 3)();
  return `${timestamp}${nanoid}`;
}

/**
 * ✅ Generate unique ID for database records
 */
export function generateId(): string {
  const timestamp = Date.now().toString(36);
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();
  return `${timestamp}${nanoid}`;
}

export const generateAffiliateCode = () => {
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', 8)();
  return nanoid;
};

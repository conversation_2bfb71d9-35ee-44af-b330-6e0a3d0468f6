# 📊 Activity Module - ศูนย์รวมประวัติและความเคลื่อนไหว

## 🎯 ภาพรวม

Activity Module เป็นศูนย์รวมสำหรับจัดการประวัติและความเคลื่อนไหวทั้งหมดในระบบ ครอบคลุม:

- ✅ **User Activities** - กิจกรรมผู้ใช้ (สมัครสมาชิก, เข้าสู่ระบบ, อัปเดตโปรไฟล์)
- ✅ **Site Activities** - กิจกรรมไซต์ (สร้าง, อัปเดต, ลบไซต์)
- ✅ **Product Activities** - กิจกรรมสินค้า (สร้าง, อัปเดต, ลบสินค้า)
- ✅ **Order Activities** - กิจกรรมออเดอร์ (สร้าง, อัปเดต, ยกเลิกออเดอร์)
- ✅ **Customer Activities** - กิจกรรมลูกค้า (สมัคร, อัปเดต, รีวิว)
- ✅ **Inventory Activities** - กิจกรรมคลังสินค้า (รับเข้า, จ่ายออก, ปรับปรุง)
- ✅ **Analytics Activities** - กิจกรรมวิเคราะห์ (ดูหน้า, ค้นหา, เปลี่ยน)
- ✅ **System Activities** - กิจกรรมระบบ (backup, update, maintenance)

## 🏗️ Architecture

```
Activity Module
├── activity.model.ts      # MongoDB Schema + Types
├── activity.service.ts     # Business Logic
├── activity.routes.ts      # API Endpoints
└── index.ts               # Exports
```

## 📋 Activity Types

### User Activities
- `user_signup` - สมัครสมาชิก
- `user_login` - เข้าสู่ระบบ
- `user_logout` - ออกจากระบบ
- `user_profile_update` - อัปเดตโปรไฟล์
- `user_password_change` - เปลี่ยนรหัสผ่าน
- `user_avatar_update` - อัปเดตรูปโปรไฟล์
- `user_email_verify` - ยืนยันอีเมล
- `user_email_resend` - ส่งอีเมลยืนยันใหม่
- `user_account_delete` - ลบบัญชี
- `user_status_change` - เปลี่ยนสถานะ
- `user_role_change` - เปลี่ยนบทบาท
- `user_points_earned` - ได้รับคะแนน
- `user_points_spent` - ใช้คะแนน
- `user_topup` - เติมเงิน
- `user_withdrawal` - ถอนเงิน

### Site Activities
- `site_create` - สร้างไซต์
- `site_update` - อัปเดตไซต์
- `site_delete` - ลบไซต์
- `site_status_change` - เปลี่ยนสถานะไซต์
- `site_settings_update` - อัปเดตการตั้งค่า
- `site_domain_change` - เปลี่ยนโดเมน
- `site_backup_create` - สร้าง backup
- `site_backup_restore` - restore backup

### Product Activities
- `product_create` - สร้างสินค้า
- `product_update` - อัปเดตสินค้า
- `product_delete` - ลบสินค้า
- `product_status_change` - เปลี่ยนสถานะสินค้า
- `product_category_change` - เปลี่ยนหมวดหมู่
- `product_price_change` - เปลี่ยนราคา
- `product_stock_update` - อัปเดตสต็อก
- `product_image_update` - อัปเดตรูปสินค้า
- `product_review_add` - เพิ่มรีวิว
- `product_review_update` - อัปเดตรีวิว
- `product_review_delete` - ลบรีวิว
- `product_variant_add` - เพิ่มตัวเลือก
- `product_variant_update` - อัปเดตตัวเลือก
- `product_variant_delete` - ลบตัวเลือก

### Order Activities
- `order_create` - สร้างออเดอร์
- `order_update` - อัปเดตออเดอร์
- `order_status_change` - เปลี่ยนสถานะออเดอร์
- `order_cancel` - ยกเลิกออเดอร์
- `order_refund` - คืนเงิน
- `order_payment_received` - ได้รับการชำระเงิน
- `order_payment_failed` - การชำระเงินล้มเหลว
- `order_shipping_update` - อัปเดตการจัดส่ง
- `order_tracking_update` - อัปเดตการติดตาม
- `order_delivered` - ส่งมอบแล้ว
- `order_return_request` - ขอคืนสินค้า
- `order_return_approved` - อนุมัติการคืน
- `order_return_rejected` - ปฏิเสธการคืน

### Customer Activities
- `customer_register` - สมัครสมาชิก
- `customer_login` - เข้าสู่ระบบ
- `customer_profile_update` - อัปเดตโปรไฟล์
- `customer_address_add` - เพิ่มที่อยู่
- `customer_address_update` - อัปเดตที่อยู่
- `customer_address_delete` - ลบที่อยู่
- `customer_wishlist_add` - เพิ่มรายการโปรด
- `customer_wishlist_remove` - ลบรายการโปรด
- `customer_review_add` - เพิ่มรีวิว
- `customer_review_update` - อัปเดตรีวิว
- `customer_review_delete` - ลบรีวิว
- `customer_subscription_start` - เริ่มการสมัครสมาชิก
- `customer_subscription_end` - สิ้นสุดการสมัครสมาชิก
- `customer_subscription_renew` - ต่ออายุการสมัครสมาชิก

### Inventory Activities
- `inventory_stock_in` - รับสินค้าเข้า
- `inventory_stock_out` - จ่ายสินค้าออก
- `inventory_adjustment` - ปรับปรุงสต็อก
- `inventory_transfer` - โอนย้ายสินค้า
- `inventory_count` - นับสต็อก
- `inventory_low_stock_alert` - แจ้งเตือนสต็อกต่ำ
- `inventory_out_of_stock_alert` - แจ้งเตือนสินค้าหมด
- `inventory_supplier_order` - สั่งซื้อจากซัพพลายเออร์
- `inventory_supplier_receive` - รับสินค้าจากซัพพลายเออร์

### Analytics Activities
- `analytics_page_view` - ดูหน้าเว็บ
- `analytics_product_view` - ดูสินค้า
- `analytics_search` - ค้นหา
- `analytics_cart_add` - เพิ่มตะกร้า
- `analytics_cart_remove` - ลบจากตะกร้า
- `analytics_checkout_start` - เริ่มชำระเงิน
- `analytics_checkout_complete` - ชำระเงินเสร็จ
- `analytics_conversion` - การแปลง
- `analytics_bounce` - การออกจากหน้า
- `analytics_session_start` - เริ่มเซสชัน
- `analytics_session_end` - สิ้นสุดเซสชัน

### System Activities
- `system_backup` - สร้าง backup
- `system_restore` - restore
- `system_update` - อัปเดตระบบ
- `system_maintenance` - บำรุงรักษา
- `system_error` - ข้อผิดพลาด
- `system_warning` - คำเตือน
- `system_info` - ข้อมูล

## 🔧 API Endpoints

### GET Endpoints
- `GET /activity` - ดึงกิจกรรมทั้งหมด (พร้อม filters)
- `GET /activity/:id` - ดึงกิจกรรมตาม ID
- `GET /activity/recent` - ดึงกิจกรรมล่าสุด
- `GET /activity/entity/:entityType/:entityId` - ดึงกิจกรรมตาม entity
- `GET /activity/user/:userId` - ดึงกิจกรรมผู้ใช้
- `GET /activity/site/:siteId` - ดึงกิจกรรมไซต์
- `GET /activity/customer/:customerId` - ดึงกิจกรรมลูกค้า
- `GET /activity/stats` - ดึงสถิติกิจกรรม

### POST Endpoints
- `POST /activity` - สร้างกิจกรรมใหม่
- `POST /activity/user` - สร้างกิจกรรมผู้ใช้
- `POST /activity/site` - สร้างกิจกรรมไซต์
- `POST /activity/product` - สร้างกิจกรรมสินค้า
- `POST /activity/order` - สร้างกิจกรรมออเดอร์
- `POST /activity/customer` - สร้างกิจกรรมลูกค้า
- `POST /activity/system` - สร้างกิจกรรมระบบ

### PUT Endpoints
- `PUT /activity/:id/status` - อัปเดตสถานะกิจกรรม

### DELETE Endpoints
- `DELETE /activity/:id` - ลบกิจกรรม
- `DELETE /activity/cleanup` - ลบกิจกรรมเก่า

## 📊 Activity Properties

```typescript
interface IActivity {
  _id: string;                    // Activity ID
  type: ActivityType;             // ประเภทกิจกรรม
  title: string;                  // หัวข้อ
  description: string;            // คำอธิบาย
  priority: ActivityPriority;     // ความสำคัญ (low/medium/high/critical)
  status: ActivityStatus;         // สถานะ (pending/processing/completed/failed/cancelled)
  
  // Entity Information
  entityType: string;             // ประเภท entity (user/site/product/order/etc.)
  entityId: string;               // ID ของ entity
  entityName?: string;            // ชื่อ entity
  
  // User Information
  userId?: string;                // ID ผู้ใช้
  userName?: string;              // ชื่อผู้ใช้
  userEmail?: string;             // อีเมลผู้ใช้
  userRole?: string;              // บทบาทผู้ใช้
  
  // Site Information
  siteId?: string;                // ID ไซต์
  siteName?: string;              // ชื่อไซต์
  
  // Customer Information
  customerId?: string;            // ID ลูกค้า
  customerName?: string;          // ชื่อลูกค้า
  customerEmail?: string;         // อีเมลลูกค้า
  
  // Additional Data
  metadata?: Record<string, any>; // ข้อมูลเพิ่มเติม
  oldValues?: Record<string, any>; // ค่าเดิม (สำหรับการเปลี่ยนแปลง)
  newValues?: Record<string, any>; // ค่าใหม่ (สำหรับการเปลี่ยนแปลง)
  
  // Timestamps
  createdAt: Date;                // สร้างเมื่อ
  updatedAt: Date;                // อัปเดตเมื่อ
  processedAt?: Date;             // ประมวลผลเมื่อ
}
```

## 🎨 Frontend Integration

### Activity Service
```typescript
import { activityService } from '$lib/services/activity';

// ดึงกิจกรรมผู้ใช้
const result = await activityService.getUserActivities(userId, 20, token);

// สร้างกิจกรรมใหม่
const result = await activityService.createUserActivity(
  'user_profile_update',
  'อัปเดตโปรไฟล์',
  'แก้ไขข้อมูลส่วนตัว',
  token
);
```

### ActivityList Component
```svelte
<script>
  import ActivityList from '$lib/components/activity/ActivityList.svelte';
</script>

<ActivityList 
  activities={activities}
  showPriority={true}
  showStatus={true}
  showEntity={true}
  maxItems={20}
  emptyMessage="ไม่มีข้อมูลกิจกรรม"
/>
```

## 🔍 Filters & Search

### Activity Filters
```typescript
interface ActivityFilters {
  type?: string;              // ประเภทกิจกรรม
  entityType?: string;        // ประเภท entity
  entityId?: string;          // ID entity
  userId?: string;            // ID ผู้ใช้
  siteId?: string;            // ID ไซต์
  customerId?: string;        // ID ลูกค้า
  priority?: string;          // ความสำคัญ
  status?: string;            // สถานะ
  startDate?: Date;           // วันที่เริ่มต้น
  endDate?: Date;             // วันที่สิ้นสุด
  limit?: number;             // จำนวนรายการ
  skip?: number;              // ข้ามรายการ
}
```

### Example Usage
```typescript
// ดึงกิจกรรมของผู้ใช้ในไซต์หนึ่ง
const filters = {
  userId: 'user123',
  siteId: 'site456',
  entityType: 'product',
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-12-31'),
  limit: 50
};

const activities = await activityService.getActivities(filters, token);
```

## 📈 Statistics

### Activity Stats
```typescript
interface ActivityStats {
  total: number;                           // จำนวนทั้งหมด
  byType: Record<ActivityType, number>;    // แยกตามประเภท
  byEntityType: Record<string, number>;    // แยกตาม entity type
  byPriority: Record<string, number>;      // แยกตามความสำคัญ
  byStatus: Record<string, number>;        // แยกตามสถานะ
  recent: Activity[];                      // กิจกรรมล่าสุด
}
```

## 🚀 Usage Examples

### 1. สร้างกิจกรรมเมื่อผู้ใช้สมัครสมาชิก
```typescript
await activityService.createUserActivity(
  'user_signup',
  'สมัครสมาชิก',
  'สร้างบัญชีใหม่',
  token,
  { 
    registrationMethod: 'email',
    source: 'website' 
  }
);
```

### 2. สร้างกิจกรรมเมื่อสร้างสินค้า
```typescript
await activityService.createProductActivity(
  'product_create',
  productId,
  productName,
  siteId,
  'สร้างสินค้าใหม่',
  `เพิ่มสินค้า "${productName}" เข้าระบบ`,
  token,
  {
    category: product.category,
    price: product.price,
    stock: product.stock
  }
);
```

### 3. สร้างกิจกรรมเมื่อมีการสั่งซื้อ
```typescript
await activityService.createOrderActivity(
  'order_create',
  orderId,
  orderNumber,
  siteId,
  customerId,
  customerName,
  'สร้างออเดอร์ใหม่',
  `ออเดอร์ ${orderNumber} จาก ${customerName}`,
  token,
  {
    totalAmount: order.totalAmount,
    items: order.items.length,
    paymentMethod: order.paymentMethod
  }
);
```

### 4. ดึงสถิติกิจกรรม
```typescript
const stats = await activityService.getActivityStats({
  siteId: 'site123',
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-12-31')
}, token);

console.log('Total activities:', stats.total);
console.log('By type:', stats.byType);
console.log('Recent activities:', stats.recent);
```

## 🔧 Configuration

### Database Indexes
```javascript
// Indexes for better performance
ActivitySchema.index({ type: 1, createdAt: -1 });
ActivitySchema.index({ entityType: 1, entityId: 1, createdAt: -1 });
ActivitySchema.index({ userId: 1, createdAt: -1 });
ActivitySchema.index({ siteId: 1, createdAt: -1 });
ActivitySchema.index({ customerId: 1, createdAt: -1 });
ActivitySchema.index({ priority: 1, status: 1 });
ActivitySchema.index({ createdAt: -1 });
ActivitySchema.index({ status: 1, processedAt: 1 });
```

### Cleanup Configuration
```typescript
// ลบกิจกรรมเก่า (90 วัน)
await activityService.cleanupOldActivities(90);
```

## 🎯 Benefits

1. **📊 Centralized Tracking** - ติดตามกิจกรรมทั้งหมดในที่เดียว
2. **🔍 Advanced Filtering** - กรองและค้นหาขั้นสูง
3. **📈 Rich Analytics** - สถิติและวิเคราะห์ข้อมูล
4. **🎨 Flexible UI** - UI ที่ยืดหยุ่นและปรับแต่งได้
5. **⚡ High Performance** - ประสิทธิภาพสูงด้วย indexing
6. **🔒 Type Safe** - TypeScript support เต็มรูปแบบ
7. **📱 Responsive** - รองรับทุกอุปกรณ์
8. **🌐 Multi-language** - รองรับหลายภาษา

## 🚀 Next Steps

1. **📊 Dashboard Integration** - เชื่อมต่อกับ dashboard
2. **🔔 Real-time Updates** - อัปเดตแบบ real-time
3. **📧 Email Notifications** - แจ้งเตือนทางอีเมล
4. **📱 Push Notifications** - แจ้งเตือนแบบ push
5. **📈 Advanced Analytics** - วิเคราะห์ขั้นสูง
6. **🤖 AI Integration** - เชื่อมต่อ AI
7. **📊 Export Features** - ส่งออกข้อมูล
8. **🔍 Advanced Search** - ค้นหาขั้นสูง

---

**สรุป**: Activity Module เป็นศูนย์รวมที่ครอบคลุมและยืดหยุ่นสำหรับจัดการประวัติและความเคลื่อนไหวทั้งหมดในระบบ ทำให้สามารถติดตาม วิเคราะห์ และจัดการกิจกรรมต่างๆ ได้อย่างมีประสิทธิภาพ 
import type { ActivityType } from './activity.model';
import { activityService } from './activity.service';

/**
 * ✅ Activity Helper Functions - ฟังก์ชันช่วยเหลือสำหรับสร้างกิจกรรม
 * - ใช้ใน modules อื่นๆ เพื่อสร้างกิจกรรมอัตโนมัติ
 * - ลดความซ้ำซ้อนของโค้ด
 * - มาตรฐานการสร้างกิจกรรม
 */

/**
 * ✅ สร้างกิจกรรม User อัตโนมัติ
 */
export async function logUserActivity(
  type: ActivityType,
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  title: string,
  description: string,
  token: string,
  options?: {
    metadata?: Record<string, any>;
  },
) {
  try {
    await activityService.createUserActivity(
      type,
      userId,
      userName,
      userEmail,
      userRole,
      title,
      description,
      token,
      options?.metadata,
    );
  }
  catch (error) {
    console.error('Error logging user activity:', error);
    // Don't throw error to avoid breaking main functionality
  }
}

/**
 * ✅ สร้างกิจกรรม Site อัตโนมัติ
 */
export async function logSiteActivity(
  type: ActivityType,
  siteId: string,
  siteName: string,
  userId: string,
  userName: string,
  title: string,
  description: string,
  token: string,
  metadata?: Record<string, any>,
) {
  try {
    await activityService.createSiteActivity(
      type,
      siteId,
      siteName,
      userId,
      userName,
      title,
      description,
      token,
      metadata,
    );
  }
  catch (error) {
    console.error('Error logging site activity:', error);
  }
}

/**
 * ✅ สร้างกิจกรรม Product อัตโนมัติ
 */
export async function logProductActivity(
  type: ActivityType,
  productId: string,
  productName: string,
  siteId: string,
  userId: string,
  userName: string,
  title: string,
  description: string,
  token: string,
  metadata?: Record<string, any>,
) {
  try {
    await activityService.createProductActivity(
      type,
      productId,
      productName,
      siteId,
      userId,
      userName,
      title,
      description,
      token,
      metadata,
    );
  }
  catch (error) {
    console.error('Error logging product activity:', error);
  }
}

/**
 * ✅ สร้างกิจกรรม Order อัตโนมัติ
 */
export async function logOrderActivity(
  type: ActivityType,
  orderId: string,
  orderNumber: string,
  siteId: string,
  customerId: string,
  customerName: string,
  userId: string,
  userName: string,
  title: string,
  description: string,
  token: string,
  metadata?: Record<string, any>,
) {
  try {
    await activityService.createOrderActivity(
      type,
      orderId,
      orderNumber,
      siteId,
      customerId,
      customerName,
      userId,
      userName,
      title,
      description,
      token,
      metadata,
    );
  }
  catch (error) {
    console.error('Error logging order activity:', error);
  }
}

/**
 * ✅ สร้างกิจกรรม Customer อัตโนมัติ
 */
export async function logCustomerActivity(
  type: ActivityType,
  customerId: string,
  customerName: string,
  customerEmail: string,
  siteId: string,
  title: string,
  description: string,
  token: string,
  metadata?: Record<string, any>,
) {
  try {
    await activityService.createCustomerActivity(
      type,
      customerId,
      customerName,
      customerEmail,
      siteId,
      title,
      description,
      token,
      metadata,
    );
  }
  catch (error) {
    console.error('Error logging customer activity:', error);
  }
}

/**
 * ✅ สร้างกิจกรรม System อัตโนมัติ
 */
export async function logSystemActivity(
  type: ActivityType,
  title: string,
  description: string,
  token: string,
  options?: {
    priority?: 'low' | 'medium' | 'high' | 'critical';
    metadata?: Record<string, any>;
  },
) {
  try {
    await activityService.createSystemActivity(
      type,
      title,
      description,
      token,
      options?.priority || 'medium',
      options?.metadata,
    );
  }
  catch (error) {
    console.error('Error logging system activity:', error);
  }
}

/**
 * ✅ สร้างกิจกรรมการเปลี่ยนแปลงข้อมูล
 */
export async function logDataChangeActivity(
  entityType: 'user' | 'site' | 'product' | 'order' | 'customer',
  entityId: string,
  entityName: string,
  changeType: 'create' | 'update' | 'delete',
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  token: string,
  options?: {
    siteId?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
  },
) {
  try {
    const activityType = `${entityType}_${changeType}` as ActivityType;
    const title = getChangeTitle(entityType, changeType, entityName);
    const description = getChangeDescription(entityType, changeType, entityName);

    const metadata: Record<string, any> = {};
    if (options?.oldValues) metadata.oldValues = options.oldValues;
    if (options?.newValues) metadata.newValues = options.newValues;

    await activityService.createActivity({
      type: activityType,
      title,
      description,
      entityType,
      entityId,
      entityName,
      userId,
      userName,
      userEmail,
      userRole,
      siteId: options?.siteId,
      metadata,
      oldValues: options?.oldValues,
      newValues: options?.newValues,
    }, token);
  }
  catch (error) {
    console.error('Error logging data change activity:', error);
  }
}

/**
 * ✅ สร้างกิจกรรมการเข้าสู่ระบบ
 */
export async function logLoginActivity(
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  token: string,
  metadata?: Record<string, any>,
) {
  await logUserActivity(
    'user_login',
    userId,
    userName,
    userEmail,
    userRole,
    'เข้าสู่ระบบ',
    'เข้าสู่ระบบสำเร็จ',
    token,
    { metadata },
  );
}

/**
 * ✅ สร้างกิจกรรมการสมัครสมาชิก
 */
export async function logSignupActivity(
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  token: string,
  metadata?: Record<string, any>,
) {
  await logUserActivity(
    'user_signup',
    userId,
    userName,
    userEmail,
    userRole,
    'สมัครสมาชิก',
    'สร้างบัญชีใหม่สำเร็จ',
    token,
    { metadata },
  );
}

/**
 * ✅ สร้างกิจกรรมการอัปเดตโปรไฟล์
 */
export async function logProfileUpdateActivity(
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  token: string,
  oldValues?: Record<string, any>,
  newValues?: Record<string, any>,
) {
  await logUserActivity(
    'user_profile_update',
    userId,
    userName,
    userEmail,
    userRole,
    'อัปเดตโปรไฟล์',
    'แก้ไขข้อมูลส่วนตัว',
    token,
    {
      metadata: {
        oldValues,
        newValues,
      },
    },
  );
}

/**
 * ✅ สร้างกิจกรรมการเปลี่ยนรหัสผ่าน
 */
export async function logPasswordChangeActivity(
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  token: string,
) {
  await logUserActivity(
    'user_password_change',
    userId,
    userName,
    userEmail,
    userRole,
    'เปลี่ยนรหัสผ่าน',
    'เปลี่ยนรหัสผ่านสำเร็จ',
    token,
    {},
  );
}

/**
 * ✅ สร้างกิจกรรมการยืนยันอีเมล
 */
export async function logEmailVerificationActivity(
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  token: string,
) {
  await logUserActivity(
    'user_email_verify',
    userId,
    userName,
    userEmail,
    userRole,
    'ยืนยันอีเมล',
    'ยืนยันอีเมลสำเร็จ',
    token,
    {},
  );
}

/**
 * ✅ สร้างกิจกรรมการสร้างสินค้า
 */
export async function logProductCreateActivity(
  productId: string,
  productName: string,
  siteId: string,
  userId: string,
  userName: string,
  token: string,
  metadata?: Record<string, any>,
) {
  await logProductActivity(
    'product_create',
    productId,
    productName,
    siteId,
    userId,
    userName,
    'สร้างสินค้าใหม่',
    `เพิ่มสินค้า "${productName}" เข้าระบบ`,
    token,
    { metadata },
  );
}

/**
 * ✅ สร้างกิจกรรมการอัปเดตสินค้า
 */
export async function logProductUpdateActivity(
  productId: string,
  productName: string,
  siteId: string,
  userId: string,
  userName: string,
  token: string,
  oldValues?: Record<string, any>,
  newValues?: Record<string, any>,
) {
  await logProductActivity(
    'product_update',
    productId,
    productName,
    siteId,
    userId,
    userName,
    'อัปเดตสินค้า',
    `แก้ไขข้อมูลสินค้า "${productName}"`,
    token,
    {
      metadata: {
        oldValues,
        newValues,
      },
    },
  );
}

/**
 * ✅ สร้างกิจกรรมการสร้างออเดอร์
 */
export async function logOrderCreateActivity(
  orderId: string,
  orderNumber: string,
  siteId: string,
  customerId: string,
  customerName: string,
  userId: string,
  userName: string,
  token: string,
  metadata?: Record<string, any>,
) {
  await logOrderActivity(
    'order_create',
    orderId,
    orderNumber,
    siteId,
    customerId,
    customerName,
    userId,
    userName,
    'สร้างออเดอร์ใหม่',
    `ออเดอร์ ${orderNumber} จาก ${customerName}`,
    token,
    { metadata },
  );
}

/**
 * ✅ สร้างกิจกรรมการเปลี่ยนสถานะออเดอร์
 */
export async function logOrderStatusChangeActivity(
  orderId: string,
  orderNumber: string,
  siteId: string,
  customerId: string,
  customerName: string,
  userId: string,
  userName: string,
  oldStatus: string,
  newStatus: string,
  token: string,
) {
  await logOrderActivity(
    'order_status_change',
    orderId,
    orderNumber,
    siteId,
    customerId,
    customerName,
    userId,
    userName,
    'เปลี่ยนสถานะออเดอร์',
    `เปลี่ยนสถานะออเดอร์ ${orderNumber} จาก ${oldStatus} เป็น ${newStatus}`,
    token,
    {
      metadata: {
        oldStatus,
        newStatus,
      },
    },
  );
}

/**
 * ✅ สร้างกิจกรรมการสร้างไซต์
 */
export async function logSiteCreateActivity(
  siteId: string,
  siteName: string,
  userId: string,
  userName: string,
  token: string,
  metadata?: Record<string, any>,
) {
  await logSiteActivity(
    'site_create',
    siteId,
    siteName,
    userId,
    userName,
    'สร้างไซต์ใหม่',
    `สร้างไซต์ "${siteName}"`,
    token,
    { metadata },
  );
}

/**
 * ✅ สร้างกิจกรรมการอัปเดตไซต์
 */
export async function logSiteUpdateActivity(
  siteId: string,
  siteName: string,
  userId: string,
  userName: string,
  token: string,
  oldValues?: Record<string, any>,
  newValues?: Record<string, any>,
) {
  await logSiteActivity(
    'site_update',
    siteId,
    siteName,
    userId,
    userName,
    'อัปเดตไซต์',
    `แก้ไขข้อมูลไซต์ "${siteName}"`,
    token,
    {
      metadata: {
        oldValues,
        newValues,
      },
    },
  );
}

/**
 * ✅ สร้างกิจกรรมการสมัครสมาชิกลูกค้า
 */
export async function logCustomerRegisterActivity(
  customerId: string,
  customerName: string,
  customerEmail: string,
  siteId: string,
  token: string,
  metadata?: Record<string, any>,
) {
  await logCustomerActivity(
    'customer_register',
    customerId,
    customerName,
    customerEmail,
    siteId,
    'สมัครสมาชิกลูกค้า',
    `ลูกค้า ${customerName} สมัครสมาชิกใหม่`,
    token,
    { metadata },
  );
}

/**
 * ✅ สร้างกิจกรรมการเพิ่มรีวิว
 */
export async function logReviewAddActivity(
  productId: string,
  productName: string,
  customerId: string,
  customerName: string,
  siteId: string,
  token: string,
  rating: number,
  metadata?: Record<string, any>,
) {
  await logCustomerActivity(
    'customer_review_add',
    customerId,
    customerName,
    '',
    siteId,
    'เพิ่มรีวิวสินค้า',
    `${customerName} ให้คะแนนสินค้า "${productName}" ${rating}/5 ดาว`,
    token,
    {
      metadata: {
        productId,
        productName,
        rating,
        ...metadata,
      },
    },
  );
}

/**
 * ✅ สร้างกิจกรรมการเข้าชมหน้าเว็บ
 */
export async function logPageViewActivity(
  pageUrl: string,
  pageTitle: string,
  siteId: string,
  token: string,
  options?: {
    userId?: string;
    customerId?: string;
    metadata?: Record<string, any>;
  },
) {
  try {
    await activityService.createActivity({
      type: 'analytics_page_view',
      title: 'เข้าชมหน้าเว็บ',
      description: `เข้าชมหน้า "${pageTitle}"`,
      entityType: 'analytics',
      entityId: pageUrl,
      entityName: pageTitle,
      userId: options?.userId,
      customerId: options?.customerId,
      siteId,
      metadata: {
        pageUrl,
        pageTitle,
        ...options?.metadata,
      },
    }, token);
  }
  catch (error) {
    console.error('Error logging page view activity:', error);
  }
}

/**
 * ✅ สร้างกิจกรรมการค้นหา
 */
export async function logSearchActivity(
  searchQuery: string,
  resultsCount: number,
  siteId: string,
  token: string,
  options?: {
    userId?: string;
    customerId?: string;
    metadata?: Record<string, any>;
  },
) {
  try {
    await activityService.createActivity({
      type: 'analytics_search',
      title: 'ค้นหาสินค้า',
      description: `ค้นหา "${searchQuery}" พบ ${resultsCount} รายการ`,
      entityType: 'analytics',
      entityId: searchQuery,
      entityName: searchQuery,
      userId: options?.userId,
      customerId: options?.customerId,
      siteId,
      metadata: {
        searchQuery,
        resultsCount,
        ...options?.metadata,
      },
    }, token);
  }
  catch (error) {
    console.error('Error logging search activity:', error);
  }
}

// Helper functions for generating titles and descriptions
function getChangeTitle(entityType: string, changeType: string, entityName: string): string {
  const changeText = {
    create: 'สร้างใหม่',
    update: 'อัปเดต',
    delete: 'ลบ',
  }[changeType];

  const entityText = {
    user: 'ผู้ใช้',
    site: 'ไซต์',
    product: 'สินค้า',
    order: 'ออเดอร์',
    customer: 'ลูกค้า',
  }[entityType];

  return `${changeText}${entityText}`;
}

function getChangeDescription(entityType: string, changeType: string, entityName: string): string {
  const changeText = {
    create: 'สร้าง',
    update: 'แก้ไข',
    delete: 'ลบ',
  }[changeType];

  const entityText = {
    user: 'ผู้ใช้',
    site: 'ไซต์',
    product: 'สินค้า',
    order: 'ออเดอร์',
    customer: 'ลูกค้า',
  }[entityType];

  return `${changeText}${entityText} "${entityName}"`;
}

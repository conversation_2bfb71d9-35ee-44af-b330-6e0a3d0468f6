import { generateId } from '@/core/utils';
import { Activity, type ActivityType, type IActivity } from './activity.model';

export interface CreateActivityData {
  type: ActivityType;
  title: string;
  description: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  entityType:
    | 'user'
    | 'site'
    | 'product'
    | 'order'
    | 'customer'
    | 'inventory'
    | 'discount'
    | 'shipping'
    | 'analytics'
    | 'notification'
    | 'chat'
    | 'media'
    | 'content'
    | 'menu'
    | 'brand'
    | 'category'
    | 'affiliate'
    | 'social'
    | 'ads'
    | 'loyalty'
    | 'search'
    | 'system';
  entityId: string;
  entityName?: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  userRole?: string;
  siteId?: string;
  siteName?: string;
  customerId?: string;
  customerName?: string;
  customerEmail?: string;
  metadata?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
}

export interface ActivityFilters {
  type?: ActivityType;
  entityType?: string;
  entityId?: string;
  userId?: string;
  siteId?: string;
  customerId?: string;
  priority?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  skip?: number;
}

export interface ActivityStats {
  total: number;
  byType: Record<ActivityType, number>;
  byEntityType: Record<string, number>;
  byPriority: Record<string, number>;
  byStatus: Record<string, number>;
  recent: IActivity[];
}

/**
 * ✅ Activity Service - จัดการประวัติและความเคลื่อนไหวทั้งหมดในระบบ
 * - สร้างกิจกรรมใหม่
 * - ดึงข้อมูลกิจกรรม
 * - สถิติกิจกรรม
 * - การกรองและค้นหา
 */
export class ActivityService {
  /**
   * ✅ สร้างกิจกรรมใหม่
   */
  async createActivity(data: CreateActivityData): Promise<IActivity> {
    const activity = new Activity({
      _id: generateId(),
      ...data,
      priority: data.priority || 'medium',
      status: data.status || 'completed',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return await activity.save();
  }

  /**
   * ✅ สร้างกิจกรรมแบบ batch (หลายกิจกรรมพร้อมกัน)
   */
  async createActivities(activities: CreateActivityData[]): Promise<IActivity[]> {
    const activityDocs = activities.map(data =>
      new Activity({
        _id: generateId(),
        ...data,
        priority: data.priority || 'medium',
        status: data.status || 'completed',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    );

    return await Activity.insertMany(activityDocs);
  }

  /**
   * ✅ ดึงกิจกรรมตาม ID
   */
  async getActivityById(id: string): Promise<IActivity | null> {
    return await Activity.findById(id);
  }

  /**
   * ✅ ดึงกิจกรรมตาม filters
   */
  async getActivities(filters: ActivityFilters = {}): Promise<IActivity[]> {
    const query: any = {};

    // Apply filters
    if (filters.type) query.type = filters.type;
    if (filters.entityType) query.entityType = filters.entityType;
    if (filters.entityId) query.entityId = filters.entityId;
    if (filters.userId) query.userId = filters.userId;
    if (filters.siteId) query.siteId = filters.siteId;
    if (filters.customerId) query.customerId = filters.customerId;
    if (filters.priority) query.priority = filters.priority;
    if (filters.status) query.status = filters.status;

    // Date range filter
    if (filters.startDate || filters.endDate) {
      query.createdAt = {};
      if (filters.startDate) query.createdAt.$gte = filters.startDate;
      if (filters.endDate) query.createdAt.$lte = filters.endDate;
    }

    const limit = filters.limit || 50;
    const skip = filters.skip || 0;

    return await Activity.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);
  }

  /**
   * ✅ ดึงกิจกรรมล่าสุด
   */
  async getRecentActivities(limit: number = 10): Promise<IActivity[]> {
    return await Activity.find()
      .sort({ createdAt: -1 })
      .limit(limit);
  }

  /**
   * ✅ ดึงกิจกรรมตาม entity
   */
  async getActivitiesByEntity(entityType: string, entityId: string, limit: number = 20): Promise<IActivity[]> {
    return await Activity.find({ entityType, entityId })
      .sort({ createdAt: -1 })
      .limit(limit);
  }

  /**
   * ✅ ดึงกิจกรรมตาม user
   */
  async getUserActivities(userId: string, limit: number = 20): Promise<IActivity[]> {
    return await Activity.find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit);
  }

  /**
   * ✅ ดึงกิจกรรมตาม site
   */
  async getSiteActivities(siteId: string, limit: number = 20): Promise<IActivity[]> {
    return await Activity.find({ siteId })
      .sort({ createdAt: -1 })
      .limit(limit);
  }

  /**
   * ✅ ดึงกิจกรรมตาม customer
   */
  async getCustomerActivities(customerId: string, limit: number = 20): Promise<IActivity[]> {
    return await Activity.find({ customerId })
      .sort({ createdAt: -1 })
      .limit(limit);
  }

  /**
   * ✅ ดึงสถิติกิจกรรม
   */
  async getActivityStats(filters: ActivityFilters = {}): Promise<ActivityStats> {
    const query: any = {};

    // Apply filters
    if (filters.type) query.type = filters.type;
    if (filters.entityType) query.entityType = filters.entityType;
    if (filters.entityId) query.entityId = filters.entityId;
    if (filters.userId) query.userId = filters.userId;
    if (filters.siteId) query.siteId = filters.siteId;
    if (filters.customerId) query.customerId = filters.customerId;
    if (filters.priority) query.priority = filters.priority;
    if (filters.status) query.status = filters.status;

    // Date range filter
    if (filters.startDate || filters.endDate) {
      query.createdAt = {};
      if (filters.startDate) query.createdAt.$gte = filters.startDate;
      if (filters.endDate) query.createdAt.$lte = filters.endDate;
    }

    const [total, byType, byEntityType, byPriority, byStatus, recent] = await Promise.all([
      Activity.countDocuments(query),
      Activity.aggregate([
        { $match: query },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
      Activity.aggregate([
        { $match: query },
        { $group: { _id: '$entityType', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
      Activity.aggregate([
        { $match: query },
        { $group: { _id: '$priority', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
      Activity.aggregate([
        { $match: query },
        { $group: { _id: '$status', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
      Activity.find(query).sort({ createdAt: -1 }).limit(10),
    ]);

    // Convert aggregation results to objects
    const byTypeObj: Record<ActivityType, number> = {} as any;
    const byEntityTypeObj: Record<string, number> = {};
    const byPriorityObj: Record<string, number> = {};
    const byStatusObj: Record<string, number> = {};

    byType.forEach(item => byTypeObj[item._id] = item.count);
    byEntityType.forEach(item => byEntityTypeObj[item._id] = item.count);
    byPriority.forEach(item => byPriorityObj[item._id] = item.count);
    byStatus.forEach(item => byStatusObj[item._id] = item.count);

    return {
      total,
      byType: byTypeObj,
      byEntityType: byEntityTypeObj,
      byPriority: byPriorityObj,
      byStatus: byStatusObj,
      recent,
    };
  }

  /**
   * ✅ อัปเดตสถานะกิจกรรม
   */
  async updateActivityStatus(
    id: string,
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled',
  ): Promise<IActivity | null> {
    return await Activity.findByIdAndUpdate(
      id,
      {
        status,
        processedAt: status === 'completed' ? new Date() : undefined,
        updatedAt: new Date(),
      },
      { new: true },
    );
  }

  /**
   * ✅ ลบกิจกรรม
   */
  async deleteActivity(id: string): Promise<boolean> {
    const result = await Activity.findByIdAndDelete(id);
    return !!result;
  }

  /**
   * ✅ ลบกิจกรรมเก่า (cleanup)
   */
  async cleanupOldActivities(daysOld: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const result = await Activity.deleteMany({
      createdAt: { $lt: cutoffDate },
      priority: { $ne: 'critical' }, // ไม่ลบ critical activities
    });

    return result.deletedCount || 0;
  }

  /**
   * ✅ สร้างกิจกรรม User
   */
  async createUserActivity(
    type: ActivityType,
    userId: string,
    userName: string,
    userEmail: string,
    userRole: string,
    title: string,
    description: string,
    metadata?: Record<string, any>,
  ): Promise<IActivity> {
    return await this.createActivity({
      type,
      title,
      description,
      entityType: 'user',
      entityId: userId,
      entityName: userName,
      userId,
      userName,
      userEmail,
      userRole,
      metadata,
    });
  }

  /**
   * ✅ สร้างกิจกรรม Site
   */
  async createSiteActivity(
    type: ActivityType,
    siteId: string,
    siteName: string,
    userId: string,
    userName: string,
    title: string,
    description: string,
    metadata?: Record<string, any>,
  ): Promise<IActivity> {
    return await this.createActivity({
      type,
      title,
      description,
      entityType: 'site',
      entityId: siteId,
      entityName: siteName,
      userId,
      userName,
      siteId,
      siteName,
      metadata,
    });
  }

  /**
   * ✅ สร้างกิจกรรม Product
   */
  async createProductActivity(
    type: ActivityType,
    productId: string,
    productName: string,
    siteId: string,
    userId: string,
    userName: string,
    title: string,
    description: string,
    metadata?: Record<string, any>,
  ): Promise<IActivity> {
    return await this.createActivity({
      type,
      title,
      description,
      entityType: 'product',
      entityId: productId,
      entityName: productName,
      userId,
      userName,
      siteId,
      metadata,
    });
  }

  /**
   * ✅ สร้างกิจกรรม Order
   */
  async createOrderActivity(
    type: ActivityType,
    orderId: string,
    orderNumber: string,
    siteId: string,
    customerId: string,
    customerName: string,
    userId: string,
    userName: string,
    title: string,
    description: string,
    metadata?: Record<string, any>,
  ): Promise<IActivity> {
    return await this.createActivity({
      type,
      title,
      description,
      entityType: 'order',
      entityId: orderId,
      entityName: orderNumber,
      userId,
      userName,
      siteId,
      customerId,
      customerName,
      metadata,
    });
  }

  /**
   * ✅ สร้างกิจกรรม Customer
   */
  async createCustomerActivity(
    type: ActivityType,
    customerId: string,
    customerName: string,
    customerEmail: string,
    siteId: string,
    title: string,
    description: string,
    metadata?: Record<string, any>,
  ): Promise<IActivity> {
    return await this.createActivity({
      type,
      title,
      description,
      entityType: 'customer',
      entityId: customerId,
      entityName: customerName,
      customerId,
      customerName,
      customerEmail,
      siteId,
      metadata,
    });
  }

  /**
   * ✅ สร้างกิจกรรม System
   */
  async createSystemActivity(
    type: ActivityType,
    title: string,
    description: string,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    metadata?: Record<string, any>,
  ): Promise<IActivity> {
    return await this.createActivity({
      type,
      title,
      description,
      priority,
      entityType: 'system',
      entityId: 'system',
      entityName: 'System',
      metadata,
    });
  }
}

export const activityService = new ActivityService();

import { validateSiteAccess } from '../site/site.service';
import { AddonLog, AddonRental, AVAILABLE_ADDONS } from './addon.model';

export class AddonService {
  constructor() {
    // No dependencies needed
  }

  // ดึงรายการ addons ของ site
  async getSiteAddons(siteId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ดึงข้อมูลการเช่า addons
      const rentals = await AddonRental.find({
        siteId,
        status: { $ne: 'cancelled' },
      }).sort({ createdAt: -1 });

      // รวมข้อมูล addons ที่มีให้เลือกกับข้อมูลการเช่า
      const addonsWithRentals = AVAILABLE_ADDONS.map(addon => {
        const rental = rentals.find(r => r.addonId === addon.id);
        return {
          ...addon,
          isRented: !!rental,
          isActive: rental?.isActive || false,
          rentedAt: rental?.createdAt || null,
          expiresAt: rental?.expiresAt || null,
          status: rental?.status || 'not_rented',
        };
      });

      return {
        success: true,
        data: addonsWithRentals,
      };
    }
    catch (error) {
      console.error('Error getting site addons:', error);
      throw new Error('ไม่สามารถดึงข้อมูล addons ได้');
    }
  }

  // เช่า addon
  async rentAddon(siteId: string, addonId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่า addon มีอยู่จริง
      const addon = AVAILABLE_ADDONS.find(a => a.id === addonId);
      if (!addon) {
        throw new Error('ไม่พบ addon ที่ระบุ');
      }

      // ตรวจสอบว่าเช่าแล้วหรือยัง
      const existingRental = await AddonRental.findOne({
        siteId,
        addonId,
        status: { $ne: 'cancelled' },
      });

      if (existingRental) {
        throw new Error('คุณได้เช่า addon นี้แล้ว');
      }

      // สร้างการเช่าใหม่
      const rental = new AddonRental({
        siteId,
        addonId,
        addonName: addon.name,
        price: addon.price,
        status: 'active',
        isActive: false, // เช่าแล้วแต่ยังไม่เปิดใช้งาน
        rentedBy: userId,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 วัน
      });

      await rental.save();

      // บันทึก log
      const log = new AddonLog({
        siteId,
        addonId,
        action: 'rent',
        userId,
        details: { price: addon.price },
      });
      await log.save();

      return {
        success: true,
        message: 'เช่า addon เรียบร้อยแล้ว',
        data: rental,
      };
    }
    catch (error) {
      console.error('Error renting addon:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถเช่า addon ได้');
    }
  }

  // เปิดใช้งาน addon
  async activateAddon(siteId: string, addonId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าได้เช่าแล้วหรือไม่
      const rental = await AddonRental.findOne({
        siteId,
        addonId,
        status: 'active',
      });

      if (!rental) {
        throw new Error('คุณยังไม่ได้เช่า addon นี้');
      }

      if (rental.isActive) {
        throw new Error('addon นี้เปิดใช้งานอยู่แล้ว');
      }

      // เปิดใช้งาน addon
      rental.isActive = true;
      rental.activatedAt = new Date();
      await rental.save();

      // บันทึก log
      const log = new AddonLog({
        siteId,
        addonId,
        action: 'activate',
        userId,
      });
      await log.save();

      return {
        success: true,
        message: 'เปิดใช้งาน addon เรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error activating addon:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถเปิดใช้งาน addon ได้');
    }
  }

  // ปิดใช้งาน addon
  async deactivateAddon(siteId: string, addonId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าได้เช่าและเปิดใช้งานแล้วหรือไม่
      const rental = await AddonRental.findOne({
        siteId,
        addonId,
        status: 'active',
        isActive: true,
      });

      if (!rental) {
        throw new Error('ไม่พบ addon ที่เปิดใช้งานอยู่');
      }

      // ปิดใช้งาน addon
      rental.isActive = false;
      rental.deactivatedAt = new Date();
      await rental.save();

      // บันทึก log
      const log = new AddonLog({
        siteId,
        addonId,
        action: 'deactivate',
        userId,
      });
      await log.save();

      return {
        success: true,
        message: 'ปิดใช้งาน addon เรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error deactivating addon:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถปิดใช้งาน addon ได้');
    }
  }

  // ยกเลิกการเช่า addon
  async cancelAddonRental(siteId: string, addonId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าได้เช่าแล้วหรือไม่
      const rental = await AddonRental.findOne({
        siteId,
        addonId,
        status: 'active',
      });

      if (!rental) {
        throw new Error('ไม่พบการเช่า addon นี้');
      }

      // ยกเลิกการเช่า
      rental.status = 'cancelled';
      rental.isActive = false;
      rental.cancelledAt = new Date();
      await rental.save();

      // บันทึก log
      const log = new AddonLog({
        siteId,
        addonId,
        action: 'cancel',
        userId,
      });
      await log.save();

      return {
        success: true,
        message: 'ยกเลิกการเช่า addon เรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error cancelling addon rental:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถยกเลิกการเช่า addon ได้');
    }
  }

  // ดึงข้อมูลการใช้งาน addon
  async getAddonUsage(siteId: string, addonId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ดึงข้อมูลการเช่า
      const rental = await AddonRental.findOne({
        siteId,
        addonId,
        status: 'active',
      });

      if (!rental) {
        throw new Error('ไม่พบการเช่า addon นี้');
      }

      // ดึงสถิติการใช้งาน (ขึ้นอยู่กับประเภท addon)
      let usageStats = {};

      // สำหรับตอนนี้ใช้ mock data เนื่องจากยังไม่มี content models
      switch (addonId) {
        case 'news':
          usageStats = {
            totalNews: 0,
            publishedNews: 0,
            categories: 0,
          };
          break;

        case 'blog':
          usageStats = {
            totalBlogs: 0,
            publishedBlogs: 0,
            totalViews: 0,
          };
          break;

        case 'novel':
          usageStats = {
            totalNovels: 0,
            totalChapters: 0,
            totalReaders: 0,
          };
          break;
      }

      return {
        success: true,
        data: {
          rental,
          usage: usageStats,
        },
      };
    }
    catch (error) {
      console.error('Error getting addon usage:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถดึงข้อมูลการใช้งาน addon ได้');
    }
  }

  // ตรวจสอบว่า addon เปิดใช้งานหรือไม่
  async isAddonActive(siteId: string, addonId: string): Promise<boolean> {
    try {
      const rental = await AddonRental.findOne({
        siteId,
        addonId,
        status: 'active',
        isActive: true,
      });

      return !!rental;
    }
    catch (error) {
      console.error('Error checking addon status:', error);
      return false;
    }
  }
}

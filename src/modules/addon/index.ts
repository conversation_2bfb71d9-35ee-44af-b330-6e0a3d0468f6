import { Elysia, t } from 'elysia';
import { requireSiteMember, requireUserAuth } from '../../core/middleware/unified-auth';
import { AddonService } from './addon.service';

export const addonModule = new Elysia({ prefix: '/addons' })
  .use(requireUserAuth)
  .use(requireSiteMember)
  .derive(({ store }) => ({
    user: store.user,
    userId: store.user?.id || store.user?.userId,
  }))
  .decorate('addonService', new AddonService())
  // ดึงรายการ addons ของ site
  .get('/sites/:siteId', async ({ params, addonService, userId }) => {
    return await addonService.getSiteAddons(params.siteId, userId);
  }, {
    params: t.Object({
      siteId: t.String(),
    }),
  })
  // เช่า addon
  .post('/sites/:siteId/:addonId/rent', async ({ params, addonService, userId }) => {
    return await addonService.rentAddon(params.siteId, params.addonId, userId);
  }, {
    params: t.Object({
      siteId: t.String(),
      addonId: t.String(),
    }),
  })
  // เปิดใช้งาน addon
  .post('/sites/:siteId/:addonId/activate', async ({ params, addonService, userId }) => {
    return await addonService.activateAddon(params.siteId, params.addonId, userId);
  }, {
    params: t.Object({
      siteId: t.String(),
      addonId: t.String(),
    }),
  })
  // ปิดใช้งาน addon
  .post('/sites/:siteId/:addonId/deactivate', async ({ params, addonService, userId }) => {
    return await addonService.deactivateAddon(params.siteId, params.addonId, userId);
  }, {
    params: t.Object({
      siteId: t.String(),
      addonId: t.String(),
    }),
  })
  // ยกเลิกการเช่า addon
  .delete('/sites/:siteId/:addonId', async ({ params, addonService, userId }) => {
    return await addonService.cancelAddonRental(params.siteId, params.addonId, userId);
  }, {
    params: t.Object({
      siteId: t.String(),
      addonId: t.String(),
    }),
  })
  // ดึงข้อมูลการใช้งาน addon
  .get('/sites/:siteId/:addonId/usage', async ({ params, addonService, userId }) => {
    return await addonService.getAddonUsage(params.siteId, params.addonId, userId);
  }, {
    params: t.Object({
      siteId: t.String(),
      addonId: t.String(),
    }),
  });

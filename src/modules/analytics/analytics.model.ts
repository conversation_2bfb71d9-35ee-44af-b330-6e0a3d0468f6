import { generateId } from '@/core/utils';
import mongoose, { type Document, Schema } from 'mongoose';

export interface IHeatmapData extends Document {
  _id: string;
  siteId: string;
  pageUrl: string;
  elementSelector: string;
  elementType: string;
  x: number;
  y: number;
  width: number;
  height: number;
  clicks: number;
  hovers: number;
  scrollDepth: number;
  sessionId: string;
  userId?: string;
  userAgent: string;
  timestamp: Date;
  createdAt: Date;
}

export interface IUserBehavior extends Document {
  _id: string;
  siteId: string;
  userId?: string;
  sessionId: string;
  pageViews: Array<{
    url: string;
    title: string;
    duration: number;
    timestamp: Date;
  }>;
  clicks: Array<{
    element: string;
    url: string;
    timestamp: Date;
  }>;
  scrolls: Array<{
    depth: number;
    url: string;
    timestamp: Date;
  }>;
  searches: Array<{
    query: string;
    results: number;
    timestamp: Date;
  }>;
  conversions: Array<{
    type: 'purchase' | 'signup' | 'download' | 'contact';
    value: number;
    timestamp: Date;
  }>;
  deviceInfo: {
    type: string;
    os: string;
    browser: string;
    screenSize: string;
  };
  location: {
    country: string;
    city: string;
    ip: string;
  };
  startTime: Date;
  endTime?: Date;
  duration: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IABTest extends Document {
  _id: string;
  siteId: string;
  name: string;
  description: string;
  type: 'page' | 'element' | 'feature' | 'pricing';
  status: 'draft' | 'active' | 'paused' | 'completed';
  variants: Array<{
    name: string;
    description: string;
    traffic: number; // percentage
    config: Record<string, any>;
  }>;
  goals: Array<{
    name: string;
    type: 'conversion' | 'revenue' | 'engagement' | 'custom';
    metric: string;
    target: number;
  }>;
  results: {
    totalVisitors: number;
    conversions: Array<{
      variant: string;
      goal: string;
      count: number;
      rate: number;
      revenue: number;
    }>;
    confidence: number;
    winner?: string;
  };
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IConversionFunnel extends Document {
  _id: string;
  siteId: string;
  name: string;
  description: string;
  steps: Array<{
    name: string;
    url: string;
    type: 'page' | 'action' | 'event';
    order: number;
  }>;
  metrics: {
    totalVisitors: number;
    stepConversions: Array<{
      step: string;
      visitors: number;
      conversionRate: number;
      dropOffRate: number;
    }>;
    overallConversionRate: number;
    averageTimeToConvert: number;
  };
  timeRange: {
    startDate: Date;
    endDate: Date;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICohortAnalysis extends Document {
  _id: string;
  siteId: string;
  name: string;
  description: string;
  cohortType: 'signup' | 'first_purchase' | 'first_visit';
  metric: 'revenue' | 'orders' | 'users' | 'engagement';
  timeUnit: 'day' | 'week' | 'month';
  cohorts: Array<{
    period: string;
    size: number;
    retention: Array<{
      period: number;
      value: number;
      percentage: number;
    }>;
  }>;
  analysisDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

const HeatmapDataSchema = new Schema<IHeatmapData>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    pageUrl: { type: String, required: true, index: true },
    elementSelector: { type: String, required: true },
    elementType: { type: String, required: true },
    x: { type: Number, required: true },
    y: { type: Number, required: true },
    width: { type: Number, required: true },
    height: { type: Number, required: true },
    clicks: { type: Number, default: 0 },
    hovers: { type: Number, default: 0 },
    scrollDepth: { type: Number, default: 0 },
    sessionId: { type: String, required: true, index: true },
    userId: { type: String, index: true },
    userAgent: { type: String },
    timestamp: { type: Date, required: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const UserBehaviorSchema = new Schema<IUserBehavior>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, index: true },
    sessionId: { type: String, required: true, index: true },
    pageViews: [
      {
        url: { type: String, required: true },
        title: { type: String },
        duration: { type: Number, default: 0 },
        timestamp: { type: Date, required: true },
      },
    ],
    clicks: [
      {
        element: { type: String, required: true },
        url: { type: String, required: true },
        timestamp: { type: Date, required: true },
      },
    ],
    scrolls: [
      {
        depth: { type: Number, required: true },
        url: { type: String, required: true },
        timestamp: { type: Date, required: true },
      },
    ],
    searches: [
      {
        query: { type: String, required: true },
        results: { type: Number, default: 0 },
        timestamp: { type: Date, required: true },
      },
    ],
    conversions: [
      {
        type: {
          type: String,
          enum: ['purchase', 'signup', 'download', 'contact'],
          required: true,
        },
        value: { type: Number, default: 0 },
        timestamp: { type: Date, required: true },
      },
    ],
    deviceInfo: {
      type: { type: String },
      os: { type: String },
      browser: { type: String },
      screenSize: { type: String },
    },
    location: {
      country: { type: String },
      city: { type: String },
      ip: { type: String },
    },
    startTime: { type: Date, required: true },
    endTime: { type: Date },
    duration: { type: Number, default: 0 },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ABTestSchema = new Schema<IABTest>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    description: { type: String },
    type: {
      type: String,
      required: true,
      enum: ['page', 'element', 'feature', 'pricing'],
    },
    status: {
      type: String,
      enum: ['draft', 'active', 'paused', 'completed'],
      default: 'draft',
    },
    variants: [
      {
        name: { type: String, required: true },
        description: { type: String },
        traffic: { type: Number, required: true },
        config: { type: Schema.Types.Mixed },
      },
    ],
    goals: [
      {
        name: { type: String, required: true },
        type: {
          type: String,
          enum: ['conversion', 'revenue', 'engagement', 'custom'],
          required: true,
        },
        metric: { type: String, required: true },
        target: { type: Number, default: 0 },
      },
    ],
    results: {
      totalVisitors: { type: Number, default: 0 },
      conversions: [
        {
          variant: { type: String, required: true },
          goal: { type: String, required: true },
          count: { type: Number, default: 0 },
          rate: { type: Number, default: 0 },
          revenue: { type: Number, default: 0 },
        },
      ],
      confidence: { type: Number, default: 0 },
      winner: { type: String },
    },
    startDate: { type: Date },
    endDate: { type: Date },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ConversionFunnelSchema = new Schema<IConversionFunnel>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    description: { type: String },
    steps: [
      {
        name: { type: String, required: true },
        url: { type: String, required: true },
        type: {
          type: String,
          enum: ['page', 'action', 'event'],
          required: true,
        },
        order: { type: Number, required: true },
      },
    ],
    metrics: {
      totalVisitors: { type: Number, default: 0 },
      stepConversions: [
        {
          step: { type: String, required: true },
          visitors: { type: Number, default: 0 },
          conversionRate: { type: Number, default: 0 },
          dropOffRate: { type: Number, default: 0 },
        },
      ],
      overallConversionRate: { type: Number, default: 0 },
      averageTimeToConvert: { type: Number, default: 0 },
    },
    timeRange: {
      startDate: { type: Date, required: true },
      endDate: { type: Date, required: true },
    },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const CohortAnalysisSchema = new Schema<ICohortAnalysis>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    description: { type: String },
    cohortType: {
      type: String,
      required: true,
      enum: ['signup', 'first_purchase', 'first_visit'],
    },
    metric: {
      type: String,
      required: true,
      enum: ['revenue', 'orders', 'users', 'engagement'],
    },
    timeUnit: {
      type: String,
      required: true,
      enum: ['day', 'week', 'month'],
    },
    cohorts: [
      {
        period: { type: String, required: true },
        size: { type: Number, required: true },
        retention: [
          {
            period: { type: Number, required: true },
            value: { type: Number, required: true },
            percentage: { type: Number, required: true },
          },
        ],
      },
    ],
    analysisDate: { type: Date, required: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
HeatmapDataSchema.index({ siteId: 1, pageUrl: 1 });
HeatmapDataSchema.index({ siteId: 1, sessionId: 1 });
HeatmapDataSchema.index({ timestamp: -1 });

UserBehaviorSchema.index({ siteId: 1, userId: 1 });
UserBehaviorSchema.index({ siteId: 1, sessionId: 1 });
UserBehaviorSchema.index({ startTime: -1 });

ABTestSchema.index({ siteId: 1, status: 1 });
ABTestSchema.index({ siteId: 1, type: 1 });

ConversionFunnelSchema.index({ siteId: 1, isActive: 1 });

CohortAnalysisSchema.index({ siteId: 1, cohortType: 1 });
CohortAnalysisSchema.index({ analysisDate: -1 });

// Static methods
HeatmapDataSchema.statics.findByPage = async function(siteId: string, pageUrl: string) {
  return this.find({ siteId, pageUrl }).sort({ timestamp: -1 });
};

UserBehaviorSchema.statics.findByUser = async function(siteId: string, userId: string) {
  return this.find({ siteId, userId }).sort({ startTime: -1 });
};

ABTestSchema.statics.findActive = async function(siteId: string) {
  return this.find({ siteId, status: 'active' });
};

ConversionFunnelSchema.statics.findActive = async function(siteId: string) {
  return this.find({ siteId, isActive: true });
};

CohortAnalysisSchema.statics.findByType = async function(siteId: string, cohortType: string) {
  return this.find({ siteId, cohortType }).sort({ analysisDate: -1 });
};

export const HeatmapData = mongoose.model<IHeatmapData>('HeatmapData', HeatmapDataSchema);
export const UserBehavior = mongoose.model<IUserBehavior>('UserBehavior', UserBehaviorSchema);
export const ABTest = mongoose.model<IABTest>('ABTest', ABTestSchema);
export const ConversionFunnel = mongoose.model<IConversionFunnel>('ConversionFunnel', ConversionFunnelSchema);
export const CohortAnalysis = mongoose.model<ICohortAnalysis>('CohortAnalysis', CohortAnalysisSchema);

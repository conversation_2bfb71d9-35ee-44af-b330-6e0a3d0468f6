import mongoose, { Document, Schema } from 'mongoose';

// === NEWS MODELS ===

export interface INewsCategory extends Document {
  siteId: string;
  name: string;
  slug: string;
  description?: string;
  createdAt: Date;
}

const NewsCategorySchema = new Schema<INewsCategory>({
  siteId: { type: String, required: true, index: true },
  name: { type: String, required: true },
  slug: { type: String, required: true },
  description: { type: String },
}, {
  timestamps: { createdAt: true, updatedAt: false },
  collection: 'news_categories',
});

NewsCategorySchema.index({ siteId: 1, slug: 1 }, { unique: true });

export interface INews extends Document {
  siteId: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  categoryId?: string;
  featuredImage?: string;
  published: boolean;
  authorId: string;
  viewsCount: number;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
}

const NewsSchema = new Schema<INews>({
  siteId: { type: String, required: true, index: true },
  title: { type: String, required: true },
  slug: { type: String, required: true },
  content: { type: String, required: true },
  excerpt: { type: String },
  categoryId: { type: String, index: true },
  featuredImage: { type: String },
  published: { type: Boolean, default: false, index: true },
  authorId: { type: String, required: true, index: true },
  viewsCount: { type: Number, default: 0 },
  publishedAt: { type: Date },
}, {
  timestamps: true,
  collection: 'news',
});

NewsSchema.index({ siteId: 1, slug: 1 }, { unique: true });
NewsSchema.index({ siteId: 1, published: 1 });
NewsSchema.index({ createdAt: -1 });

// === BLOG MODELS ===

export interface IBlog extends Document {
  siteId: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  tags: string[];
  featuredImage?: string;
  published: boolean;
  authorId: string;
  viewsCount: number;
  likesCount: number;
  commentsCount: number;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
}

const BlogSchema = new Schema<IBlog>({
  siteId: { type: String, required: true, index: true },
  title: { type: String, required: true },
  slug: { type: String, required: true },
  content: { type: String, required: true },
  excerpt: { type: String },
  tags: [{ type: String }],
  featuredImage: { type: String },
  published: { type: Boolean, default: false, index: true },
  authorId: { type: String, required: true, index: true },
  viewsCount: { type: Number, default: 0 },
  likesCount: { type: Number, default: 0 },
  commentsCount: { type: Number, default: 0 },
  publishedAt: { type: Date },
}, {
  timestamps: true,
  collection: 'blogs',
});

BlogSchema.index({ siteId: 1, slug: 1 }, { unique: true });
BlogSchema.index({ siteId: 1, published: 1 });
BlogSchema.index({ createdAt: -1 });
BlogSchema.index({ tags: 1 });

export interface IBlogComment extends Document {
  blogId: string;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  content: string;
  approved: boolean;
  parentId?: string;
  createdAt: Date;
}

const BlogCommentSchema = new Schema<IBlogComment>({
  blogId: { type: String, required: true, index: true },
  userId: { type: String, index: true },
  guestName: { type: String },
  guestEmail: { type: String },
  content: { type: String, required: true },
  approved: { type: Boolean, default: false, index: true },
  parentId: { type: String, index: true },
}, {
  timestamps: { createdAt: true, updatedAt: false },
  collection: 'blog_comments',
});

// === NOVEL MODELS ===

export interface INovel extends Document {
  siteId: string;
  title: string;
  slug: string;
  description: string;
  genre?: string;
  tags: string[];
  coverImage?: string;
  published: boolean;
  completed: boolean;
  authorId: string;
  chaptersCount: number;
  viewsCount: number;
  likesCount: number;
  rating: number;
  ratingCount: number;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
}

const NovelSchema = new Schema<INovel>({
  siteId: { type: String, required: true, index: true },
  title: { type: String, required: true },
  slug: { type: String, required: true },
  description: { type: String, required: true },
  genre: { type: String, index: true },
  tags: [{ type: String }],
  coverImage: { type: String },
  published: { type: Boolean, default: false, index: true },
  completed: { type: Boolean, default: false },
  authorId: { type: String, required: true, index: true },
  chaptersCount: { type: Number, default: 0 },
  viewsCount: { type: Number, default: 0 },
  likesCount: { type: Number, default: 0 },
  rating: { type: Number, default: 0, min: 0, max: 5 },
  ratingCount: { type: Number, default: 0 },
  publishedAt: { type: Date },
}, {
  timestamps: true,
  collection: 'novels',
});

// NovelSchema.index({ siteId: 1, slug: 1 }, { unique: true });
// NovelSchema.index({ siteId: 1, published: 1 });
// NovelSchema.index({ genre: 1 });
// NovelSchema.index({ rating: -1 });
// NovelSchema.index({ createdAt: -1 });

export interface INovelChapter extends Document {
  novelId: string;
  title: string;
  slug: string;
  content: string;
  chapterNumber: number;
  published: boolean;
  isPremium: boolean;
  viewsCount: number;
  likesCount: number;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
}

const NovelChapterSchema = new Schema<INovelChapter>({
  novelId: { type: String, required: true, index: true },
  title: { type: String, required: true },
  slug: { type: String, required: true },
  content: { type: String, required: true },
  chapterNumber: { type: Number, required: true },
  published: { type: Boolean, default: false, index: true },
  isPremium: { type: Boolean, default: false },
  viewsCount: { type: Number, default: 0 },
  likesCount: { type: Number, default: 0 },
  publishedAt: { type: Date },
}, {
  timestamps: true,
  collection: 'novel_chapters',
});

NovelChapterSchema.index({ novelId: 1, chapterNumber: 1 }, { unique: true });
NovelChapterSchema.index({ novelId: 1, slug: 1 }, { unique: true });
NovelChapterSchema.index({ novelId: 1, published: 1 });

export interface INovelRating extends Document {
  novelId: string;
  userId: string;
  rating: number;
  review?: string;
  createdAt: Date;
  updatedAt: Date;
}

const NovelRatingSchema = new Schema<INovelRating>({
  novelId: { type: String, required: true, index: true },
  userId: { type: String, required: true, index: true },
  rating: { type: Number, required: true, min: 1, max: 5 },
  review: { type: String },
}, {
  timestamps: true,
  collection: 'novel_ratings',
});

NovelRatingSchema.index({ novelId: 1, userId: 1 }, { unique: true });

// === CONTENT VIEWS MODEL ===

export interface IContentView extends Document {
  contentType: 'news' | 'blog' | 'novel' | 'chapter';
  contentId: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  viewedAt: Date;
}

const ContentViewSchema = new Schema<IContentView>({
  contentType: {
    type: String,
    enum: ['news', 'blog', 'novel', 'chapter'],
    required: true,
    index: true,
  },
  contentId: { type: String, required: true, index: true },
  userId: { type: String, index: true },
  ipAddress: { type: String },
  userAgent: { type: String },
}, {
  timestamps: { createdAt: 'viewedAt', updatedAt: false },
  collection: 'content_views',
});

ContentViewSchema.index({ contentType: 1, contentId: 1 });
ContentViewSchema.index({ viewedAt: -1 });

// Export models
export const NewsCategory = mongoose.model<INewsCategory>('NewsCategory', NewsCategorySchema);
export const News = mongoose.model<INews>('News', NewsSchema);
export const Blog = mongoose.model<IBlog>('Blog', BlogSchema);
export const BlogComment = mongoose.model<IBlogComment>('BlogComment', BlogCommentSchema);
export const Novel = mongoose.model<INovel>('Novel', NovelSchema);
export const NovelChapter = mongoose.model<INovelChapter>('NovelChapter', NovelChapterSchema);
export const NovelRating = mongoose.model<INovelRating>('NovelRating', NovelRatingSchema);
export const ContentView = mongoose.model<IContentView>('ContentView', ContentViewSchema);

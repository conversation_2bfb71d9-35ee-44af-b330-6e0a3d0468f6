import { Elysia, t } from 'elysia';
import { addonGuard } from '../../core/guards/addon.guard';
import { requireSiteMember, requireUserAuth } from '../../core/middleware/unified-auth';
import { BlogService } from './blog.service';
import { NewsService } from './news.service';
import { NovelService } from './novel.service';

export const contentModule = new Elysia({ prefix: '/content' })
  .use(requireUserAuth)
  .use(requireSiteMember)
  .derive(({ store }) => ({
    user: store.user,
    userId: store.user?.id || store.user?.userId,
  }))
  .decorate('newsService', new NewsService())
  .decorate('blogService', new BlogService())
  .decorate('novelService', new NovelService())
  // === NEWS ROUTES ===
  .group('/news', app =>
    app
      .use(addonGuard('news'))
      // ดึงรายการข่าว
      .get('/sites/:siteId', async ({ params, newsService, userId }) => {
        return await newsService.getNewsList(params.siteId, userId);
      })
      // สร้างข่าวใหม่
      .post('/sites/:siteId', async ({ params, body, newsService, userId }) => {
        return await newsService.createNews(params.siteId, body, userId);
      }, {
        body: t.Object({
          title: t.String(),
          content: t.String(),
          excerpt: t.Optional(t.String()),
          categoryId: t.Optional(t.String()),
          published: t.Optional(t.Boolean()),
          featuredImage: t.Optional(t.String()),
        }),
      })
      // อัปเดตข่าว
      .put('/sites/:siteId/:newsId', async ({ params, body, newsService, userId }) => {
        return await newsService.updateNews(params.siteId, params.newsId, body, userId);
      })
      // ลบข่าว
      .delete('/sites/:siteId/:newsId', async ({ params, newsService, userId }) => {
        return await newsService.deleteNews(params.siteId, params.newsId, userId);
      })
      // เปลี่ยนสถานะการเผยแพร่
      .post('/sites/:siteId/:newsId/toggle-publish', async ({ params, body, newsService, userId }) => {
        return await newsService.togglePublish(params.siteId, params.newsId, body.published, userId);
      }, {
        body: t.Object({
          published: t.Boolean(),
        }),
      })
      // จัดการหมวดหมู่ข่าว
      .get('/sites/:siteId/categories', async ({ params, newsService, userId }) => {
        return await newsService.getCategories(params.siteId, userId);
      })
      .post('/sites/:siteId/categories', async ({ params, body, newsService, userId }) => {
        return await newsService.createCategory(params.siteId, body, userId);
      }, {
        body: t.Object({
          name: t.String(),
          description: t.Optional(t.String()),
        }),
      }))
  // === BLOG ROUTES ===
  .group('/blogs', app =>
    app
      .use(addonGuard('blog'))
      // ดึงรายการบล็อก
      .get('/sites/:siteId', async ({ params, blogService, userId }) => {
        return await blogService.getBlogsList(params.siteId, userId);
      })
      // สร้างบล็อกใหม่
      .post('/sites/:siteId', async ({ params, body, blogService, userId }) => {
        return await blogService.createBlog(params.siteId, body, userId);
      }, {
        body: t.Object({
          title: t.String(),
          content: t.String(),
          excerpt: t.Optional(t.String()),
          tags: t.Optional(t.Array(t.String())),
          published: t.Optional(t.Boolean()),
          featuredImage: t.Optional(t.String()),
        }),
      })
      // อัปเดตบล็อก
      .put('/sites/:siteId/:blogId', async ({ params, body, blogService, userId }) => {
        return await blogService.updateBlog(params.siteId, params.blogId, body, userId);
      })
      // ลบบล็อก
      .delete('/sites/:siteId/:blogId', async ({ params, blogService, userId }) => {
        return await blogService.deleteBlog(params.siteId, params.blogId, userId);
      })
      // เปลี่ยนสถานะการเผยแพร่
      .post('/sites/:siteId/:blogId/toggle-publish', async ({ params, body, blogService, userId }) => {
        return await blogService.togglePublish(params.siteId, params.blogId, body.published, userId);
      }, {
        body: t.Object({
          published: t.Boolean(),
        }),
      }))
  // === NOVEL ROUTES ===
  .group('/novels', app =>
    app
      .use(addonGuard('novel'))
      // ดึงรายการนิยาย
      .get('/sites/:siteId', async ({ params, novelService, userId }) => {
        return await novelService.getNovelsList(params.siteId, userId);
      })
      // สร้างนิยายใหม่
      .post('/sites/:siteId', async ({ params, body, novelService, userId }) => {
        return await novelService.createNovel(params.siteId, body, userId);
      }, {
        body: t.Object({
          title: t.String(),
          description: t.String(),
          genre: t.Optional(t.String()),
          tags: t.Optional(t.Array(t.String())),
          published: t.Optional(t.Boolean()),
          coverImage: t.Optional(t.String()),
        }),
      })
      // อัปเดตนิยาย
      .put('/sites/:siteId/:novelId', async ({ params, body, novelService, userId }) => {
        return await novelService.updateNovel(params.siteId, params.novelId, body, userId);
      })
      // ลบนิยาย
      .delete('/sites/:siteId/:novelId', async ({ params, novelService, userId }) => {
        return await novelService.deleteNovel(params.siteId, params.novelId, userId);
      })
      // จัดการตอนนิยาย
      .get('/sites/:siteId/:novelId/chapters', async ({ params, novelService, userId }) => {
        return await novelService.getChapters(params.siteId, params.novelId, userId);
      })
      .post('/sites/:siteId/:novelId/chapters', async ({ params, body, novelService, userId }) => {
        return await novelService.createChapter(params.siteId, params.novelId, body, userId);
      }, {
        body: t.Object({
          title: t.String(),
          content: t.String(),
          chapterNumber: t.Number(),
          published: t.Optional(t.Boolean()),
        }),
      }));

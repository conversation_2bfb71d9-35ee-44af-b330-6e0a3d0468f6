import { AddonService } from '../addon/addon.service';
import { validateSiteAccess } from '../site/site.service';
import { Novel, NovelChapter, NovelRating } from './content.model';

export class NovelService {
  private addonService: AddonService;

  constructor() {
    this.addonService = new AddonService();
  }

  // ดึงรายการนิยาย
  async getNovelsList(siteId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าเปิดใช้งาน addon novel หรือไม่
      const isActive = await this.addonService.isAddonActive(siteId, 'novel');
      if (!isActive) {
        throw new Error('ระบบนิยายยังไม่ได้เปิดใช้งาน');
      }

      const novels = await Novel.find({ siteId })
        .populate('authorId', 'name')
        .sort({ createdAt: -1 });

      return {
        success: true,
        data: novels,
      };
    }
    catch (error) {
      console.error('Error getting novels list:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถดึงรายการนิยายได้');
    }
  }

  // สร้างนิยายใหม่
  async createNovel(siteId: string, novelData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่าเปิดใช้งาน addon novel หรือไม่
      const isActive = await this.addonService.isAddonActive(siteId, 'novel');
      if (!isActive) {
        throw new Error('ระบบนิยายยังไม่ได้เปิดใช้งาน');
      }

      // สร้าง slug จากหัวข้อ
      const slug = this.generateSlug(novelData.title);

      const newNovel = new Novel({
        siteId,
        title: novelData.title,
        slug,
        description: novelData.description,
        genre: novelData.genre || null,
        tags: novelData.tags || [],
        coverImage: novelData.coverImage || null,
        published: novelData.published || false,
        authorId: userId,
        publishedAt: novelData.published ? new Date() : null,
      });

      await newNovel.save();

      return {
        success: true,
        message: 'สร้างนิยายเรียบร้อยแล้ว',
        data: newNovel,
      };
    }
    catch (error) {
      console.error('Error creating novel:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถสร้างนิยายได้');
    }
  }

  // อัปเดตนิยาย
  async updateNovel(siteId: string, novelId: string, novelData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่านิยายมีอยู่จริง
      const existingNovel = await Novel.findOne({ _id: novelId, siteId });

      if (!existingNovel) {
        throw new Error('ไม่พบนิยายที่ระบุ');
      }

      const updateData = {
        title: novelData.title,
        slug: this.generateSlug(novelData.title),
        description: novelData.description,
        genre: novelData.genre || null,
        tags: novelData.tags || [],
        coverImage: novelData.coverImage || null,
        published: novelData.published,
        completed: novelData.completed || false,
        publishedAt: novelData.published && !existingNovel.published ? new Date() : existingNovel.publishedAt,
      };

      await Novel.findByIdAndUpdate(novelId, updateData);

      return {
        success: true,
        message: 'อัปเดตนิยายเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error updating novel:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถอัปเดตนิยายได้');
    }
  }

  // ลบนิยาย
  async deleteNovel(siteId: string, novelId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่านิยายมีอยู่จริง
      const existingNovel = await Novel.findOne({ _id: novelId, siteId });

      if (!existingNovel) {
        throw new Error('ไม่พบนิยายที่ระบุ');
      }

      // ลบตอนทั้งหมด
      await NovelChapter.deleteMany({ novelId });

      // ลบการให้คะแนน
      await NovelRating.deleteMany({ novelId });

      // ลบนิยาย
      await Novel.findByIdAndDelete(novelId);

      return {
        success: true,
        message: 'ลบนิยายเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error deleting novel:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถลบนิยายได้');
    }
  }

  // ดึงตอนนิยาย
  async getChapters(siteId: string, novelId: string, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่านิยายมีอยู่จริง
      const novel = await Novel.findOne({ _id: novelId, siteId });
      if (!novel) {
        throw new Error('ไม่พบนิยายที่ระบุ');
      }

      const chapters = await NovelChapter.find({ novelId })
        .sort({ chapterNumber: 1 });

      return {
        success: true,
        data: {
          novel,
          chapters,
        },
      };
    }
    catch (error) {
      console.error('Error getting chapters:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถดึงตอนนิยายได้');
    }
  }

  // สร้างตอนใหม่
  async createChapter(siteId: string, novelId: string, chapterData: any, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      // ตรวจสอบว่านิยายมีอยู่จริง
      const novel = await Novel.findOne({ _id: novelId, siteId });
      if (!novel) {
        throw new Error('ไม่พบนิยายที่ระบุ');
      }

      // สร้าง slug จากหัวข้อ
      const slug = this.generateSlug(chapterData.title);

      const newChapter = new NovelChapter({
        novelId,
        title: chapterData.title,
        slug,
        content: chapterData.content,
        chapterNumber: chapterData.chapterNumber,
        published: chapterData.published || false,
        isPremium: chapterData.isPremium || false,
        publishedAt: chapterData.published ? new Date() : null,
      });

      await newChapter.save();

      // อัปเดตจำนวนตอนในนิยาย
      await Novel.findByIdAndUpdate(novelId, {
        $inc: { chaptersCount: 1 },
      });

      return {
        success: true,
        message: 'สร้างตอนใหม่เรียบร้อยแล้ว',
        data: newChapter,
      };
    }
    catch (error) {
      console.error('Error creating chapter:', error);
      throw new Error(error instanceof Error ? error.message : 'ไม่สามารถสร้างตอนใหม่ได้');
    }
  }

  // เปลี่ยนสถานะการเผยแพร่นิยาย
  async togglePublish(siteId: string, novelId: string, published: boolean, userId: string) {
    try {
      // ตรวจสอบสิทธิ์เข้าถึง site
      await validateSiteAccess(siteId, userId);

      const updateData: any = { published };
      if (published) {
        updateData.publishedAt = new Date();
      }

      await Novel.findOneAndUpdate(
        { _id: novelId, siteId },
        updateData,
      );

      return {
        success: true,
        message: published ? 'เผยแพร่นิยายเรียบร้อยแล้ว' : 'ยกเลิกการเผยแพร่นิยายเรียบร้อยแล้ว',
      };
    }
    catch (error) {
      console.error('Error toggling novel publish status:', error);
      throw new Error('ไม่สามารถเปลี่ยนสถานะการเผยแพร่ได้');
    }
  }

  // สร้าง slug จากข้อความ
  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
}

import { generateId } from '@/core/utils';
import mongoose, { type Document, Schema } from 'mongoose';

// ประเภทการแจ้งเตือนที่ครอบคลุมทุกระบบ
export type NotificationType =
  | 'order' // คำสั่งซื้อ
  | 'product' // สินค้า
  | 'promotion' // โปรโมชั่น
  | 'system' // ระบบ
  | 'chat' // แชท
  | 'affiliate' // พันธมิตร
  | 'topup' // เติมเงิน
  | 'membership' // สมาชิกใหม่
  | 'expiry' // วันหมดอายุ
  | 'inventory' // สต็อกสินค้า
  | 'payment' // การชำระเงิน
  | 'security' // ความปลอดภัย
  | 'marketing'; // การตลาด

export interface INotification extends Document {
  _id: string;
  siteId: string;
  recipientId: string;
  recipientType: 'user' | 'customer' | 'admin';
  type: NotificationType;
  title: string;
  message: string;
  data?: {
    orderId?: string;
    productId?: string;
    chatRoomId?: string;
    affiliateId?: string;
    customerId?: string;
    userId?: string;
    amount?: number;
    balance?: number;
    expiryDate?: Date;
    daysLeft?: number;
    stockLevel?: number;
    threshold?: number;
    url?: string;
    image?: string;
    metadata?: Record<string, any>;
  };
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'unread' | 'read' | 'archived';
  channels: {
    inApp: boolean;
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  deliveryStatus: {
    inApp: { sent: boolean; delivered: boolean; read: boolean; };
    email: { sent: boolean; delivered: boolean; opened: boolean; };
    push: { sent: boolean; delivered: boolean; clicked: boolean; };
    sms: { sent: boolean; delivered: boolean; };
  };
  scheduledAt?: Date;
  sentAt?: Date;
  readAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface INotificationTemplate extends Document {
  _id: string;
  siteId: string;
  name: string;
  type: NotificationType;
  title: string;
  message: string;
  variables: string[];
  channels: {
    inApp: boolean;
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  conditions?: {
    userType?: string[];
    minAmount?: number;
    maxAmount?: number;
    daysBeforeExpiry?: number;
    stockThreshold?: number;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface INotificationSettings extends Document {
  _id: string;
  siteId: string;
  userId: string;
  userType: 'user' | 'customer' | 'admin';
  preferences: {
    orderUpdates: boolean;
    productAlerts: boolean;
    promotions: boolean;
    systemMessages: boolean;
    chatMessages: boolean;
    affiliateUpdates: boolean;
    topupAlerts: boolean;
    membershipUpdates: boolean;
    expiryWarnings: boolean;
    inventoryAlerts: boolean;
    paymentNotifications: boolean;
    securityAlerts: boolean;
    marketingMessages: boolean;
  };
  channels: {
    inApp: boolean;
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:mm
    endTime: string; // HH:mm
    timezone: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Schema สำหรับ Notification Rule (เพิ่มใหม่)
export interface INotificationRule extends Document {
  _id: string;
  siteId: string;
  name: string;
  type: NotificationType;
  trigger: {
    event: string; // 'user_register', 'order_created', 'stock_low', 'expiry_warning'
    conditions: Record<string, any>;
  };
  template: string; // template ID
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const NotificationSchema = new Schema<INotification>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    recipientId: { type: String, required: true, index: true },
    recipientType: {
      type: String,
      required: true,
      enum: ['user', 'customer', 'admin'],
    },
    type: {
      type: String,
      required: true,
      enum: [
        'order',
        'product',
        'promotion',
        'system',
        'chat',
        'affiliate',
        'topup',
        'membership',
        'expiry',
        'inventory',
        'payment',
        'security',
        'marketing',
      ],
    },
    title: { type: String, required: true },
    message: { type: String, required: true },
    data: {
      orderId: { type: String },
      productId: { type: String },
      chatRoomId: { type: String },
      affiliateId: { type: String },
      customerId: { type: String },
      userId: { type: String },
      amount: { type: Number },
      balance: { type: Number },
      expiryDate: { type: Date },
      daysLeft: { type: Number },
      stockLevel: { type: Number },
      threshold: { type: Number },
      url: { type: String },
      image: { type: String },
      metadata: { type: Schema.Types.Mixed },
    },
    priority: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
    },
    status: {
      type: String,
      required: true,
      enum: ['unread', 'read', 'archived'],
      default: 'unread',
    },
    channels: {
      inApp: { type: Boolean, default: true },
      email: { type: Boolean, default: false },
      push: { type: Boolean, default: false },
      sms: { type: Boolean, default: false },
    },
    deliveryStatus: {
      inApp: {
        sent: { type: Boolean, default: false },
        delivered: { type: Boolean, default: false },
        read: { type: Boolean, default: false },
      },
      email: {
        sent: { type: Boolean, default: false },
        delivered: { type: Boolean, default: false },
        opened: { type: Boolean, default: false },
      },
      push: {
        sent: { type: Boolean, default: false },
        delivered: { type: Boolean, default: false },
        clicked: { type: Boolean, default: false },
      },
      sms: {
        sent: { type: Boolean, default: false },
        delivered: { type: Boolean, default: false },
      },
    },
    scheduledAt: { type: Date },
    sentAt: { type: Date },
    readAt: { type: Date },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const NotificationTemplateSchema = new Schema<INotificationTemplate>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: [
        'order',
        'product',
        'promotion',
        'system',
        'chat',
        'affiliate',
        'topup',
        'membership',
        'expiry',
        'inventory',
        'payment',
        'security',
        'marketing',
      ],
    },
    title: { type: String, required: true },
    message: { type: String, required: true },
    variables: [{ type: String }],
    channels: {
      inApp: { type: Boolean, default: true },
      email: { type: Boolean, default: false },
      push: { type: Boolean, default: false },
      sms: { type: Boolean, default: false },
    },
    conditions: {
      userType: [{ type: String }],
      minAmount: { type: Number },
      maxAmount: { type: Number },
      daysBeforeExpiry: { type: Number },
      stockThreshold: { type: Number },
    },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const NotificationSettingsSchema = new Schema<INotificationSettings>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    userType: {
      type: String,
      required: true,
      enum: ['user', 'customer', 'admin'],
    },
    preferences: {
      orderUpdates: { type: Boolean, default: true },
      productAlerts: { type: Boolean, default: true },
      promotions: { type: Boolean, default: true },
      systemMessages: { type: Boolean, default: true },
      chatMessages: { type: Boolean, default: true },
      affiliateUpdates: { type: Boolean, default: true },
      topupAlerts: { type: Boolean, default: true },
      membershipUpdates: { type: Boolean, default: true },
      expiryWarnings: { type: Boolean, default: true },
      inventoryAlerts: { type: Boolean, default: false }, // เฉพาะ admin
      paymentNotifications: { type: Boolean, default: true },
      securityAlerts: { type: Boolean, default: true },
      marketingMessages: { type: Boolean, default: false },
    },
    channels: {
      inApp: { type: Boolean, default: true },
      email: { type: Boolean, default: false },
      push: { type: Boolean, default: false },
      sms: { type: Boolean, default: false },
    },
    quietHours: {
      enabled: { type: Boolean, default: false },
      startTime: { type: String, default: '22:00' },
      endTime: { type: String, default: '08:00' },
      timezone: { type: String, default: 'Asia/Bangkok' },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const NotificationRuleSchema = new Schema<INotificationRule>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: [
        'order',
        'product',
        'promotion',
        'system',
        'chat',
        'affiliate',
        'topup',
        'membership',
        'expiry',
        'inventory',
        'payment',
        'security',
        'marketing',
      ],
    },
    trigger: {
      event: { type: String, required: true },
      conditions: { type: Schema.Types.Mixed },
    },
    template: { type: String, required: true },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
NotificationSchema.index({ siteId: 1, recipientId: 1 });
NotificationSchema.index({ siteId: 1, status: 1 });
NotificationSchema.index({ siteId: 1, type: 1 });
NotificationSchema.index({ createdAt: -1 });
NotificationSchema.index({ scheduledAt: 1 });

NotificationTemplateSchema.index({ siteId: 1, type: 1 });
NotificationTemplateSchema.index({ siteId: 1, isActive: 1 });

NotificationSettingsSchema.index({ siteId: 1, userId: 1 });
NotificationSettingsSchema.index({ siteId: 1, userType: 1 });

NotificationRuleSchema.index({ siteId: 1, type: 1 });
NotificationRuleSchema.index({ siteId: 1, isActive: 1 });

// Static methods
NotificationSchema.statics.findUnread = async function(siteId: string, recipientId: string) {
  return this.find({ siteId, recipientId, status: 'unread' }).sort({ createdAt: -1 });
};

NotificationSchema.statics.markAsRead = async function(siteId: string, notificationIds: string[]) {
  return this.updateMany(
    { siteId, _id: { $in: notificationIds } },
    {
      $set: {
        status: 'read',
        readAt: new Date(),
        'deliveryStatus.inApp.read': true,
      },
    },
  );
};

NotificationSchema.statics.getUnreadCount = async function(siteId: string, recipientId: string) {
  return this.countDocuments({ siteId, recipientId, status: 'unread' });
};

NotificationTemplateSchema.statics.findByType = async function(siteId: string, type: string) {
  return this.findOne({ siteId, type, isActive: true });
};

NotificationSettingsSchema.statics.findByUser = async function(siteId: string, userId: string) {
  return this.findOne({ siteId, userId });
};

export const Notification = mongoose.model<INotification>('Notification', NotificationSchema);
export const NotificationTemplate = mongoose.model<INotificationTemplate>(
  'NotificationTemplate',
  NotificationTemplateSchema,
);
export const NotificationSettings = mongoose.model<INotificationSettings>(
  'NotificationSettings',
  NotificationSettingsSchema,
);
export const NotificationRule = mongoose.model<INotificationRule>('NotificationRule', NotificationRuleSchema);

import { userAuthPlugin } from '@/core/plugins/auth';
import { Elysia, t } from 'elysia';
import { invitationQuerySchema } from './invitation.schema';
import {
  acceptInvitation,
  cancelInvitation,
  generateInvitationLink,
  getInvitationByCode,
  getInvitationById,
  getReceivedInvitations,
  rejectInvitation,
  resendInvitation,
} from './invitation.service';

export const invitationRoutes = new Elysia({ prefix: '/invitations' })
  // .use(userAuthPlugin) // Temporarily disable auth for testing
  // ดูคำเชิญที่ได้รับ
  .get(
    '/received',
    async ({ query, user }: any) => {
      const userId = user._id;
      const result = await getReceivedInvitations(userId, query);
      return result;
    },
    {
      query: invitationQuerySchema,
    },
  )
  // ดูคำเชิญเฉพาะ
  .get(
    '/:invitationId',
    async ({ params, user }: any) => {
      const { invitationId } = params;
      const userId = user._id;

      const result = await getInvitationById(invitationId, userId);
      return result;
    },
    {
      params: t.Object({ invitationId: t.String() }),
    },
  )
  // สร้างลิงก์คำเชิญ
  .get(
    '/:invitationId/link',
    async ({ params, request }: any) => {
      const { invitationId } = params;
      const baseUrl = new URL(request.url).origin;

      const inviteLink = await generateInvitationLink(invitationId, baseUrl);
      return {
        success: true,
        data: {
          invitationId,
          inviteLink,
          inviteCode: invitationId,
        },
      };
    },
    {
      params: t.Object({ invitationId: t.String() }),
    },
  )
  // รับคำเชิญด้วยโค้ด (ไม่ต้อง login)
  .get(
    '/code/:inviteCode',
    async ({ params }: any) => {
      const { inviteCode } = params;

      const result = await getInvitationByCode(inviteCode);
      return result;
    },
    {
      params: t.Object({ inviteCode: t.String() }),
    },
  )
  // รับคำเชิญ
  .post(
    '/:invitationId/accept',
    async ({ params }: any) => {
      const { invitationId } = params;
      const userId = 'mdvopwju7A0Un'; // Hardcode for testing
      console.log('🚀 Route: Accepting invitation');
      console.log('📋 userId:', userId);
      console.log('📋 invitationId:', invitationId);

      const result = await acceptInvitation(invitationId, userId);
      return {
        success: true,
        message: 'ยืนยันคำเชิญเรียบร้อยแล้ว',
        statusMessage: 'ยืนยันคำเชิญเรียบร้อยแล้ว',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      params: t.Object({
        invitationId: t.String({
          minLength: 1,
          error: 'invitationId ต้องเป็นข้อความและไม่ว่าง',
        }),
      }),
    },
  )
  // ปฏิเสธคำเชิญ
  .post(
    '/:invitationId/reject',
    async ({ params, user }: any) => {
      const { invitationId } = params;
      const userId = user._id;

      const result = await rejectInvitation(invitationId, userId);
      return result;
    },
    {
      params: t.Object({ invitationId: t.String() }),
    },
  )
  // ยกเลิกคำเชิญ
  .delete(
    '/:invitationId',
    async ({ params, user }: any) => {
      const { invitationId } = params;
      const fromUserId = user._id;

      const result = await cancelInvitation(invitationId, fromUserId);
      return result;
    },
    {
      params: t.Object({ invitationId: t.String() }),
    },
  )
  // ส่งคำเชิญใหม่
  .post(
    '/:invitationId/resend',
    async ({ params, user }: any) => {
      const { invitationId } = params;
      const fromUserId = user._id;

      const result = await resendInvitation(invitationId, fromUserId);
      return result;
    },
    {
      params: t.Object({ invitationId: t.String() }),
    },
  );

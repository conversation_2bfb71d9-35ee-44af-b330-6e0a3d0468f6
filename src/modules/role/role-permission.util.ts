import { HttpError } from '@/core/utils/error';
import type { SiteRole } from '@/modules/role/role.model';
import { getUserRole } from '@/modules/role/role.service';

// Permission levels สำหรับแต่ละ role
const ROLE_PERMISSIONS = {
  owner: {
    level: 4,
    permissions: [
      'site.manage',
      'site.delete',
      'site.transfer',
      'users.manage',
      'users.invite',
      'users.remove',
      'roles.manage',
      'content.manage',
      'content.create',
      'content.edit',
      'content.delete',
      'orders.manage',
      'products.manage',
      'analytics.view',
      'settings.manage',
    ],
  },
  admin: {
    level: 3,
    permissions: [
      'users.invite',
      'users.manage',
      'content.manage',
      'content.create',
      'content.edit',
      'content.delete',
      'orders.manage',
      'products.manage',
      'analytics.view',
    ],
  },
  editor: {
    level: 2,
    permissions: ['content.create', 'content.edit', 'products.manage', 'orders.view'],
  },
  viewer: {
    level: 1,
    permissions: ['content.view', 'orders.view', 'products.view'],
  },
};

// ตรวจสอบสิทธิ์ในเว็บไซต์
export async function checkSitePermission(siteId: string, userId: string, allowedRoles: SiteRole[]): Promise<boolean> {
  const userRole = await getUserRole(siteId, userId);
  return !!userRole && allowedRoles.includes(userRole);
}

// ตรวจสอบสิทธิ์เฉพาะ permission
export async function checkPermission(siteId: string, userId: string, permission: string): Promise<boolean> {
  const userRole = await getUserRole(siteId, userId);
  if (!userRole) return false;

  const rolePermissions = ROLE_PERMISSIONS[userRole as SiteRole];
  return rolePermissions.permissions.includes(permission);
}

// ตรวจสอบระดับสิทธิ์
export async function checkPermissionLevel(siteId: string, userId: string, requiredLevel: number): Promise<boolean> {
  const userRole = await getUserRole(siteId, userId);
  if (!userRole) return false;

  const rolePermissions = ROLE_PERMISSIONS[userRole as SiteRole];
  return rolePermissions.level >= requiredLevel;
}

// Middleware สำหรับตรวจสอบ role
export function requireRole(allowedRoles: SiteRole[]) {
  return async (context: any) => {
    const { params, store } = context;
    const siteId = params.siteId;
    const userId = store.userId;

    if (!siteId || !userId) {
      throw new HttpError(401, 'ไม่ได้รับอนุญาต');
    }

    const hasPermission = await checkSitePermission(siteId, userId, allowedRoles);
    if (!hasPermission) {
      throw new HttpError(403, 'คุณไม่มีสิทธิ์เข้าถึงฟังก์ชันนี้');
    }

    return context;
  };
}

// Middleware สำหรับตรวจสอบ permission เฉพาะ
export function requirePermission(permission: string) {
  return async (context: any) => {
    const { params, store } = context;
    const siteId = params.siteId;
    const userId = store.userId;

    if (!siteId || !userId) {
      throw new HttpError(401, 'ไม่ได้รับอนุญาต');
    }

    const hasPermission = await checkPermission(siteId, userId, permission);
    if (!hasPermission) {
      throw new HttpError(403, 'คุณไม่มีสิทธิ์เข้าถึงฟังก์ชันนี้');
    }

    return context;
  };
}

// ตรวจสอบว่าสามารถจัดการ role ของผู้อื่นได้หรือไม่
export async function canManageRole(siteId: string, managerId: string, targetRole: SiteRole): Promise<boolean> {
  const managerRole = await getUserRole(siteId, managerId);
  if (!managerRole) return false;

  const managerLevel = ROLE_PERMISSIONS[managerRole as SiteRole].level;
  const targetLevel = ROLE_PERMISSIONS[targetRole].level;

  // สามารถจัดการ role ที่มีระดับต่ำกว่าตัวเองได้เท่านั้น
  return managerLevel > targetLevel;
}

// ตรวจสอบว่าสามารถเชิญคนเข้า role นี้ได้หรือไม่
export async function canInviteToRole(siteId: string, inviterId: string, targetRole: SiteRole): Promise<boolean> {
  const inviterRole = await getUserRole(siteId, inviterId);
  if (!inviterRole) return false;

  // เฉพาะ owner และ admin เท่านั้นที่เชิญได้
  if (!['owner', 'admin'].includes(inviterRole)) return false;

  // owner สามารถเชิญทุก role ได้
  if (inviterRole === 'owner') return true;

  // admin สามารถเชิญได้เฉพาะ editor และ viewer
  if (inviterRole === 'admin') {
    return ['editor', 'viewer'].includes(targetRole);
  }

  return false;
}

// ดึงรายการ permissions ของ user
export async function getUserPermissions(siteId: string, userId: string): Promise<string[]> {
  const userRole = await getUserRole(siteId, userId);
  if (!userRole) return [];

  return ROLE_PERMISSIONS[userRole as SiteRole].permissions;
}

// ดึงข้อมูล role และ permissions
export async function getUserRoleInfo(
  siteId: string,
  userId: string,
): Promise<{
  role: SiteRole | null;
  level: number;
  permissions: string[];
}> {
  const userRole = await getUserRole(siteId, userId);

  if (!userRole) {
    return {
      role: null,
      level: 0,
      permissions: [],
    };
  }

  const roleInfo = ROLE_PERMISSIONS[userRole as SiteRole];

  return {
    role: userRole,
    level: roleInfo.level,
    permissions: roleInfo.permissions,
  };
}

// ตรวจสอบว่าเป็น owner หรือไม่
export async function isOwner(siteId: string, userId: string): Promise<boolean> {
  const userRole = await getUserRole(siteId, userId);
  return userRole === 'owner';
}

// ตรวจสอบว่าเป็น admin ขึ้นไปหรือไม่
export async function isAdminOrAbove(siteId: string, userId: string): Promise<boolean> {
  const userRole = await getUserRole(siteId, userId);
  return userRole === 'owner' || userRole === 'admin';
}

// ตรวจสอบว่าสามารถแก้ไขเนื้อหาได้หรือไม่
export async function canEditContent(siteId: string, userId: string): Promise<boolean> {
  return await checkPermission(siteId, userId, 'content.edit');
}

// ตรวจสอบว่าสามารถจัดการสินค้าได้หรือไม่
export async function canManageProducts(siteId: string, userId: string): Promise<boolean> {
  return await checkPermission(siteId, userId, 'products.manage');
}

// ตรวจสอบว่าสามารถดูการวิเคราะห์ได้หรือไม่
export async function canViewAnalytics(siteId: string, userId: string): Promise<boolean> {
  return await checkPermission(siteId, userId, 'analytics.view');
}

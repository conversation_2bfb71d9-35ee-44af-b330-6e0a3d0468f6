import { SearchIndex, SearchQuery } from './search.model';

export class AdvancedSearchAnalytics {
  static async generateDashboardData(siteId: string, timeRange: { start: Date; end: Date; }): Promise<SearchDashboard> {
    try {
      const [searchVolume, topQueries, noResultsQueries, conversionData, performanceMetrics, userBehavior] =
        await Promise.all([
          this.getSearchVolume(siteId, timeRange),
          this.getTopQueries(siteId, timeRange),
          this.getNoResultsQueries(siteId, timeRange),
          this.getConversionData(siteId, timeRange),
          this.getPerformanceMetrics(siteId, timeRange),
          this.getUserBehaviorAnalytics(siteId, timeRange),
        ]);

      return {
        timeRange,
        searchVolume,
        topQueries,
        noResultsQueries,
        conversionData,
        performanceMetrics,
        userBehavior,
        insights: this.generateInsights({
          searchVolume,
          topQueries,
          noResultsQueries,
          conversionData,
        }),
      };
    }
    catch (error) {
      console.error('Error generating dashboard data:', error);
      throw error;
    }
  }

  private static async getSearchVolume(
    siteId: string,
    timeRange: { start: Date; end: Date; },
  ): Promise<SearchVolumeData> {
    const pipeline = [
      {
        $match: {
          siteId,
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
        },
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            hour: { $hour: '$createdAt' },
          },
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' },
          avgResults: { $avg: '$results.total' },
        },
      },
      {
        $group: {
          _id: '$_id.date',
          totalSearches: { $sum: '$count' },
          uniqueUsers: { $sum: { $size: '$uniqueUsers' } },
          avgResults: { $avg: '$avgResults' },
          hourlyData: {
            $push: {
              hour: '$_id.hour',
              searches: '$count',
            },
          },
        },
      },
      { $sort: { _id: 1 as const } },
    ];

    const dailyData = await SearchQuery.aggregate(pipeline);

    const totalSearches = dailyData.reduce((sum: number, day: any) => sum + day.totalSearches, 0);
    const totalUniqueUsers = new Set(dailyData.flatMap((day: any) => day.uniqueUsers)).size;

    return {
      totalSearches,
      totalUniqueUsers,
      avgSearchesPerUser: totalUniqueUsers > 0 ? totalSearches / totalUniqueUsers : 0,
      dailyData: dailyData.map(day => ({
        date: day._id,
        searches: day.totalSearches,
        uniqueUsers: day.uniqueUsers,
        avgResults: day.avgResults,
      })),
      hourlyDistribution: this.calculateHourlyDistribution(dailyData),
    };
  }

  private static async getTopQueries(siteId: string, timeRange: { start: Date; end: Date; }): Promise<TopQuery[]> {
    const pipeline = [
      {
        $match: {
          siteId,
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
        },
      },
      {
        $group: {
          _id: '$query',
          count: { $sum: 1 },
          avgResults: { $avg: '$results.total' },
          avgReturned: { $avg: '$results.returned' },
          uniqueUsers: { $addToSet: '$userId' },
          lastSearched: { $max: '$createdAt' },
        },
      },
      {
        $project: {
          query: '$_id',
          count: 1,
          avgResults: 1,
          avgReturned: 1,
          uniqueUsers: { $size: '$uniqueUsers' },
          lastSearched: 1,
          clickThroughRate: { $divide: ['$avgReturned', '$avgResults'] },
        },
      },
      { $sort: { count: -1 as -1 } },
      { $limit: 50 },
    ];

    return await SearchQuery.aggregate(pipeline);
  }

  private static async getNoResultsQueries(
    siteId: string,
    timeRange: { start: Date; end: Date; },
  ): Promise<NoResultsQuery[]> {
    const pipeline = [
      {
        $match: {
          siteId,
          'results.total': 0,
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
        },
      },
      {
        $group: {
          _id: '$query',
          count: { $sum: 1 },
          lastSearched: { $max: '$createdAt' },
          users: { $addToSet: '$userId' },
        },
      },
      {
        $project: {
          query: '$_id',
          count: 1,
          lastSearched: 1,
          uniqueUsers: { $size: '$users' },
        },
      },
      { $sort: { count: -1 as -1 } },
      { $limit: 20 },
    ];

    return await SearchQuery.aggregate(pipeline);
  }

  private static async getConversionData(
    _siteId: string,
    _timeRange: { start: Date; end: Date; },
  ): Promise<ConversionData> {
    // This would integrate with order/purchase data
    // For now, return mock data structure
    return {
      searchToView: 0.25,
      searchToPurchase: 0.05,
      avgOrderValue: 0,
      revenueFromSearch: 0,
      topConvertingQueries: [],
    };
  }

  private static async getPerformanceMetrics(
    siteId: string,
    timeRange: { start: Date; end: Date; },
  ): Promise<PerformanceMetrics> {
    const pipeline = [
      {
        $match: {
          siteId,
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
        },
      },
      {
        $group: {
          _id: null,
          avgResponseTime: { $avg: '$responseTime' }, // Would need to add this field
          totalQueries: { $sum: 1 },
          successfulQueries: {
            $sum: { $cond: [{ $gt: ['$results.total', 0] }, 1, 0] },
          },
          avgResultsPerQuery: { $avg: '$results.total' },
        },
      },
    ];

    const [metrics] = await SearchQuery.aggregate(pipeline);

    if (!metrics) {
      return {
        avgResponseTime: 0,
        successRate: 0,
        avgResultsPerQuery: 0,
        indexSize: 0,
        cacheHitRate: 0,
      };
    }

    const indexSize = await SearchIndex.countDocuments({ siteId });

    return {
      avgResponseTime: metrics.avgResponseTime || 0,
      successRate: metrics.totalQueries > 0 ? metrics.successfulQueries / metrics.totalQueries : 0,
      avgResultsPerQuery: metrics.avgResultsPerQuery || 0,
      indexSize,
      cacheHitRate: 0.75, // Would need to track this in cache layer
    };
  }

  private static async getUserBehaviorAnalytics(
    siteId: string,
    timeRange: { start: Date; end: Date; },
  ): Promise<UserBehaviorData> {
    const pipeline = [
      {
        $match: {
          siteId,
          userId: { $exists: true },
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
        },
      },
      {
        $group: {
          _id: '$userId',
          searchCount: { $sum: 1 },
          queries: { $addToSet: '$query' },
          avgResults: { $avg: '$results.total' },
          firstSearch: { $min: '$createdAt' },
          lastSearch: { $max: '$createdAt' },
        },
      },
      {
        $project: {
          userId: '$_id',
          searchCount: 1,
          uniqueQueries: { $size: '$queries' },
          avgResults: 1,
          sessionDuration: { $subtract: ['$lastSearch', '$firstSearch'] },
          queryDiversity: { $divide: [{ $size: '$queries' }, '$searchCount'] },
        },
      },
    ];

    const userData = await SearchQuery.aggregate(pipeline);

    return {
      totalUsers: userData.length,
      avgSearchesPerUser: userData.reduce((sum: number, user: any) => sum + user.searchCount, 0) / userData.length || 0,
      avgSessionDuration: userData.reduce((sum: number, user: any) => sum + user.sessionDuration, 0) / userData.length
        || 0,
      queryDiversityScore: userData.reduce((sum: number, user: any) => sum + user.queryDiversity, 0) / userData.length
        || 0,
      returningUserRate: 0, // Would need session tracking
      userSegments: this.analyzeUserSegments(userData),
    };
  }

  private static calculateHourlyDistribution(dailyData: any[]): HourlyDistribution[] {
    const hourlyTotals = new Array(24).fill(0);

    for (const day of dailyData) {
      for (const hourData of day.hourlyData) {
        hourlyTotals[hourData.hour] += hourData.searches;
      }
    }

    return hourlyTotals.map((total, hour) => ({
      hour,
      searches: total,
      percentage: (total / hourlyTotals.reduce((sum, val) => sum + val, 0)) * 100,
    }));
  }

  private static analyzeUserSegments(userData: any[]): UserSegment[] {
    const segments = [
      { name: 'Heavy Users', condition: (user: any) => user.searchCount >= 10, users: [] as any[] },
      {
        name: 'Regular Users',
        condition: (user: any) => user.searchCount >= 3 && user.searchCount < 10,
        users: [] as any[],
      },
      { name: 'Light Users', condition: (user: any) => user.searchCount < 3, users: [] as any[] },
    ];

    for (const user of userData) {
      for (const segment of segments) {
        if (segment.condition(user)) {
          segment.users.push(user);
          break;
        }
      }
    }

    return segments.map(segment => ({
      name: segment.name,
      count: segment.users.length,
      percentage: (segment.users.length / userData.length) * 100,
      avgSearches: segment.users.reduce((sum, user: any) => sum + user.searchCount, 0) / segment.users.length || 0,
    }));
  }

  private static generateInsights(data: any): SearchInsight[] {
    const insights: SearchInsight[] = [];

    // No results insight
    if (data.noResultsQueries.length > 0) {
      const noResultsRate = data.noResultsQueries.reduce((sum: number, q: any) => sum + q.count, 0)
        / data.searchVolume.totalSearches;

      if (noResultsRate > 0.1) {
        insights.push({
          type: 'warning',
          title: 'สัดส่วนการค้นหาที่ไม่มีผลลัพธ์สูง',
          description: `${(noResultsRate * 100).toFixed(1)}% ของการค้นหาไม่มีผลลัพธ์`,
          recommendation: 'ควรเพิ่มสินค้าหรือปรับปรุงระบบค้นหาสำหรับคำค้นหายอดนิยม',
          priority: 'high',
        });
      }
    }

    // Popular queries insight
    if (data.topQueries.length > 0) {
      const topQuery = data.topQueries[0];
      insights.push({
        type: 'info',
        title: 'คำค้นหายอดนิยม',
        description: `"${topQuery.query}" ถูกค้นหา ${topQuery.count} ครั้ง`,
        recommendation: 'ควรเพิ่มสินค้าที่เกี่ยวข้องกับคำค้นหานี้',
        priority: 'medium',
      });
    }

    // Search volume trend
    if (data.searchVolume.dailyData.length >= 7) {
      const recent = data.searchVolume.dailyData.slice(-3);
      const previous = data.searchVolume.dailyData.slice(-7, -3);

      const recentAvg = recent.reduce((sum: any, day: any) => sum + day.searches, 0) / recent.length;
      const previousAvg = previous.reduce((sum: any, day: any) => sum + day.searches, 0) / previous.length;

      const growth = ((recentAvg - previousAvg) / previousAvg) * 100;

      if (growth > 20) {
        insights.push({
          type: 'success',
          title: 'การค้นหาเพิ่มขึ้น',
          description: `การค้นหาเพิ่มขึ้น ${growth.toFixed(1)}% ในช่วง 3 วันที่ผ่านมา`,
          recommendation: 'ควรเตรียมสต็อกสินค้าให้เพียงพอ',
          priority: 'medium',
        });
      }
    }

    return insights;
  }

  static async getSearchFunnel(_siteId: string, _timeRange: { start: Date; end: Date; }): Promise<SearchFunnel> {
    // This would track the complete user journey from search to purchase
    return {
      searches: 1000,
      resultsViewed: 750,
      itemsClicked: 300,
      itemsAddedToCart: 150,
      purchases: 50,
      conversionRate: 0.05,
    };
  }

  static async getQueryPerformanceReport(
    siteId: string,
    query: string,
    timeRange: { start: Date; end: Date; },
  ): Promise<QueryPerformanceReport> {
    const pipeline = [
      {
        $match: {
          siteId,
          query,
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
        },
      },
      {
        $group: {
          _id: null,
          totalSearches: { $sum: 1 },
          avgResults: { $avg: '$results.total' },
          avgReturned: { $avg: '$results.returned' },
          uniqueUsers: { $addToSet: '$userId' },
          dailyTrend: {
            $push: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
              count: 1,
            },
          },
        },
      },
    ];

    const [data] = await SearchQuery.aggregate(pipeline);

    if (!data) {
      return {
        query,
        totalSearches: 0,
        uniqueUsers: 0,
        avgResults: 0,
        clickThroughRate: 0,
        dailyTrend: [],
        relatedQueries: [],
        topResults: [],
      };
    }

    // Get related queries
    const relatedQueries = await this.getRelatedQueries(siteId, query, timeRange);

    // Get top results for this query
    const topResults = await this.getTopResultsForQuery(siteId, query);

    return {
      query,
      totalSearches: data.totalSearches,
      uniqueUsers: data.uniqueUsers.length,
      avgResults: data.avgResults,
      clickThroughRate: data.avgResults > 0 ? data.avgReturned / data.avgResults : 0,
      dailyTrend: this.processDailyTrend(data.dailyTrend),
      relatedQueries,
      topResults,
    };
  }

  private static async getRelatedQueries(
    siteId: string,
    query: string,
    timeRange: { start: Date; end: Date; },
  ): Promise<string[]> {
    // Find queries that users searched for in the same session
    // This is a simplified version - would need session tracking
    const pipeline = [
      {
        $match: {
          siteId,
          query: { $ne: query },
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
        },
      },
      {
        $group: {
          _id: '$query',
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 as -1 } },
      { $limit: 5 },
    ];

    const related = await SearchQuery.aggregate(pipeline);
    return related.map(r => r._id);
  }

  private static async getTopResultsForQuery(siteId: string, query: string): Promise<any[]> {
    // Get the most commonly returned results for this query
    const searchResults = await SearchIndex.find({
      siteId,
      $text: { $search: query },
    })
      .sort({ score: { $meta: 'textScore' }, popularity: -1 })
      .limit(10)
      .lean();

    return searchResults.map(result => ({
      id: result._id,
      title: result.title,
      type: result.entityType,
      popularity: result.popularity,
    }));
  }

  private static processDailyTrend(dailyTrend: any[]): DailyTrendData[] {
    const trendMap = new Map<string, number>();

    for (const item of dailyTrend) {
      const count = trendMap.get(item.date) || 0;
      trendMap.set(item.date, count + 1);
    }

    return Array.from(trendMap.entries())
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }
}

// Type definitions
interface SearchDashboard {
  timeRange: { start: Date; end: Date; };
  searchVolume: SearchVolumeData;
  topQueries: TopQuery[];
  noResultsQueries: NoResultsQuery[];
  conversionData: ConversionData;
  performanceMetrics: PerformanceMetrics;
  userBehavior: UserBehaviorData;
  insights: SearchInsight[];
}

interface SearchVolumeData {
  totalSearches: number;
  totalUniqueUsers: number;
  avgSearchesPerUser: number;
  dailyData: DailySearchData[];
  hourlyDistribution: HourlyDistribution[];
}

interface DailySearchData {
  date: string;
  searches: number;
  uniqueUsers: number;
  avgResults: number;
}

interface HourlyDistribution {
  hour: number;
  searches: number;
  percentage: number;
}

interface TopQuery {
  query: string;
  count: number;
  avgResults: number;
  avgReturned: number;
  uniqueUsers: number;
  lastSearched: Date;
  clickThroughRate: number;
}

interface NoResultsQuery {
  query: string;
  count: number;
  lastSearched: Date;
  uniqueUsers: number;
}

interface ConversionData {
  searchToView: number;
  searchToPurchase: number;
  avgOrderValue: number;
  revenueFromSearch: number;
  topConvertingQueries: any[];
}

interface PerformanceMetrics {
  avgResponseTime: number;
  successRate: number;
  avgResultsPerQuery: number;
  indexSize: number;
  cacheHitRate: number;
}

interface UserBehaviorData {
  totalUsers: number;
  avgSearchesPerUser: number;
  avgSessionDuration: number;
  queryDiversityScore: number;
  returningUserRate: number;
  userSegments: UserSegment[];
}

interface UserSegment {
  name: string;
  count: number;
  percentage: number;
  avgSearches: number;
}

interface SearchInsight {
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  description: string;
  recommendation: string;
  priority: 'low' | 'medium' | 'high';
}

interface SearchFunnel {
  searches: number;
  resultsViewed: number;
  itemsClicked: number;
  itemsAddedToCart: number;
  purchases: number;
  conversionRate: number;
}

interface QueryPerformanceReport {
  query: string;
  totalSearches: number;
  uniqueUsers: number;
  avgResults: number;
  clickThroughRate: number;
  dailyTrend: DailyTrendData[];
  relatedQueries: string[];
  topResults: any[];
}

interface DailyTrendData {
  date: string;
  count: number;
}

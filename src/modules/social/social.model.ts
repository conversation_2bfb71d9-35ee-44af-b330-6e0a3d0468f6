import { generateId } from '@/core/utils';
import mongoose, { type Document, Schema } from 'mongoose';

export interface ISocialPost extends Document {
  _id: string;
  siteId: string;
  userId: string;
  userType: 'user' | 'customer' | 'admin';
  type: 'product' | 'review' | 'lifestyle' | 'promotion' | 'story';
  content: string;
  images: Array<{
    url: string;
    caption?: string;
    order: number;
  }>;
  products: Array<{
    productId: string;
    position: { x: number; y: number; };
    tagText?: string;
  }>;
  hashtags: string[];
  mentions: string[];
  location?: {
    name: string;
    coordinates: [number, number];
  };
  engagement: {
    likes: number;
    comments: number;
    shares: number;
    saves: number;
    clicks: number;
  };
  visibility: 'public' | 'followers' | 'private';
  status: 'draft' | 'published' | 'archived' | 'reported';
  scheduledAt?: Date;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISocialComment extends Document {
  _id: string;
  siteId: string;
  postId: string;
  userId: string;
  userType: 'user' | 'customer' | 'admin';
  content: string;
  parentCommentId?: string;
  likes: number;
  status: 'active' | 'hidden' | 'deleted';
  createdAt: Date;
  updatedAt: Date;
}

export interface ISocialShare extends Document {
  _id: string;
  siteId: string;
  postId: string;
  userId: string;
  userType: 'user' | 'customer' | 'admin';
  platform: 'facebook' | 'twitter' | 'instagram' | 'line' | 'copy_link';
  shareUrl?: string;
  shareId?: string; // External platform ID
  status: 'pending' | 'success' | 'failed';
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISocialInfluencer extends Document {
  _id: string;
  siteId: string;
  userId: string;
  name: string;
  username: string;
  bio?: string;
  avatar?: string;
  socialLinks: {
    instagram?: string;
    facebook?: string;
    youtube?: string;
    tiktok?: string;
  };
  stats: {
    followers: number;
    posts: number;
    engagement: number;
    reach: number;
  };
  categories: string[];
  commission: {
    rate: number;
    minOrder: number;
    maxOrder: number;
  };
  status: 'active' | 'inactive' | 'pending';
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISocialCampaign extends Document {
  _id: string;
  siteId: string;
  name: string;
  description: string;
  type: 'product_launch' | 'seasonal' | 'influencer' | 'user_generated';
  hashtag: string;
  products: string[];
  influencers: string[];
  budget: {
    total: number;
    spent: number;
    currency: string;
  };
  goals: {
    reach: number;
    engagement: number;
    sales: number;
    conversions: number;
  };
  metrics: {
    reach: number;
    engagement: number;
    sales: number;
    conversions: number;
    posts: number;
    participants: number;
  };
  startDate: Date;
  endDate: Date;
  status: 'draft' | 'active' | 'paused' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

const SocialPostSchema = new Schema<ISocialPost>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    userType: {
      type: String,
      required: true,
      enum: ['user', 'customer', 'admin'],
    },
    type: {
      type: String,
      required: true,
      enum: ['product', 'review', 'lifestyle', 'promotion', 'story'],
    },
    content: { type: String, required: true },
    images: [
      {
        url: { type: String, required: true },
        caption: { type: String },
        order: { type: Number, default: 0 },
      },
    ],
    products: [
      {
        productId: { type: String, required: true },
        position: {
          x: { type: Number, required: true },
          y: { type: Number, required: true },
        },
        tagText: { type: String },
      },
    ],
    hashtags: [{ type: String }],
    mentions: [{ type: String }],
    location: {
      name: { type: String },
      coordinates: [{ type: Number }],
    },
    engagement: {
      likes: { type: Number, default: 0 },
      comments: { type: Number, default: 0 },
      shares: { type: Number, default: 0 },
      saves: { type: Number, default: 0 },
      clicks: { type: Number, default: 0 },
    },
    visibility: {
      type: String,
      enum: ['public', 'followers', 'private'],
      default: 'public',
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'archived', 'reported'],
      default: 'published',
    },
    scheduledAt: { type: Date },
    publishedAt: { type: Date },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const SocialCommentSchema = new Schema<ISocialComment>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    postId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    userType: {
      type: String,
      required: true,
      enum: ['user', 'customer', 'admin'],
    },
    content: { type: String, required: true },
    parentCommentId: { type: String, index: true },
    likes: { type: Number, default: 0 },
    status: {
      type: String,
      enum: ['active', 'hidden', 'deleted'],
      default: 'active',
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const SocialShareSchema = new Schema<ISocialShare>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    postId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    userType: {
      type: String,
      required: true,
      enum: ['user', 'customer', 'admin'],
    },
    platform: {
      type: String,
      required: true,
      enum: ['facebook', 'twitter', 'instagram', 'line', 'copy_link'],
    },
    shareUrl: { type: String },
    shareId: { type: String },
    status: {
      type: String,
      enum: ['pending', 'success', 'failed'],
      default: 'pending',
    },
    metadata: { type: Schema.Types.Mixed },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const SocialInfluencerSchema = new Schema<ISocialInfluencer>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    username: { type: String, required: true, unique: true },
    bio: { type: String },
    avatar: { type: String },
    socialLinks: {
      instagram: { type: String },
      facebook: { type: String },
      youtube: { type: String },
      tiktok: { type: String },
    },
    stats: {
      followers: { type: Number, default: 0 },
      posts: { type: Number, default: 0 },
      engagement: { type: Number, default: 0 },
      reach: { type: Number, default: 0 },
    },
    categories: [{ type: String }],
    commission: {
      rate: { type: Number, default: 10 },
      minOrder: { type: Number, default: 0 },
      maxOrder: { type: Number, default: 0 },
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'pending'],
      default: 'pending',
    },
    isVerified: { type: Boolean, default: false },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const SocialCampaignSchema = new Schema<ISocialCampaign>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: ['product_launch', 'seasonal', 'influencer', 'user_generated'],
    },
    hashtag: { type: String, required: true },
    products: [{ type: String }],
    influencers: [{ type: String }],
    budget: {
      total: { type: Number, required: true },
      spent: { type: Number, default: 0 },
      currency: { type: String, default: 'THB' },
    },
    goals: {
      reach: { type: Number, default: 0 },
      engagement: { type: Number, default: 0 },
      sales: { type: Number, default: 0 },
      conversions: { type: Number, default: 0 },
    },
    metrics: {
      reach: { type: Number, default: 0 },
      engagement: { type: Number, default: 0 },
      sales: { type: Number, default: 0 },
      conversions: { type: Number, default: 0 },
      posts: { type: Number, default: 0 },
      participants: { type: Number, default: 0 },
    },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    status: {
      type: String,
      enum: ['draft', 'active', 'paused', 'completed'],
      default: 'draft',
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
SocialPostSchema.index({ siteId: 1, userId: 1 });
SocialPostSchema.index({ siteId: 1, type: 1 });
SocialPostSchema.index({ siteId: 1, status: 1 });
SocialPostSchema.index({ siteId: 1, hashtags: 1 });
SocialPostSchema.index({ createdAt: -1 });

SocialCommentSchema.index({ siteId: 1, postId: 1 });
SocialCommentSchema.index({ siteId: 1, userId: 1 });
SocialCommentSchema.index({ siteId: 1, status: 1 });

SocialShareSchema.index({ siteId: 1, postId: 1 });
SocialShareSchema.index({ siteId: 1, platform: 1 });
SocialShareSchema.index({ siteId: 1, status: 1 });

SocialInfluencerSchema.index({ siteId: 1, status: 1 });
SocialInfluencerSchema.index({ siteId: 1, categories: 1 });
SocialInfluencerSchema.index({ 'stats.followers': -1 });

SocialCampaignSchema.index({ siteId: 1, status: 1 });
SocialCampaignSchema.index({ siteId: 1, type: 1 });
SocialCampaignSchema.index({ startDate: 1, endDate: 1 });

// Static methods
SocialPostSchema.statics.findByUser = async function(siteId: string, userId: string) {
  return this.find({ siteId, userId, status: 'published' }).sort({ createdAt: -1 });
};

SocialPostSchema.statics.findByHashtag = async function(siteId: string, hashtag: string) {
  return this.find({ siteId, hashtags: hashtag, status: 'published' }).sort({ createdAt: -1 });
};

SocialPostSchema.statics.findTrending = async function(siteId: string, limit: number = 10) {
  return this.find({ siteId, status: 'published' })
    .sort({ 'engagement.likes': -1, 'engagement.comments': -1 })
    .limit(limit);
};

SocialCommentSchema.statics.findByPost = async function(siteId: string, postId: string) {
  return this.find({ siteId, postId, status: 'active' }).sort({ createdAt: 1 });
};

SocialInfluencerSchema.statics.findActive = async function(siteId: string) {
  return this.find({ siteId, status: 'active' }).sort({ 'stats.followers': -1 });
};

SocialCampaignSchema.statics.findActive = async function(siteId: string) {
  const now = new Date();
  return this.find({
    siteId,
    status: 'active',
    startDate: { $lte: now },
    endDate: { $gte: now },
  });
};

export const SocialPost = mongoose.model<ISocialPost>('SocialPost', SocialPostSchema);
export const SocialComment = mongoose.model<ISocialComment>('SocialComment', SocialCommentSchema);
export const SocialShare = mongoose.model<ISocialShare>('SocialShare', SocialShareSchema);
export const SocialInfluencer = mongoose.model<ISocialInfluencer>('SocialInfluencer', SocialInfluencerSchema);
export const SocialCampaign = mongoose.model<ISocialCampaign>('SocialCampaign', SocialCampaignSchema);

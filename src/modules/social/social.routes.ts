import { userPlugin } from '@/core/middleware/checkUser';
import { Elysia, t } from 'elysia';
import * as socialService from './social.service';

export const socialRoutes = new Elysia({ prefix: '/social' })
  .use(userPlugin)
  .get(
    '/posts',
    async ({ query, store }: any) => {
      const { userId, type, page = 1, limit = 20 } = query;
      const result = await socialService.getSocialPosts(store.siteId, userId, type, Number(page), Number(limit));
      return { success: true, data: result };
    },
    {
      query: t.Object({
        userId: t.Optional(t.String()),
        type: t.Optional(t.String()),
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
      }),
    },
  )
  .post(
    '/posts',
    async ({ body, store }: any) => {
      const post = await socialService.createSocialPost(store.siteId, { ...body, userId: store.userId });
      return { success: true, data: post };
    },
    {
      body: t.Object({
        type: t.Union([
          t.Literal('product'),
          t.Literal('review'),
          t.Literal('lifestyle'),
          t.Literal('promotion'),
          t.Literal('story'),
        ]),
        title: t.String(),
        content: t.String(),
        images: t.Optional(
          t.Array(
            t.Object({
              url: t.String(),
              caption: t.Optional(t.String()),
              order: t.Number(),
            }),
          ),
        ),
        products: t.Optional(
          t.Array(
            t.Object({
              productId: t.String(),
              position: t.Number(),
            }),
          ),
        ),
        hashtags: t.Array(t.String()),
        location: t.Optional(t.String()),
        isSponsored: t.Optional(t.Boolean()),
        scheduledAt: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/posts/:postId',
    async ({ params, store }: any) => {
      const { postId } = params;
      const post = await socialService.getSocialPost(store.siteId, postId);
      return { success: true, data: post };
    },
    {
      params: t.Object({ postId: t.String() }),
    },
  )
  .put(
    '/posts/:postId',
    async ({ params, body, store }: any) => {
      const { postId } = params;
      const post = await socialService.updateSocialPost(store.siteId, postId, store.userId, body);
      return { success: true, data: post };
    },
    {
      params: t.Object({ postId: t.String() }),
      body: t.Object({
        title: t.Optional(t.String()),
        content: t.Optional(t.String()),
        images: t.Optional(
          t.Array(
            t.Object({
              url: t.String(),
              caption: t.Optional(t.String()),
              order: t.Number(),
            }),
          ),
        ),
        products: t.Optional(
          t.Array(
            t.Object({
              productId: t.String(),
              position: t.Number(),
            }),
          ),
        ),
        hashtags: t.Optional(t.Array(t.String())),
        location: t.Optional(t.String()),
        isSponsored: t.Optional(t.Boolean()),
        scheduledAt: t.Optional(t.String()),
      }),
    },
  )
  .delete(
    '/posts/:postId',
    async ({ params, store }: any) => {
      const { postId } = params;
      const result = await socialService.deleteSocialPost(store.siteId, postId, store.userId);
      return { success: true, data: result };
    },
    {
      params: t.Object({ postId: t.String() }),
    },
  )
  .post(
    '/posts/:postId/like',
    async ({ params, store }: any) => {
      const { postId } = params;
      const post = await socialService.likeSocialPost(store.siteId, postId);
      return { success: true, data: post };
    },
    {
      params: t.Object({ postId: t.String() }),
    },
  )
  .post(
    '/posts/:postId/share',
    async ({ params, body, store }: any) => {
      const { postId } = params;
      const share = await socialService.shareSocialPost(store.siteId, { ...body, postId, userId: store.userId });
      return { success: true, data: share };
    },
    {
      params: t.Object({ postId: t.String() }),
      body: t.Object({
        platform: t.Union([
          t.Literal('facebook'),
          t.Literal('twitter'),
          t.Literal('instagram'),
          t.Literal('linkedin'),
          t.Literal('pinterest'),
          t.Literal('whatsapp'),
          t.Literal('telegram'),
        ]),
        url: t.Optional(t.String()),
        message: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/posts/:postId/comments',
    async ({ params, store }: any) => {
      const { postId } = params;
      const comments = await socialService.getSocialComments(store.siteId, postId);
      return { success: true, data: comments };
    },
    {
      params: t.Object({ postId: t.String() }),
    },
  )
  .post(
    '/posts/:postId/comments',
    async ({ params, body, store }: any) => {
      const { postId } = params;
      const comment = await socialService.createSocialComment(store.siteId, { ...body, postId, userId: store.userId });
      return { success: true, data: comment };
    },
    {
      params: t.Object({ postId: t.String() }),
      body: t.Object({
        content: t.String(),
        parentCommentId: t.Optional(t.String()),
      }),
    },
  )
  .post(
    '/comments/:commentId/like',
    async ({ params, store }: any) => {
      const { commentId } = params;
      const comment = await socialService.likeSocialComment(store.siteId, commentId);
      return { success: true, data: comment };
    },
    {
      params: t.Object({ commentId: t.String() }),
    },
  )
  .get(
    '/trending',
    async ({ query, store }: any) => {
      const { limit = 10 } = query;
      const posts = await socialService.getTrendingPosts(store.siteId, Number(limit));
      return { success: true, data: posts };
    },
    {
      query: t.Object({ limit: t.Optional(t.String()) }),
    },
  )
  .get(
    '/hashtag/:hashtag',
    async ({ params, store }: any) => {
      const { hashtag } = params;
      const posts = await socialService.getPostsByHashtag(store.siteId, hashtag);
      return { success: true, data: posts };
    },
    {
      params: t.Object({ hashtag: t.String() }),
    },
  )
  .post(
    '/influencer',
    async ({ body, store }: any) => {
      const influencer = await socialService.createSocialInfluencer(store.siteId, body);
      return { success: true, data: influencer };
    },
    {
      body: t.Object({
        name: t.String(),
        username: t.String(),
        bio: t.Optional(t.String()),
        avatar: t.Optional(t.String()),
        platforms: t.Array(
          t.Object({
            name: t.String(),
            username: t.String(),
            followers: t.Number(),
            engagement: t.Number(),
          }),
        ),
        categories: t.Array(t.String()),
        contactInfo: t.Object({
          email: t.Optional(t.String()),
          phone: t.Optional(t.String()),
          website: t.Optional(t.String()),
        }),
        rates: t.Object({
          post: t.Number(),
          story: t.Number(),
          video: t.Number(),
          collaboration: t.Number(),
        }),
      }),
    },
  )
  .get('/influencer', async ({ store }: any) => {
    const influencers = await socialService.getSocialInfluencers(store.siteId);
    return { success: true, data: influencers };
  })
  .put(
    '/influencer/:influencerId',
    async ({ params, body, store }: any) => {
      const { influencerId } = params;
      const influencer = await socialService.updateSocialInfluencer(store.siteId, influencerId, body);
      return { success: true, data: influencer };
    },
    {
      params: t.Object({ influencerId: t.String() }),
      body: t.Object({
        name: t.Optional(t.String()),
        username: t.Optional(t.String()),
        bio: t.Optional(t.String()),
        avatar: t.Optional(t.String()),
        platforms: t.Optional(
          t.Array(
            t.Object({
              name: t.String(),
              username: t.String(),
              followers: t.Number(),
              engagement: t.Number(),
            }),
          ),
        ),
        categories: t.Optional(t.Array(t.String())),
        contactInfo: t.Optional(
          t.Object({
            email: t.Optional(t.String()),
            phone: t.Optional(t.String()),
            website: t.Optional(t.String()),
          }),
        ),
        rates: t.Optional(
          t.Object({
            post: t.Optional(t.Number()),
            story: t.Optional(t.Number()),
            video: t.Optional(t.Number()),
            collaboration: t.Optional(t.Number()),
          }),
        ),
        isActive: t.Optional(t.Boolean()),
        isVerified: t.Optional(t.Boolean()),
      }),
    },
  )
  .delete(
    '/influencer/:influencerId',
    async ({ params, store }: any) => {
      const { influencerId } = params;
      const result = await socialService.deleteSocialInfluencer(store.siteId, influencerId);
      return { success: true, data: result };
    },
    {
      params: t.Object({ influencerId: t.String() }),
    },
  )
  .post(
    '/campaign',
    async ({ body, store }: any) => {
      const campaign = await socialService.createSocialCampaign(store.siteId, body);
      return { success: true, data: campaign };
    },
    {
      body: t.Object({
        name: t.String(),
        description: t.String(),
        objective: t.Union([
          t.Literal('awareness'),
          t.Literal('engagement'),
          t.Literal('traffic'),
          t.Literal('conversions'),
          t.Literal('sales'),
        ]),
        platforms: t.Array(t.String()),
        influencers: t.Array(t.String()),
        budget: t.Number(),
        startDate: t.String(),
        endDate: t.String(),
        hashtags: t.Array(t.String()),
        requirements: t.Object({
          postCount: t.Number(),
          storyCount: t.Number(),
          videoCount: t.Number(),
          mentions: t.Array(t.String()),
          callToAction: t.String(),
        }),
      }),
    },
  )
  .get('/campaign', async ({ store }: any) => {
    const campaigns = await socialService.getSocialCampaigns(store.siteId);
    return { success: true, data: campaigns };
  })
  .get('/campaign/active', async ({ store }: any) => {
    const campaigns = await socialService.getActiveSocialCampaigns(store.siteId);
    return { success: true, data: campaigns };
  })
  .put(
    '/campaign/:campaignId',
    async ({ params, body, store }: any) => {
      const { campaignId } = params;
      const campaign = await socialService.updateSocialCampaign(store.siteId, campaignId, body);
      return { success: true, data: campaign };
    },
    {
      params: t.Object({ campaignId: t.String() }),
      body: t.Object({
        name: t.Optional(t.String()),
        description: t.Optional(t.String()),
        objective: t.Optional(t.String()),
        platforms: t.Optional(t.Array(t.String())),
        influencers: t.Optional(t.Array(t.String())),
        budget: t.Optional(t.Number()),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
        hashtags: t.Optional(t.Array(t.String())),
        requirements: t.Optional(
          t.Object({
            postCount: t.Optional(t.Number()),
            storyCount: t.Optional(t.Number()),
            videoCount: t.Optional(t.Number()),
            mentions: t.Optional(t.Array(t.String())),
            callToAction: t.Optional(t.String()),
          }),
        ),
        status: t.Optional(t.String()),
      }),
    },
  )
  .delete(
    '/campaign/:campaignId',
    async ({ params, store }: any) => {
      const { campaignId } = params;
      const result = await socialService.deleteSocialCampaign(store.siteId, campaignId);
      return { success: true, data: result };
    },
    {
      params: t.Object({ campaignId: t.String() }),
    },
  )
  .get(
    '/stats',
    async ({ query, store }: any) => {
      const { startDate, endDate } = query;
      const timeRange = { start: new Date(startDate), end: new Date(endDate) };
      const stats = await socialService.getSocialStats(store.siteId, timeRange);
      return { success: true, data: stats };
    },
    {
      query: t.Object({ startDate: t.String(), endDate: t.String() }),
    },
  )
  .get(
    '/influencer/:influencerId/stats',
    async ({ params, store }: any) => {
      const { influencerId } = params;
      const stats = await socialService.getInfluencerStats(store.siteId, influencerId);
      return { success: true, data: stats };
    },
    {
      params: t.Object({ influencerId: t.String() }),
    },
  );

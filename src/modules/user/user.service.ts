import { createResetPasswordEmail, createVerificationEmail, sendEmail } from '@/core/config/email';
import { config } from '@/core/config/environment';
import { loginAttemptService } from '@/core/services/login-attempt.service';
import { blacklistTokenOnLogout } from '@/core/services/token-blacklist.service';
import { cloudinaryUpload } from '@/core/utils/cloudinary-upload';
import { HttpError } from '@/core/utils/error';
import { generateFileId } from '@/core/utils/idGenerator';
import * as jose from 'jose';
import { User } from './user.model';

const defaultDeps = {
  User: User,
  sendEmail,
  createVerificationEmail,
  createResetPasswordEmail,
};

export type SignupUserInput = { email: string; password: string; };
export type SigninUserInput = { email: string; password: string; };

export async function signupUser({ email, password }: SignupUserInput, deps = defaultDeps) {
  const exist = await deps.User.findOne({ email });
  if (exist) throw new HttpError(409, 'อีเมลนี้ถูกใช้งานแล้ว');
  const user = await deps.User.create({
    _id: generateFileId(5),
    email,
    password,
    isEmailVerified: false,
  });

  // สร้าง access token (อายุสั้น)
  const token = await new jose.SignJWT({ userId: user._id, email })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('24h')
    .sign(new TextEncoder().encode(config.jwtSecret));

  // สร้าง refresh token (อายุยาว)
  const refreshToken = await new jose.SignJWT({ userId: user._id, email })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime(config.refreshTokenExpiresIn)
    .sign(new TextEncoder().encode(config.refreshTokenSecret));

  // บันทึก refresh token ลง database
  user.refreshToken = refreshToken;
  await user.save();

  // สร้าง user object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };

  const link = `${config.frontendUrl}/verify-email?token=${token}`;
  await deps.sendEmail(email, 'ยืนยันอีเมล', deps.createVerificationEmail(link));
  return { user: safeUser, token, refreshToken };
}

export async function signinUser(
  { email, password }: SigninUserInput,
  ip: string = 'unknown',
  userAgent: string = 'unknown',
  deps = defaultDeps,
) {
  // ✅ ตรวจสอบ account lockout ก่อน
  // const lockStatus = await loginAttemptService.isAccountLocked(email);
  // if (lockStatus.isLocked) {
  //   await loginAttemptService.recordAttempt(email, ip, userAgent, false, 'account_locked');
  //   throw new HttpError(423, `บัญชีถูกล็อค กรุณาลองใหม่อีกครั้งในเวลา ${lockStatus.lockedUntil?.toLocaleString('th-TH')}`);
  // }

  const user = await deps.User.findOne({ email });
  if (!user) {
    await loginAttemptService.recordAttempt(email, ip, userAgent, false, 'user_not_found');
    throw new HttpError(404, 'ไม่พบผู้ใช้');
  }

  const valid = await user.comparePassword(password);
  if (!valid) {
    await loginAttemptService.recordAttempt(email, ip, userAgent, false, 'invalid_password');
    throw new HttpError(401, 'รหัสผ่านไม่ถูกต้อง');
  }

  if (!user.isEmailVerified) {
    await loginAttemptService.recordAttempt(email, ip, userAgent, false, 'email_not_verified');
    throw new HttpError(401, 'กรุณายืนยันอีเมลก่อนเข้าสู่ระบบ');
  }

  // สร้าง access token (อายุสั้น)
  const token = await new jose.SignJWT({ userId: user._id, email })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime(config.jwtExpiresIn)
    .sign(new TextEncoder().encode(config.jwtSecret));

  // ✅ ใช้ Token Rotation System แทน JWT refresh token
  console.log('[signinUser] Using Token Rotation System for refresh token');
  const { generateRefreshToken, storeRefreshToken, createDeviceFingerprint } = await import(
    '@/core/utils/token-rotation'
  );

  // สร้าง device fingerprint
  const deviceFingerprint = createDeviceFingerprint(userAgent, ip);

  // สร้าง refresh token ด้วย Token Rotation System
  const { token: refreshToken, tokenId } = generateRefreshToken();

  // เก็บ refresh token ใน Token Rotation System (Database)
  await storeRefreshToken(refreshToken, tokenId, user._id.toString(), deviceFingerprint, userAgent, ip);

  console.log('[signinUser] Token Rotation System refresh token created:', {
    tokenLength: refreshToken.length,
    tokenId: tokenId,
    deviceFingerprint: deviceFingerprint.substring(0, 16) + '...',
  });

  // ✅ บันทึก successful login attempt
  await loginAttemptService.recordAttempt(email, ip, userAgent, true);

  // สร้าง user object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };

  return { user: safeUser, token, refreshToken };
}

export async function signoutUser(userId: string, token?: string, deps = defaultDeps) {
  const user = await deps.User.findByIdAndUpdate(userId, { $unset: { refreshToken: 1 } }, { new: true });
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  // ✅ Blacklist current token if provided
  if (token) {
    await blacklistTokenOnLogout(token, userId);
  }

  // ✅ Revoke all token rotation tokens for this user
  try {
    const { revokeAllUserTokens } = await import('@/core/utils/token-rotation');
    await revokeAllUserTokens(userId);
    console.log('[signoutUser] Revoked all token rotation tokens for user:', userId);
  }
  catch (error) {
    console.error('[signoutUser] Failed to revoke token rotation tokens:', error);
  }

  // สร้าง safeUser
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
  return { safeUser };
}

export async function verifyEmail(token: string, deps = defaultDeps) {
  const { payload } = await jose.jwtVerify(token, new TextEncoder().encode(config.jwtSecret));
  const user = await deps.User.findById(payload.userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');
  user.isEmailVerified = true;
  await user.save();
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
  return { safeUser };
}

export async function resendVerificationEmail(email: string, deps = defaultDeps) {
  const user = await deps.User.findOne({ email });
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  // ถ้ายืนยันอีเมลแล้ว ให้ส่งข้อความแจ้งเตือน
  if (user.isEmailVerified) {
    return {
      success: true,
      message: 'อีเมลนี้ได้รับการยืนยันแล้ว',
    };
  }

  // สร้าง verification token ใหม่
  const token = await new jose.SignJWT({ userId: user._id, email })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('24h')
    .sign(new TextEncoder().encode(config.jwtSecret));

  // สร้าง verification link
  const link = `${config.frontendUrl}/verify-email?token=${token}`;

  // ส่งอีเมลยืนยัน
  await deps.sendEmail(email, 'ยืนยันอีเมล', deps.createVerificationEmail(link));

  return {
    success: true,
    message: 'ส่งอีเมลยืนยันใหม่สำเร็จ กรุณาตรวจสอบอีเมลของคุณ',
  };
}

export async function forgotPassword(email: string, deps = defaultDeps) {
  const user = await deps.User.findOne({ email });
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');
  const token = await new jose.SignJWT({ userId: user._id, email })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('1h')
    .sign(new TextEncoder().encode(config.jwtSecret));
  user.resetPasswordToken = token;
  await user.save();
  const link = `${config.frontendUrl}/reset-password?token=${token}`;
  await deps.sendEmail(email, 'รีเซ็ตรหัสผ่าน', deps.createResetPasswordEmail(link));
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
  return { safeUser };
}

export async function resetPassword(token: string, newPassword: string, deps = defaultDeps) {
  const { payload } = await jose.jwtVerify(token, new TextEncoder().encode(config.jwtSecret));
  const user = await deps.User.findById(payload.userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');
  if (user.resetPasswordToken !== token) throw new HttpError(401, 'โทเค็นไม่ถูกต้อง');
  user.password = newPassword;
  user.resetPasswordToken = undefined;
  await user.save();
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
  return { safeUser };
}

export async function getUserProfile(userId: string, deps = defaultDeps) {
  if (!userId) throw new HttpError(404, 'ไม่พบ userId');

  const user = await deps.User.findById(userId).select('-password -refreshToken -resetPasswordToken');
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  // สร้าง user object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };

  return { safeUser };
}

export async function updateUserProfile(
  userId: string,
  userData: { firstName?: string; lastName?: string; },
  deps = defaultDeps,
) {
  if (!userId) throw new HttpError(404, 'ไม่พบ userId');

  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  // อัปเดตข้อมูล
  if (userData.firstName !== undefined) {
    user.firstName = userData.firstName;
  }
  if (userData.lastName !== undefined) {
    user.lastName = userData.lastName;
  }

  await user.save();

  // สร้าง user object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };

  return { safeUser };
}

export async function changePassword(userId: string, currentPassword: string, newPassword: string, deps = defaultDeps) {
  if (!userId) throw new HttpError(404, 'ไม่พบ userId');
  if (!currentPassword || !newPassword) throw new HttpError(400, 'กรุณากรอกรหัสผ่านเก่าและรหัสผ่านใหม่');
  if (newPassword.length < 6) throw new HttpError(400, 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร');

  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  // ตรวจสอบรหัสผ่านเก่า
  const isValidPassword = await user.comparePassword(currentPassword);
  if (!isValidPassword) throw new HttpError(401, 'รหัสผ่านเก่าไม่ถูกต้อง');

  // อัปเดตรหัสผ่านใหม่
  user.password = newPassword;
  await user.save();

  // Return ข้อมูลดิบ - ให้ route จัดการ response format
  return {
    userId: user._id,
    email: user.email,
    updatedAt: new Date(),
  };
}

export async function getUserById(userId: string, deps = defaultDeps) {
  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
  return { safeUser };
}

export async function refreshNewToken(
  refreshToken: string,
  userAgent: string = 'unknown',
  ipAddress: string = 'unknown',
  deps = defaultDeps,
) {
  console.log('[refreshNewToken] Starting Token Rotation System refresh');
  console.log('[refreshNewToken] Refresh token received:', refreshToken);
  console.log('[refreshNewToken] Refresh token type:', typeof refreshToken);
  console.log('[refreshNewToken] Refresh token length:', refreshToken?.length);

  // Import token rotation functions
  const { validateRefreshToken, rotateRefreshToken, createDeviceFingerprint } = await import(
    '@/core/utils/token-rotation'
  );

  // ตรวจสอบ refresh token format
  if (!refreshToken) {
    console.error('[refreshNewToken] Refresh token is null or undefined');
    throw new HttpError(400, 'Refresh token ไม่ถูกต้อง: token เป็น null');
  }

  if (typeof refreshToken !== 'string') {
    console.error('[refreshNewToken] Refresh token is not a string:', typeof refreshToken);
    throw new HttpError(400, 'Refresh token ไม่ถูกต้อง: ต้องเป็น string');
  }

  // ตรวจสอบ token rotation format (hex string ความยาว 128)
  if (refreshToken.length !== 128) {
    console.error('[refreshNewToken] Invalid token rotation format - length:', refreshToken.length);
    throw new HttpError(400, 'Refresh token ไม่ถูกต้อง: รูปแบบ Token Rotation ไม่ถูกต้อง');
  }

  // สร้าง device fingerprint
  const deviceFingerprint = createDeviceFingerprint(userAgent, ipAddress);
  console.log('[refreshNewToken] Device fingerprint created');

  // ตรวจสอบ refresh token ด้วย Token Rotation System (Database)
  console.log('[refreshNewToken] Validating token with Token Rotation System');
  const validation = await validateRefreshToken(refreshToken, deviceFingerprint, ipAddress, userAgent);

  if (!validation.valid || !validation.record) {
    console.error('[refreshNewToken] Token validation failed');
    throw new HttpError(401, 'Refresh token ไม่ถูกต้องหรือหมดอายุ');
  }

  console.log('[refreshNewToken] Token validated successfully');
  const { record, shouldRotate } = validation;

  // ดึงข้อมูล user จาก database
  console.log('[refreshNewToken] Finding user in database');
  const user = await deps.User.findById(record.userId);
  if (!user) {
    console.error('[refreshNewToken] User not found for userId:', record.userId);
    throw new HttpError(404, 'ไม่พบผู้ใช้');
  }
  console.log('[refreshNewToken] User found:', user.email);

  // สร้าง access token ใหม่
  console.log('[refreshNewToken] Creating new access token');
  const newToken = await new jose.SignJWT({ userId: user._id, email: user.email })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime(config.jwtExpiresIn)
    .sign(new TextEncoder().encode(config.jwtSecret));

  // จัดการ refresh token rotation
  let newRefreshToken = refreshToken;
  if (shouldRotate) {
    console.log('[refreshNewToken] Rotating refresh token');
    const rotationResult = await rotateRefreshToken(
      record.tokenId,
      record.userId.toString(),
      deviceFingerprint,
      userAgent,
      ipAddress,
    );
    if (rotationResult) {
      newRefreshToken = rotationResult.token;
      console.log('[refreshNewToken] Refresh token rotated successfully');
    }
    else {
      console.error('[refreshNewToken] Failed to rotate refresh token');
      throw new HttpError(500, 'ไม่สามารถ rotate refresh token ได้');
    }
  }
  else {
    console.log('[refreshNewToken] Token rotation not needed');
  }

  // สร้าง user object ที่ปลอดภัย (ไม่รวม password, refreshToken)
  console.log('[refreshNewToken] Creating safe user object');
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };

  console.log('[refreshNewToken] Token Rotation System refresh completed successfully');
  return { safeUser, newToken, newRefreshToken };
}

// ฟังก์ชันสำหรับอัพโหลดรูปโปรไฟล์
export async function uploadUserProfileImage(userId: string, file: string | Buffer, deps = defaultDeps) {
  if (!userId) throw new HttpError(400, 'ไม่พบ userId');
  if (!file) throw new HttpError(400, 'ไม่พบไฟล์รูปภาพ');

  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  try {
    // ลบรูปเก่าถ้ามี
    if (user.avatar) {
      const publicId = user.avatar.split('/').pop()?.split('.')[0];
      if (publicId && publicId.startsWith('profile_')) {
        await cloudinaryUpload.deleteFile(`profiles/${publicId}`);
      }
    }

    // อัพโหลดรูปใหม่
    const result = await cloudinaryUpload.uploadProfileImage(file, userId);

    // อัปเดต avatar ใน database
    user.avatar = result.secureUrl;
    await user.save();

    const safeUser = {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      avatar: user.avatar,
      cover: user.cover,
      isEmailVerified: user.isEmailVerified,
      moneyPoint: user.moneyPoint,
      goldPoint: user.goldPoint,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    // return ข้อมูลดิบ
    return { user: safeUser, imageUrl: result.secureUrl };
  }
  catch (error) {
    console.error('Error uploading profile image:', error);
    throw new HttpError(
      500,
      `ไม่สามารถอัพโหลดรูปโปรไฟล์ได้: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

// ฟังก์ชันสำหรับอัพโหลดรูปปก
export async function uploadUserCoverImage(userId: string, file: string | Buffer, deps = defaultDeps) {
  if (!userId) throw new HttpError(400, 'ไม่พบ userId');
  if (!file) throw new HttpError(400, 'ไม่พบไฟล์รูปภาพ');
  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');
  // ลบรูปเก่าถ้ามี
  if (user.cover) {
    const publicId = user.cover.split('/').pop()?.split('.')[0];
    if (publicId && publicId.startsWith('cover_')) {
      await cloudinaryUpload.deleteFile(`covers/${publicId}`);
    }
  }
  // อัพโหลดรูปใหม่
  const result = await cloudinaryUpload.uploadCoverImage(file, userId);
  // อัปเดต cover ใน database
  user.cover = result.secureUrl;
  await user.save();
  const safeUser = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    avatar: user.avatar,
    cover: user.cover,
    isEmailVerified: user.isEmailVerified,
    moneyPoint: user.moneyPoint,
    goldPoint: user.goldPoint,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
  return { user: safeUser, imageUrl: result.secureUrl };
}

// ฟังก์ชันสำหรับสร้าง signed URL สำหรับ upload จาก client
export async function generateUserUploadSignature(userId: string, type: 'profile' | 'cover', deps = defaultDeps) {
  if (!userId) throw new HttpError(400, 'ไม่พบ userId');
  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');
  const folder = type === 'profile' ? 'profiles' : 'covers';
  const publicId = type === 'profile' ? `profile_${userId}` : `cover_${userId}`;
  const transformation = type === 'profile'
    ? { width: 400, height: 400, crop: 'fill', gravity: 'face', quality: 'auto:good', format: 'webp' }
    : { width: 1200, height: 400, crop: 'fill', quality: 'auto:good', format: 'webp' };
  const signedData = cloudinaryUpload.generateSignedUploadUrl({
    folder,
    publicId,
    resourceType: 'image',
    transformation,
  });
  return signedData;
}

export async function getUserActivity(userId: string, deps = defaultDeps) {
  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  // สร้างข้อมูลกิจกรรมจากข้อมูลจริง
  const activities = [];

  // กิจกรรมการสมัครสมาชิก
  if (user.createdAt) {
    activities.push({
      id: 1,
      type: 'signup',
      title: 'สมัครสมาชิก',
      description: 'สร้างบัญชีใหม่',
      timestamp: user.createdAt.toISOString(),
      icon: 'mdi:account-plus',
      color: 'text-blue-500',
    });
  }

  // กิจกรรมการยืนยันอีเมล
  if (user.isEmailVerified && user.updatedAt) {
    activities.push({
      id: 2,
      type: 'email_verified',
      title: 'ยืนยันอีเมล',
      description: 'ยืนยันอีเมลเรียบร้อยแล้ว',
      timestamp: user.updatedAt.toISOString(),
      icon: 'mdi:email-check',
      color: 'text-green-500',
    });
  }

  // กิจกรรมการอัปเดตโปรไฟล์
  if (user.updatedAt && user.updatedAt > user.createdAt) {
    activities.push({
      id: 3,
      type: 'profile_update',
      title: 'อัปเดตข้อมูลโปรไฟล์',
      description: 'แก้ไขข้อมูลส่วนตัว',
      timestamp: user.updatedAt.toISOString(),
      icon: 'mdi:account-edit',
      color: 'text-purple-500',
    });
  }

  // กิจกรรมการเข้าสู่ระบบล่าสุด (จำลอง)
  activities.push({
    id: 4,
    type: 'login',
    title: 'เข้าสู่ระบบ',
    description: 'เข้าสู่ระบบล่าสุด',
    timestamp: new Date().toISOString(),
    icon: 'mdi:login',
    color: 'text-green-500',
  });

  // เรียงลำดับตามเวลา (ใหม่สุดก่อน)
  return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
}

export async function getUserStats(userId: string, deps = defaultDeps) {
  const user = await deps.User.findById(userId);
  if (!user) throw new HttpError(404, 'ไม่พบผู้ใช้');

  // คำนวณจำนวนวันที่เป็นสมาชิก
  const joinedDate = new Date(user.createdAt);
  const now = new Date();
  const joinedDays = Math.floor((now.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));

  // สร้างสถิติจากข้อมูลจริง
  const stats = {
    totalLogins: user.loginCount || 1, // ใช้ loginCount จาก user model หรือ default เป็น 1
    totalPoints: (user.moneyPoint || 0) + (user.goldPoint || 0),
    joinedDays: Math.max(1, joinedDays), // อย่างน้อย 1 วัน
    lastActive: user.lastLoginAt || user.updatedAt || user.createdAt,
    moneyPoints: user.moneyPoint || 0,
    goldPoints: user.goldPoint || 0,
    isEmailVerified: user.isEmailVerified || false,
    role: user.role || 'user',
    status: user.status || 'active',
  };

  return stats;
}
